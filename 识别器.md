## 医疗脱敏系统识别器统一分类



**识别器两类配置属性：**

1. 规则识别器层、NLP：开关参数（true、false）
2. 上下文识别器层：开关、上下文正面关键词（数组）、上下文负面关键词（数组）
3. 其中有些识别器用同一个字段名，比如MobilePhone + PHONE_NUMBER。虽然MobilePhone 是上下文识别器，PHONE_NUMBER是NLP层只有开关参数，但到时候需要开关参数不用上下文参数即可。

**过滤器：**按照分类进行，数组形式





### **🎯 A. 身份证件类识别器（Identity Documents）**

| 识别器                    | 层次           | 实体类型              | 功能描述                     | 状态    |
| ------------------------- | -------------- | --------------------- | ---------------------------- | ------- |
| **ChineseIDRecognizer**   | 规则识别器层   | ChineseIDRecognizer   | 18位中国身份证号码识别       | ✅使用中 |
| **CertificateRecognizer** | 上下文识别器层 | CertificateRecognizer | 护照、驾照、军官证等各类证件 | ✅使用中 |
| **BankCardRecognizer**         | 上下文识别器层 | BankCardRecognizer         | 银行卡号识别（16-19位）  | ✅使用中 |
| **MedicalInsuranceRecognizer** | 上下文识别器层 | MedicalInsuranceRecognizer | 医保社保卡号识别         | ✅使用中 |
| **Presidio CREDIT_CARD**       | Presidio内置层 | CREDIT_CARD       | 信用卡号识别（国际标准） | ✅使用中 |

---

### **📞 B. 通信联系类识别器（Communication）**

| 识别器                      | 层次           | 实体类型              | 功能描述           | 状态    |
| --------------------------- | -------------- | --------------------- | ------------------ | ------- |
| **MobilePhoneRecognizer**   | 上下文识别器层 | MobilePhoneRecognizer   | 11位中国手机号码   | ✅使用中 |
| **LandlinePhoneRecognizer** | 上下文识别器层 | LandlinePhoneRecognizer | 固定电话（带区号） | ✅使用中 |
| **Presidio PHONE_NUMBER**   | Presidio内置层 | PHONE_NUMBER   | 通用电话号码识别   | ✅使用中 |
| **WeChatRecognizer**       | 上下文识别器层 | WeChatRecognizer     | 微信号识别（6-20位） | ✅使用中 |
| **QQNumberRecognizer**     | 上下文识别器层 | QQNumberRecognizer     | QQ号码识别（5-11位） | ✅使用中 |
| **Presidio EMAIL_ADDRESS** | Presidio内置层 | EMAIL_ADDRESS | 邮箱地址识别         | ✅使用中 |




### **🏥 C. 医疗信息类识别器（Medical Information）**

| 识别器                        | 层次           | 实体类型                  | 功能描述                  | 状态    |
| ----------------------------- | -------------- | ------------------------- | ------------------------- | ------- |
| **MedicalNumberRecognizer**   | 上下文识别器层 | MedicalNumberRecognizer   | 病案号、检验号等医疗编号  | ✅使用中 |
| **StructuredFieldRecognizer** | 规则识别器层   | StructuredFieldRecognizer | "字段:值"格式的结构化信息 | ⚠️禁用 |
| **Presidio MEDICAL_LICENSE**  | Presidio内置层 | MEDICAL_LICENSE  | 医疗执照号码              | ✅使用中 |
| **OrganizationRecognizer** | 上下文识别器层 | OrganizationRecognizer | 医疗机构名称（医院、CRO等） | ✅使用中 |
| **SponsorRecognizer**      | 规则识别器层   | SponsorRecognizer              | 申办方名称信息              | ✅使用中 |
| **spaCy ORG**              | spaCy NLP层    | ORG                  | 通用机构名称识别            | ✅使用中 |

---

### **📍 D. 位置时间类识别器（Location & Time）**

| 识别器                        | 层次           | 实体类型                  | 功能描述                | 状态    |
| ----------------------------- | -------------- | ------------------------- | ----------------------- | ------- |
| **CompleteAddressRecognizer** | 规则识别器层   | CompleteAddressRecognizer | 完整地址（省市区+详细） | ✅使用中 |
| **GPSCoordinateRecognizer**   | 上下文识别器层 | GPSCoordinateRecognizer   | GPS坐标（经纬度）       | ✅使用中 |
| **LicensePlateRecognizer**    | 规则识别器层   | LicensePlateRecognizer    | 中国车牌号码            | ✅使用中 |
| **spaCy GPE**                 | spaCy NLP层    | GPE              | 地理政治实体            | ✅使用中 |
| **spaCy LOCATION**            | spaCy NLP层    | LOCATION         | 地理位置                | ✅使用中 |
| **Presidio IP_ADDRESS**       | Presidio内置层 | IP_ADDRESS       | IP地址识别              | ✅使用中 |
| **spaCy DATE**         | spaCy NLP层  | DATE → DATE_TIME | 日期识别（统一映射） | ✅使用中 |
| **spaCy TIME**         | spaCy NLP层  | TIME → DATE_TIME | 时间识别（统一映射） | ✅使用中 |

---

### **🔒 E. 隐私个人类识别器（Privacy & Personal）**

| 识别器                  | 层次         | 实体类型               | 功能描述              | 状态    |
| ----------------------- | ------------ | ---------------------- | --------------------- | ------- |
| **spaCy PERSON**        | spaCy NLP层  | PERSON                 | 人名识别（2-4字中文） | ✅使用中 |
| **GenderRecognizer**    | 上下文识别器层 | GenderRecognizer     | 性别信息识别（男、女、男性、女性） | ✅使用中 |
| **AgeRecognizer**       | 上下文识别器层 | AgeRecognizer        | 年龄信息识别（35岁、45周岁、3个月等） | ✅使用中 |
| **EthnicityRecognizer** | 规则识别器层 | EthnicityRecognizer    | 56个民族信息          | ✅使用中 |
| **spaCy NRP**           | spaCy NLP层  | NRP                    | 民族/国籍信息         | ✅使用中 |
| **PrivacyInfoRecognizer**    | 规则识别器层 | PrivacyInfoRecognizer    | 婚姻状况、宗教信仰等 | ✅使用中 |
| **EducationLevelRecognizer** | 规则识别器层 | EducationLevelRecognizer | 学历学位             | ✅使用中 |
| **Presidio IBAN_CODE**  | Presidio内置层 | IBAN_CODE | 国际银行账号                 | ✅使用中 |
| **URLRecognizer** | 规则识别器层   | URLRecognizer       | 严格URL识别（HTTP/FTP/邮件） | ⚠️本体定义 |



## **📊 重复识别器统一处理方案**

### **🔄 重复组合分类**

| 重复组合         | 涉及识别器                           | 统一方案                              |
| ---------------- | ------------------------------------ | ------------------------------------- |
| **电话识别组合** | MobilePhone + PHONE_NUMBER           | 按中国本土化程度分工：手机号→通用电话 |
| **时间日期组合** | ~~DateTimeRecognizer~~ + spaCy DATE/TIME | spaCy DATE/TIME统一映射为DATE_TIME，DateTimeRecognizer已废弃 |
| **银行卡组合**   | BankCardRecognizer + CREDIT_CARD     | 按地域性分工：中国银行卡→国际信用卡   |
| **民族识别组合** | ETHNICITY + NRP                      | 按专业性分工：中国民族→国际民族国籍   |
| **个人信息组合** | GenderRecognizer + AgeRecognizer + PERSON | 按信息类型分工：性别→年龄→姓名 |

---

## **🔄 时间日期识别架构说明（v5.0重大变更）**

### **架构变更**
- **废弃**：`DateTimeRecognizer`（自定义正则识别器）
- **采用**：spaCy内置的`DATE`和`TIME`实体识别
- **统一映射**：Presidio将spaCy的`DATE`和`TIME`统一映射为`DATE_TIME`类型

### **实际工作流程**
```
spaCy识别 → Presidio映射 → API输出
DATE     → DATE_TIME   → DATE_TIME
TIME     → DATE_TIME   → DATE_TIME
```

### **测试验证结果**
- ✅ spaCy原生识别：`"2024年1月15日"` → `DATE`，`"上午9:30"` → `TIME`
- ✅ Presidio统一映射：所有时间日期 → `DATE_TIME`
- ✅ API实际输出：统一为`DATE_TIME`类型
- ✅ 前端显示：`DATE_TIME` → `NLP模型检测器` → `日期时间`






## 过滤器：

| 过滤器名称                                       | 功能描述             | 词典规模 |
| ------------------------------------------------ | -------------------- | -------- |
| 医学检验项目过滤词典MEDICAL_TEST_ITEMS           | 排除误识别的检验项目 | 80+项目  |
| 医学术语过滤词典MEDICAL_TERMS                    | 排除医学专业术语     | 200+术语 |
| 药物名称过滤词典DRUG_NAMES                       | 排除药物名称误识别   | 50+药物  |
| 疾病名称过滤词典DISEASE_NAMES                    | 排除疾病名称误识别   | 30+疾病  |
| 神经学体征过滤词典NEUROLOGICAL_SIGNS             | 排除神经学体征误识别 | 20+体征  |
| 英文医学术语过滤词典ENGLISH_MEDICAL_TERMS        | 排除英文医学术语     | 50+术语  |
| 医学缩写过滤词典MEDICAL_ABBREVIATIONS            | 排除医学缩写误识别   | 30+缩写  |
| 其他名称过滤词典OTHER_NAMES                      | 其他专业术语过滤     | 变动     |
