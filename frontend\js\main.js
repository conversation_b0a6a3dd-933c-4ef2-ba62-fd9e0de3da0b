/**
 * 主入口文件
 * 协调各个模块的加载和初始化
 */

// 应用配置
const APP_CONFIG = {
    version: '2.0.0',
    name: '医疗数据脱敏系统',
    modules: [
        'dom-utils',
        'api-client',
        'data-formatters',
        'template-loader',
        'rules-config-manager',
        'batch-image-processing',
        'result-display',
        'event-handlers'
    ]
};

// 模块加载状态
const moduleStatus = {
    loaded: [],
    failed: [],
    total: APP_CONFIG.modules.length
};

// 检查模块是否加载完成
function checkModuleLoaded(moduleName) {
    const moduleMap = {
        'dom-utils': 'DOMUtils',
        'api-client': 'APIClient',
        'data-formatters': 'DataFormatters',
        'template-loader': 'TemplateLoader',
        'rules-config-manager': 'RulesConfigManager',
        'batch-image-processing': 'BatchImageProcessing',
        'result-display': 'ResultDisplay',
        'event-handlers': 'EventHandlers'
    };
    
    const globalName = moduleMap[moduleName];
    return window[globalName] !== undefined;
}

// 等待所有模块加载完成
function waitForModules() {
    return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
            const loadedCount = APP_CONFIG.modules.filter(checkModuleLoaded).length;
            
            console.log(`模块加载进度: ${loadedCount}/${APP_CONFIG.modules.length}`);
            
            if (loadedCount === APP_CONFIG.modules.length) {
                clearInterval(checkInterval);
                console.log('✅ 所有模块加载完成');
                resolve();
            }
        }, 100);
        
        // 超时处理
        setTimeout(() => {
            clearInterval(checkInterval);
            const failedModules = APP_CONFIG.modules.filter(module => !checkModuleLoaded(module));
            if (failedModules.length > 0) {
                console.error('❌ 模块加载超时，失败的模块:', failedModules);
                reject(new Error(`模块加载失败: ${failedModules.join(', ')}`));
            }
        }, 10000);
    });
}

// 应用初始化
async function initializeApplication() {
    console.log(`🚀 ${APP_CONFIG.name} v${APP_CONFIG.version} 开始初始化`);
    console.log('📦 等待模块加载...');
    
    try {
        // 等待所有模块加载完成
        await waitForModules();
        
        // 验证关键功能
        console.log('🔍 验证关键功能...');
        
        // 检查DOM工具
        if (!window.DOMUtils) {
            throw new Error('DOM工具模块未加载');
        }
        
        // 检查API客户端
        if (!window.APIClient) {
            throw new Error('API客户端模块未加载');
        }
        
        // 检查批量处理
        if (!window.BatchImageProcessing) {
            throw new Error('批量图片处理模块未加载');
        }
        
        // 检查结果显示
        if (!window.ResultDisplay) {
            throw new Error('结果显示模块未加载');
        }
        
        // 检查模板加载器
        if (!window.TemplateLoader) {
            throw new Error('模板加载器模块未加载');
        }

        // 检查规则配置管理器
        if (!window.RulesConfigManager) {
            throw new Error('规则配置管理器模块未加载');
        }

        // 检查事件处理
        if (!window.EventHandlers) {
            throw new Error('事件处理模块未加载');
        }

        console.log('✅ 所有模块验证通过');

        // 显示应用信息
        displayAppInfo();

        // 初始化规则配置管理器
        console.log('🔧 初始化规则配置管理器...');
        const rulesInitialized = await window.rulesConfigManager.initialize();
        if (!rulesInitialized) {
            console.warn('⚠️ 规则配置管理器初始化失败，但应用将继续运行');
        } else {
            console.log('✅ 规则配置管理器初始化完成');
        }

        // 执行健康检查
        await performHealthCheck();

        console.log('🎉 应用初始化完成，系统就绪！');
        
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        displayErrorMessage('应用初始化失败: ' + error.message);
    }
}

// 显示应用信息
function displayAppInfo() {
    console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    ${APP_CONFIG.name}                    ║
║                        版本 ${APP_CONFIG.version}                        ║
╠══════════════════════════════════════════════════════════════╣
║ 功能模块:                                                    ║
║ • DOM工具模块 - 安全的DOM操作和元素管理                      ║
║ • API客户端模块 - OCR和脱敏API通信                          ║
║ • 数据格式化模块 - 数据转换和格式化                          ║
║ • 批量图片处理模块 - 批量上传和处理管理                      ║
║ • 结果显示模块 - 脱敏结果展示和标签页管理                    ║
║ • 事件处理模块 - 用户交互和事件监听                          ║
╚══════════════════════════════════════════════════════════════╝
    `);
}

// 执行健康检查
async function performHealthCheck() {
    console.log('🏥 执行系统健康检查...');
    
    try {
        // API连接检查
        const healthResult = await window.APIClient.healthCheck();
        if (healthResult.success) {
            console.log('✅ API服务连接正常');
        } else {
            console.warn('⚠️ API服务连接异常，但不影响基本功能');
        }
        
        // DOM元素检查
        const criticalElements = [
            'text-input',
            'file-input', 
            'results-section',
            'upload-area'
        ];
        
        const missingElements = criticalElements.filter(id => !document.getElementById(id));
        if (missingElements.length === 0) {
            console.log('✅ 关键DOM元素检查通过');
        } else {
            console.warn('⚠️ 缺失关键DOM元素:', missingElements);
        }
        
        console.log('✅ 健康检查完成');
        
    } catch (error) {
        console.warn('⚠️ 健康检查过程中出现问题:', error);
    }
}

// 显示错误消息
function displayErrorMessage(message) {
    // 尝试使用UI工具显示错误
    if (window.UIUtils && window.UIUtils.showError) {
        window.UIUtils.showError(message);
    } else {
        // 降级到alert
        alert('系统错误: ' + message);
    }
}

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局JavaScript错误:', event.error);
    console.error('错误位置:', event.filename, ':', event.lineno);
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 导出主应用功能
window.MedicalDeidentificationApp = {
    config: APP_CONFIG,
    initialize: initializeApplication,
    healthCheck: performHealthCheck
};

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM内容加载完成');
    
    // 延迟一点时间确保所有模块脚本都已加载
    setTimeout(initializeApplication, 100);
});

console.log('✓ 主应用模块已加载');
