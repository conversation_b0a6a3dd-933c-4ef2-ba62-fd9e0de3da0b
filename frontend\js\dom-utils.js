/**
 * DOM工具模块
 * 提供安全的DOM元素访问和操作功能
 */

// 安全的DOM元素获取函数
function safeGetElement(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`⚠️ 未找到元素: #${id}`);
    }
    return element;
}

function safeQuerySelectorAll(selector) {
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) {
        console.warn(`⚠️ 未找到元素: ${selector}`);
    }
    return elements;
}

// DOM元素集合
const elements = {
    tabBtns: safeQuerySelectorAll('.tab-btn'),
    tabContents: safeQuerySelectorAll('.tab-content'),
    textInput: safeGetElement('text-input'),
    fileInput: safeGetElement('file-input'),
    uploadArea: safeGetElement('upload-area'),
    imagePreview: safeGetElement('image-preview'),
    previewImg: safeGetElement('preview-img'),
    // 批量图片相关元素
    batchImagesContainer: safeGetElement('batch-images-container'),
    imagesGrid: safeGetElement('images-grid'),
    imageCount: safeGetElement('image-count'),
    clearAllImagesBtn: safeGetElement('clear-all-images-btn'),
    processAllImagesBtn: safeGetElement('process-all-images-btn'),
    reprocessAllImagesBtn: safeGetElement('reprocess-all-images-btn'),
    // 结果展示相关元素
    loading: safeGetElement('loading'),
    loadingText: safeGetElement('loading-text'),
    resultsSection: safeGetElement('results-section'),
    deidentifiedText: safeGetElement('deidentified-text'),
    highlightedText: safeGetElement('highlighted-text'),
    sensitiveInfoList: safeGetElement('sensitive-info-list'),
    filteredInfoList: safeGetElement('filtered-info-list'),
    detectedCount: safeGetElement('detected-count'),
    filteredCount: safeGetElement('filtered-count'),
    processingTime: safeGetElement('processing-time'),
    entityCount: safeGetElement('entity-count'),
    errorMessage: safeGetElement('error-message'),
    errorText: safeGetElement('error-text'),
    // 结果标签页相关元素
    resultTabBtns: safeQuerySelectorAll('.result-tab-btn'),
    resultTabContents: safeQuerySelectorAll('.result-tab-content')
};

// 显示/隐藏元素
function showElement(element) {
    if (element) {
        element.style.display = 'block';
    }
}

function hideElement(element) {
    if (element) {
        element.style.display = 'none';
    }
}

// 添加/移除CSS类
function addClass(element, className) {
    if (element) {
        element.classList.add(className);
    }
}

function removeClass(element, className) {
    if (element) {
        element.classList.remove(className);
    }
}

function toggleClass(element, className, condition) {
    if (element) {
        element.classList.toggle(className, condition);
    }
}

// 设置元素内容
function setTextContent(element, text) {
    if (element) {
        element.textContent = text;
    }
}

function setInnerHTML(element, html) {
    if (element) {
        element.innerHTML = html;
    }
}

// 滚动到元素
function scrollToElement(element, behavior = 'smooth') {
    if (element) {
        element.scrollIntoView({ behavior });
    }
}

// 检查元素是否存在
function elementExists(element) {
    return element !== null && element !== undefined;
}

// 等待DOM加载完成
function onDOMReady(callback) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
    } else {
        callback();
    }
}

// 创建元素
function createElement(tag, className = '', innerHTML = '') {
    const element = document.createElement(tag);
    if (className) {
        element.className = className;
    }
    if (innerHTML) {
        element.innerHTML = innerHTML;
    }
    return element;
}

// 安全的事件监听器绑定
function safeAddEventListener(element, event, handler, options = {}) {
    if (element && typeof handler === 'function') {
        element.addEventListener(event, handler, options);
        return true;
    }
    console.warn(`无法绑定事件监听器: 元素或处理函数无效`);
    return false;
}

// 移除所有子元素
function clearChildren(element) {
    if (element) {
        element.innerHTML = '';
    }
}

// 导出所有功能
window.DOMUtils = {
    safeGetElement,
    safeQuerySelectorAll,
    elements,
    showElement,
    hideElement,
    addClass,
    removeClass,
    toggleClass,
    setTextContent,
    setInnerHTML,
    scrollToElement,
    elementExists,
    onDOMReady,
    createElement,
    safeAddEventListener,
    clearChildren
};

console.log('✓ DOM工具模块已加载');
