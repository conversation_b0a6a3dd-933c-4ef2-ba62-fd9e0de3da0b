"""
医疗数据脱敏系统的工具函数

本模块包含用于模式创建、结果格式化和医疗脱敏系统其他支持功能的辅助函数。
"""

from typing import List, Dict, Any, Optional, Tuple
from presidio_analyzer import Pattern, RecognizerResult
import re
import hashlib
import time
from functools import wraps


def create_dictionary_patterns(words: List[str], score: float = 0.9) -> List[Pattern]:
    """
    从词典词汇列表创建Presidio模式

    参数:
        words (List[str]): 要创建模式的词汇列表
        score (float): 模式的置信度分数

    返回:
        List[Pattern]: Pattern对象列表
    """
    patterns = []
    for i, word in enumerate(words):
        pattern = Pattern(
            name=f"dict_pattern_{i}",
            regex=rf'\b{re.escape(word)}\b',
            score=score
        )
        patterns.append(pattern)
    return patterns


def format_detection_result(result: RecognizerResult, text: str) -> Dict[str, Any]:
    """
    将Presidio识别结果格式化为标准化输出格式

    参数:
        result (RecognizerResult): Presidio识别结果
        text (str): 原始文本

    返回:
        Dict[str, Any]: 标准化结果格式
    """
    return {
        "text": text[result.start:result.end],
        "entity_type": result.entity_type,
        "start": result.start,
        "end": result.end,
        "confidence": result.score
    }


def format_detection_results(results: List[RecognizerResult], text: str) -> List[Dict[str, Any]]:
    """
    将多个Presidio结果格式化为标准化输出格式

    参数:
        results (List[RecognizerResult]): Presidio识别结果列表
        text (str): 原始文本

    返回:
        List[Dict[str, Any]]: 标准化结果列表
    """
    return [format_detection_result(result, text) for result in results]


def filter_results_by_confidence(results: List[Dict[str, Any]], min_confidence: float = 0.8) -> List[Dict[str, Any]]:
    """
    按最小置信度阈值过滤检测结果

    参数:
        results (List[Dict[str, Any]]): 检测结果
        min_confidence (float): 最小置信度阈值

    返回:
        List[Dict[str, Any]]: 过滤后的结果
    """
    return [result for result in results if result['confidence'] >= min_confidence]


def filter_results_by_entity_types(results: List[Dict[str, Any]], entity_types: List[str]) -> List[Dict[str, Any]]:
    """
    按实体类型过滤检测结果

    参数:
        results (List[Dict[str, Any]]): 检测结果
        entity_types (List[str]): 要包含的实体类型列表

    返回:
        List[Dict[str, Any]]: 过滤后的结果
    """
    return [result for result in results if result['entity_type'] in entity_types]


def group_results_by_entity_type(results: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    按实体类型分组检测结果

    参数:
        results (List[Dict[str, Any]]): 检测结果

    返回:
        Dict[str, List[Dict[str, Any]]]: 按实体类型分组的结果
    """
    groups = {}
    for result in results:
        entity_type = result['entity_type']
        if entity_type not in groups:
            groups[entity_type] = []
        groups[entity_type].append(result)
    return groups


def remove_overlapping_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Remove overlapping detection results, keeping the one with higher confidence
    
    Args:
        results (List[Dict[str, Any]]): Detection results
        
    Returns:
        List[Dict[str, Any]]: Results with overlaps removed
    """
    if not results:
        return results
    
    # Sort by start position and confidence (descending)
    sorted_results = sorted(results, key=lambda x: (x['start'], -x['confidence']))
    
    filtered_results = []
    for current in sorted_results:
        # Check if current result overlaps with any already accepted result
        overlaps = False
        for accepted in filtered_results:
            if (current['start'] < accepted['end'] and current['end'] > accepted['start']):
                overlaps = True
                break
        
        if not overlaps:
            filtered_results.append(current)
    
    return filtered_results


def calculate_text_hash(text: str) -> str:
    """
    Calculate MD5 hash of text for caching purposes
    
    Args:
        text (str): Input text
        
    Returns:
        str: MD5 hash string
    """
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def validate_text_input(text: str) -> Tuple[bool, Optional[str]]:
    """
    Validate input text for processing
    
    Args:
        text (str): Input text to validate
        
    Returns:
        Tuple[bool, Optional[str]]: (is_valid, error_message)
    """
    if not isinstance(text, str):
        return False, "Input must be a string"
    
    if not text.strip():
        return False, "Input text cannot be empty"
    
    if len(text) > 100000:  # 100KB limit
        return False, "Input text too long (max 100,000 characters)"
    
    return True, None


def timing_decorator(func):
    """
    Decorator to measure function execution time
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # Add timing info to result if it's a dict
        if isinstance(result, dict):
            result['_execution_time'] = execution_time
        elif isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
            # Add timing info as metadata
            result.append({'_metadata': {'execution_time': execution_time}})
        
        return result
    return wrapper


def create_summary_statistics(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create summary statistics for detection results
    
    Args:
        results (List[Dict[str, Any]]): Detection results
        
    Returns:
        Dict[str, Any]: Summary statistics
    """
    if not results:
        return {
            "total_entities": 0,
            "entity_types": {},
            "confidence_stats": {},
            "text_coverage": 0.0
        }
    
    # Count by entity type
    entity_counts = {}
    confidences = []
    total_chars = 0
    
    for result in results:
        entity_type = result['entity_type']
        entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
        confidences.append(result['confidence'])
        total_chars += len(result['text'])
    
    # Calculate confidence statistics
    confidence_stats = {
        "min": min(confidences),
        "max": max(confidences),
        "avg": sum(confidences) / len(confidences),
        "count_high": len([c for c in confidences if c >= 0.9]),
        "count_medium": len([c for c in confidences if 0.7 <= c < 0.9]),
        "count_low": len([c for c in confidences if c < 0.7])
    }
    
    return {
        "total_entities": len(results),
        "entity_types": entity_counts,
        "confidence_stats": confidence_stats,
        "total_sensitive_chars": total_chars
    }


def anonymize_text_simple(text: str, results: List[Dict[str, Any]], replacement: str = "***") -> str:
    """
    Simple text anonymization by replacing detected entities
    
    Args:
        text (str): Original text
        results (List[Dict[str, Any]]): Detection results
        replacement (str): Replacement string
        
    Returns:
        str: Anonymized text
    """
    if not results:
        return text
    
    # Sort results by start position in reverse order to avoid index shifting
    sorted_results = sorted(results, key=lambda x: x['start'], reverse=True)
    
    anonymized_text = text
    for result in sorted_results:
        start, end = result['start'], result['end']
        anonymized_text = anonymized_text[:start] + replacement + anonymized_text[end:]
    
    return anonymized_text


def get_entity_context(text: str, start: int, end: int, context_size: int = 20) -> Dict[str, str]:
    """
    Get context around a detected entity
    
    Args:
        text (str): Original text
        start (int): Entity start position
        end (int): Entity end position
        context_size (int): Number of characters to include on each side
        
    Returns:
        Dict[str, str]: Context information
    """
    text_len = len(text)
    context_start = max(0, start - context_size)
    context_end = min(text_len, end + context_size)
    
    return {
        "before": text[context_start:start],
        "entity": text[start:end],
        "after": text[end:context_end],
        "full_context": text[context_start:context_end]
    }
