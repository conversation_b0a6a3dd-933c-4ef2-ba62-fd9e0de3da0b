/**
 * 模板加载器模块
 * 负责加载HTML组件模板并进行数据绑定
 */

class TemplateLoader {
    constructor() {
        this.templateCache = new Map();
        this.dataCache = new Map();
        this.initializeInlineTemplates();
        this.initializeInlineData();
    }

    /**
     * 初始化内联模板
     */
    initializeInlineTemplates() {
        // 规则项模板
        this.templateCache.set('rule-item.html', `
<!-- 规则项组件模板 -->
<div class="rule-item" data-rule-id="{{ruleId}}">
    <div class="rule-header">
        <div class="rule-info">
            <span class="rule-name">{{name}}</span>
            <span class="rule-type">{{type}}</span>
        </div>
        <label class="switch">
            <input type="checkbox" {{checked}} data-rule="{{ruleId}}">
            <span class="slider"></span>
        </label>
    </div>
    <div class="rule-description">{{description}}</div>
    {{keywordTags}}
</div>`);

        // 规则分类模板
        this.templateCache.set('rule-category.html', `
<!-- 规则分类组件模板 -->
<div class="rule-category">
    <div class="category-header" data-category="{{categoryId}}">
        <div class="category-title">
            <i class="fas {{icon}}"></i>
            <span>{{title}}</span>
            <span class="rule-count">({{ruleCount}}个识别器)</span>
        </div>
        <div class="category-controls">
            <button class="expand-btn" data-category="{{categoryId}}">
                <i class="fas {{chevronIcon}}"></i>
            </button>
        </div>
    </div>
    <div class="category-content {{collapsed}}" data-category="{{categoryId}}">
        {{ruleItems}}
    </div>
</div>`);
    }

    /**
     * 初始化内联数据
     */
    initializeInlineData() {
        const rulesConfig = {
            "categories": {
                "identity": {
                    "id": "identity",
                    "title": "核心身份识别器组",
                    "icon": "fa-id-card",
                    "enabled": true,
                    "collapsed": true,
                    "rules": [
                        {
                            "id": "chinese_id",
                            "name": "身份证号识别器",
                            "type": "ChineseIDRecognizer",
                            "description": "基于正则表达式识别18位身份证号码",
                            "enabled": true
                        },
                        {
                            "id": "mobile_phone",
                            "name": "手机号识别器",
                            "type": "MobilePhoneRecognizer",
                            "description": "识别11位手机号码格式",
                            "enabled": true
                        },
                        {
                            "id": "passport",
                            "name": "护照号识别器",
                            "type": "PassportRecognizer",
                            "description": "识别护照号码格式",
                            "enabled": true
                        },
                        {
                            "id": "bank_card",
                            "name": "银行卡识别器",
                            "type": "BankCardRecognizer",
                            "description": "识别银行卡号格式",
                            "enabled": true
                        },
                        {
                            "id": "landline_phone",
                            "name": "固话识别器",
                            "type": "LandlinePhoneRecognizer",
                            "description": "识别固定电话号码",
                            "enabled": true
                        },
                        {
                            "id": "biometric",
                            "name": "生物特征识别器",
                            "type": "BiometricRecognizer",
                            "description": "识别生物特征相关信息",
                            "enabled": true,
                            "keywords": ["指纹", "声纹", "面部特征", "虹膜", "掌纹", "DNA", "基因", "生物特征", "人脸识别", "指纹识别", "声纹识别", "虹膜识别", "面部识别"]
                        }
                    ]
                },
                "medical": {
                    "id": "medical",
                    "title": "医疗专业识别器组",
                    "icon": "fa-heartbeat",
                    "enabled": true,
                    "collapsed": true,
                    "rules": [
                        {
                            "id": "medical_record",
                            "name": "病案号识别器",
                            "type": "MedicalRecordRecognizer",
                            "description": "识别医疗病案号码",
                            "enabled": true
                        },
                        {
                            "id": "medical_insurance",
                            "name": "医保卡识别器",
                            "type": "MedicalInsuranceRecognizer",
                            "description": "识别医保卡号码",
                            "enabled": true
                        },
                        {
                            "id": "medical_position",
                            "name": "医疗职位识别器",
                            "type": "MedicalPositionRecognizer",
                            "description": "识别医疗相关职位信息",
                            "enabled": true,
                            "keywords": ["主任医师", "副主任医师", "主治医师", "住院医师", "实习医师", "护士长", "主管护师", "护师", "护士", "实习护士", "药师", "主管药师", "副主任药师", "主任药师", "技师", "主管技师", "副主任技师", "主任技师", "医师", "医生", "大夫", "主任", "副主任", "科主任"]
                        },
                        {
                            "id": "medical_number",
                            "name": "医疗编号识别器",
                            "type": "MedicalNumberRecognizer",
                            "description": "增强的医疗编号识别",
                            "enabled": true
                        }
                    ]
                }
            }
        };

        this.dataCache.set('rules-config.json', rulesConfig);
    }

    /**
     * 加载HTML模板文件
     * @param {string} templatePath 模板文件路径
     * @returns {Promise<string>} 模板内容
     */
    async loadTemplate(templatePath) {
        if (this.templateCache.has(templatePath)) {
            return this.templateCache.get(templatePath);
        }

        console.warn(`Template ${templatePath} not found in cache`);
        return '';
    }

    /**
     * 加载JSON数据文件
     * @param {string} dataPath 数据文件路径
     * @returns {Promise<Object>} 数据对象
     */
    async loadData(dataPath) {
        if (this.dataCache.has(dataPath)) {
            return this.dataCache.get(dataPath);
        }

        console.warn(`Data ${dataPath} not found in cache`);
        return {};
    }

    /**
     * 渲染模板
     * @param {string} template 模板字符串
     * @param {Object} data 数据对象
     * @returns {string} 渲染后的HTML
     */
    renderTemplate(template, data) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return data[key] !== undefined ? data[key] : '';
        });
    }

    /**
     * 生成关键词标签HTML
     * @param {Array} keywords 关键词数组
     * @returns {string} 关键词标签HTML
     */
    generateKeywordTags(keywords = []) {
        if (!keywords || keywords.length === 0) {
            return '';
        }

        const keywordHtml = keywords.map(keyword => 
            `<span class="keyword-tag">${keyword}</span>`
        ).join('');

        return keywordHtml;
    }

    /**
     * 生成规则项HTML
     * @param {Object} rule 规则对象
     * @param {string} template 规则项模板
     * @returns {string} 规则项HTML
     */
    async generateRuleItem(rule, template) {
        const keywordTags = rule.keywords ? 
            `<div class="keywords-tags">
                ${this.generateKeywordTags(rule.keywords)}
                <button class="add-tag-btn" title="添加关键词" data-category="${rule.id}">
                    <i class="fas fa-plus"></i>
                </button>
            </div>` : '';

        const ruleData = {
            ruleId: rule.id,
            name: rule.name,
            type: rule.type,
            description: rule.description,
            checked: rule.enabled ? 'checked' : '',
            keywordTags: keywordTags
        };

        return this.renderTemplate(template, ruleData);
    }

    /**
     * 生成规则分类HTML
     * @param {Object} category 分类对象
     * @param {string} categoryTemplate 分类模板
     * @param {string} ruleTemplate 规则项模板
     * @returns {string} 分类HTML
     */
    async generateRuleCategory(category, categoryTemplate, ruleTemplate) {
        // 生成所有规则项
        const ruleItemsPromises = category.rules.map(rule => 
            this.generateRuleItem(rule, ruleTemplate)
        );
        const ruleItems = await Promise.all(ruleItemsPromises);

        // 检查是否所有规则都启用
        const allEnabled = category.rules.every(rule => rule.enabled);

        const categoryData = {
            categoryId: category.id,
            title: category.title,
            icon: category.icon,
            ruleCount: category.rules.length,
            allChecked: allEnabled ? 'checked' : '',
            collapsed: category.collapsed ? 'collapsed' : '',
            chevronIcon: category.collapsed ? 'fa-chevron-right' : 'fa-chevron-down',
            ruleItems: ruleItems.join('')
        };

        return this.renderTemplate(categoryTemplate, categoryData);
    }

    /**
     * 生成完整的规则配置面板
     * @returns {Promise<string>} 规则配置面板HTML
     */
    async generateRulesConfigPanel() {
        try {
            console.log('开始生成规则配置面板...');

            // 加载模板和数据
            const [categoryTemplate, ruleTemplate, rulesData] = await Promise.all([
                this.loadTemplate('rule-category.html'),
                this.loadTemplate('rule-item.html'),
                this.loadData('rules-config.json')
            ]);

            if (!rulesData.categories) {
                console.error('规则配置数据格式错误');
                return '';
            }

            // 生成所有分类
            const categoryPromises = Object.values(rulesData.categories).map(category =>
                this.generateRuleCategory(category, categoryTemplate, ruleTemplate)
            );

            const categories = await Promise.all(categoryPromises);
            
            console.log(`✓ 成功生成 ${categories.length} 个规则分类`);
            return categories.join('');

        } catch (error) {
            console.error('生成规则配置面板失败:', error);
            return '';
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.templateCache.clear();
        this.dataCache.clear();
        console.log('模板和数据缓存已清除');
    }
}

// 创建全局实例
const templateLoader = new TemplateLoader();

// 导出到全局
window.TemplateLoader = TemplateLoader;
window.templateLoader = templateLoader;

console.log('✓ 模板加载器模块已加载');
