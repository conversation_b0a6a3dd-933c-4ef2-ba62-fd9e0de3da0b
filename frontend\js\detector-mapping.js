/**
 * 检测器映射模块
 * 根据实体类型推断使用的检测器类型
 */

// 检测器类型定义
const DETECTOR_TYPES = {
    REGEX: {
        name: '正则表达式检测器',
        description: '基于模式匹配的精确识别',
        icon: 'fas fa-search',
        color: '#e74c3c',
        bgColor: '#fdf2f2'
    },
    DICTIONARY: {
        name: '词典检测器', 
        description: '基于预定义词典的匹配',
        icon: 'fas fa-book',
        color: '#3498db',
        bgColor: '#f0f8ff'
    },
    CONTEXT: {
        name: '上下文检测器',
        description: '结合上下文信息的智能识别',
        icon: 'fas fa-brain',
        color: '#f39c12',
        bgColor: '#fffbf0'
    },
    NLP_MODEL: {
        name: 'NLP模型检测器',
        description: '基于spaCy自然语言处理模型的智能识别',
        icon: 'fas fa-robot',
        color: '#9b59b6',
        bgColor: '#f8f4ff'
    },
    HYBRID: {
        name: '混合检测器',
        description: '多种技术结合的综合识别',
        icon: 'fas fa-cogs',
        color: '#2ecc71',
        bgColor: '#f0fff4'
    }
};

// 实体类型到检测器的映射
const ENTITY_TO_DETECTOR_MAPPING = {
    // 规则识别器（使用识别器类名）
    'ChineseIDRecognizer': 'REGEX',
    'LicensePlateRecognizer': 'REGEX',
    'EthnicityRecognizer': 'DICTIONARY',
    'EducationLevelRecognizer': 'DICTIONARY',
    'PrivacyInfoRecognizer': 'REGEX',
    'CompleteAddressRecognizer': 'REGEX',
    'URLRecognizer': 'REGEX',
    'StructuredFieldRecognizer': 'CONTEXT',
    'SponsorRecognizer': 'REGEX',

    // 上下文识别器（使用识别器类名）
    'MobilePhoneRecognizer': 'CONTEXT',
    'LandlinePhoneRecognizer': 'CONTEXT',
    'MedicalInsuranceRecognizer': 'CONTEXT',
    'BankCardRecognizer': 'CONTEXT',
    'WeChatRecognizer': 'CONTEXT',
    'CertificateRecognizer': 'CONTEXT',
    'QQNumberRecognizer': 'CONTEXT',
    'OrganizationRecognizer': 'CONTEXT',
    'GPSCoordinateRecognizer': 'CONTEXT',
    'MedicalNumberRecognizer': 'CONTEXT',
    'GenderRecognizer': 'CONTEXT',
    'AgeRecognizer': 'CONTEXT',

    // spaCy和Presidio实体类型
    'PERSON': 'NLP_MODEL',
    'LOCATION': 'NLP_MODEL',
    'NRP': 'NLP_MODEL',
    'DATE_TIME': 'NLP_MODEL',
    'EMAIL_ADDRESS': 'REGEX',
    'IP_ADDRESS': 'REGEX',
    'CREDIT_CARD': 'REGEX',
    'IBAN_CODE': 'REGEX',

    // 向后兼容的旧实体类型映射
    'CHINESE_ID': 'REGEX',
    'LICENSE_PLATE': 'REGEX',
    'ETHNICITY': 'DICTIONARY',
    'EDUCATION_LEVEL': 'DICTIONARY',
    'PRIVACY_INFO': 'REGEX',
    'COMPLETE_ADDRESS': 'REGEX',
    'URL': 'REGEX',
    'STRUCTURED_FIELD': 'CONTEXT',
    'SPONSOR': 'REGEX',
    'MOBILE_PHONE': 'CONTEXT',
    'LANDLINE_PHONE': 'CONTEXT',
    'MEDICAL_INSURANCE': 'CONTEXT',
    'BANK_CARD': 'CONTEXT',
    'WECHAT_ID': 'CONTEXT',
    'CERTIFICATE': 'CONTEXT',
    'QQ_NUMBER': 'CONTEXT',
    'MEDICAL_ORGANIZATION': 'CONTEXT',
    'GPS_COORDINATE': 'CONTEXT',
    'MEDICAL_NUMBER': 'CONTEXT',
    'GENDER': 'CONTEXT',
    'AGE': 'CONTEXT',
    
    // NLP模型检测器
    'PERSON': 'NLP_MODEL',
    'ORGANIZATION': 'NLP_MODEL',
    'MEDICAL_ORGANIZATION': 'NLP_MODEL',
    'LOCATION': 'NLP_MODEL',
    'GPE': 'NLP_MODEL',
    'DATE_TIME': 'NLP_MODEL',
    
    // 混合检测器
    'COMPLETE_ADDRESS': 'HYBRID',
    'COMMUNICATION_CONTENT': 'HYBRID',
    'PRIVACY_INFO': 'HYBRID'
};

/**
 * 根据实体类型获取检测器信息
 * @param {string} entityType - 实体类型
 * @returns {Object} 检测器信息
 */
function getDetectorInfo(entityType) {
    const detectorType = ENTITY_TO_DETECTOR_MAPPING[entityType] || 'REGEX';
    return {
        type: detectorType,
        ...DETECTOR_TYPES[detectorType]
    };
}

/**
 * 按检测器类型分组敏感信息
 * @param {Array} sensitiveWords - 敏感信息数组
 * @returns {Object} 按检测器类型分组的结果
 */
function groupByDetector(sensitiveWords) {
    const groups = {};
    
    sensitiveWords.forEach(entity => {
        const detectorInfo = getDetectorInfo(entity.entity_type);
        const detectorType = detectorInfo.type;
        
        if (!groups[detectorType]) {
            groups[detectorType] = {
                detector: detectorInfo,
                entities: []
            };
        }
        
        groups[detectorType].entities.push({
            ...entity,
            detectorInfo
        });
    });
    
    return groups;
}

/**
 * 获取所有检测器类型
 * @returns {Object} 所有检测器类型定义
 */
function getAllDetectorTypes() {
    return DETECTOR_TYPES;
}

// 导出模块
window.DetectorMapping = {
    DETECTOR_TYPES,
    ENTITY_TO_DETECTOR_MAPPING,
    getDetectorInfo,
    groupByDetector,
    getAllDetectorTypes
};

console.log('✓ 检测器映射模块已加载');
