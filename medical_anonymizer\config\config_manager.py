"""
动态配置管理器

本模块提供高级的配置管理功能，包括：
1. 配置的动态加载和热更新
2. 配置变更的监听和通知
3. 配置的备份和恢复
4. 配置的验证和错误处理
"""

import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
import logging

from .config_mapper import ConfigMapper, create_config_mapper
from medical_anonymizer.recognizers.patterns import UNIFIED_RECOGNIZER_CONFIGS

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file_path: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file_path: 配置文件路径
        """
        self.mapper = create_config_mapper(config_file_path)
        self.config_file_path = self.mapper.config_file_path
        self.backup_dir = self.config_file_path.parent / "backups"
        self.backup_dir.mkdir(exist_ok=True)

        # 当前配置的备份
        self.current_config_backup = None
    

    
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """
        创建配置备份
        
        Args:
            backup_name: 备份名称，如果为None则使用时间戳
            
        Returns:
            备份文件路径
        """
        if backup_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"config_backup_{timestamp}.json"
        
        backup_path = self.backup_dir / backup_name
        
        try:
            shutil.copy2(self.config_file_path, backup_path)
            logger.info(f"创建配置备份: {backup_path}")
            return str(backup_path)
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
            raise
    
    def restore_backup(self, backup_path: str) -> bool:
        """
        从备份恢复配置
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否成功恢复
        """
        backup_file = Path(backup_path)
        
        if not backup_file.exists():
            logger.error(f"备份文件不存在: {backup_path}")
            return False
        
        try:
            # 创建当前配置的备份
            current_backup = self.create_backup("before_restore")
            
            # 恢复备份
            shutil.copy2(backup_file, self.config_file_path)
            
            # 重新加载配置
            if self.reload_config():
                logger.info(f"成功从备份恢复配置: {backup_path}")
                return True
            else:
                # 恢复失败，回滚到之前的配置
                shutil.copy2(current_backup, self.config_file_path)
                self.reload_config()
                logger.error("配置恢复失败，已回滚到之前的配置")
                return False
                
        except Exception as e:
            logger.error(f"恢复配置备份失败: {e}")
            return False
    

    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        return self.mapper.load_config()
    
    def reload_config(self) -> bool:
        """
        重新加载配置并应用
        
        Returns:
            是否成功重新加载
        """
        try:
            # 备份当前配置
            self.current_config_backup = dict(UNIFIED_RECOGNIZER_CONFIGS)
            
            # 重新加载配置
            success = self.mapper.reload_config()
            
            if success:
                logger.info("配置重新加载成功")
            else:
                # 恢复之前的配置
                if self.current_config_backup:
                    UNIFIED_RECOGNIZER_CONFIGS.clear()
                    UNIFIED_RECOGNIZER_CONFIGS.update(self.current_config_backup)
                logger.error("配置重新加载失败，已恢复之前的配置")
            
            return success
            
        except Exception as e:
            logger.error(f"重新加载配置时发生异常: {e}")
            # 恢复之前的配置
            if self.current_config_backup:
                UNIFIED_RECOGNIZER_CONFIGS.clear()
                UNIFIED_RECOGNIZER_CONFIGS.update(self.current_config_backup)
            return False
    
    def update_recognizer_config(self, recognizer_name: str, updates: Dict[str, Any]) -> bool:
        """
        更新单个识别器配置
        
        Args:
            recognizer_name: 识别器名称
            updates: 要更新的配置项
            
        Returns:
            是否成功更新
        """
        try:
            # 加载当前配置
            config = self.load_config()
            
            # 查找识别器配置
            recognizer_config = None
            category_name = None
            
            for cat_name, cat_config in config.get("recognizers", {}).items():
                if recognizer_name in cat_config.get("recognizers", {}):
                    recognizer_config = cat_config["recognizers"][recognizer_name]
                    category_name = cat_name
                    break
            
            if recognizer_config is None:
                logger.error(f"未找到识别器配置: {recognizer_name}")
                return False
            
            # 创建备份
            backup_path = self.create_backup(f"before_update_{recognizer_name}")
            
            # 更新配置
            recognizer_config.update(updates)
            
            # 保存配置文件
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 重新加载配置
            if self.reload_config():
                logger.info(f"成功更新识别器配置: {recognizer_name}")
                return True
            else:
                # 更新失败，恢复备份
                self.restore_backup(backup_path)
                logger.error(f"更新识别器配置失败，已恢复备份: {recognizer_name}")
                return False
                
        except Exception as e:
            logger.error(f"更新识别器配置时发生异常: {e}")
            return False
    
    def enable_recognizer(self, recognizer_name: str) -> bool:
        """
        启用识别器
        
        Args:
            recognizer_name: 识别器名称
            
        Returns:
            是否成功启用
        """
        return self.update_recognizer_config(recognizer_name, {"enabled": True})
    
    def disable_recognizer(self, recognizer_name: str) -> bool:
        """
        禁用识别器
        
        Args:
            recognizer_name: 识别器名称
            
        Returns:
            是否成功禁用
        """
        return self.update_recognizer_config(recognizer_name, {"enabled": False})
    
    def get_recognizer_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有识别器的状态信息
        
        Returns:
            识别器状态字典
        """
        status = {}
        
        try:
            config = self.load_config()
            recognizers = config.get("recognizers", {})
            
            for category_name, category_config in recognizers.items():
                category_recognizers = category_config.get("recognizers", {})
                
                for recognizer_name, recognizer_config in category_recognizers.items():
                    status[recognizer_name] = {
                        "name": recognizer_name,
                        "displayName": recognizer_config.get("displayName", ""),
                        "description": recognizer_config.get("description", ""),
                        "category": category_name,
                        "enabled": recognizer_config.get("enabled", True),
                        "layer": recognizer_config.get("layer", "rule"),
                        "score": recognizer_config.get("score"),
                        "sourceRecognizer": recognizer_config.get("sourceRecognizer", "")
                    }
        
        except Exception as e:
            logger.error(f"获取识别器状态失败: {e}")
        
        return status

    def get_filter_config(self) -> Optional[Dict[str, Any]]:
        """
        获取过滤器配置

        Returns:
            过滤器配置字典，如果不存在则返回None
        """
        try:
            config = self.load_config()
            return config.get("filters", None)
        except Exception as e:
            logger.error(f"获取过滤器配置失败: {e}")
            return None

    def update_filter_config(self, filter_name: str, updates: Dict[str, Any]) -> bool:
        """
        更新过滤器配置

        Args:
            filter_name: 过滤器名称
            updates: 更新内容

        Returns:
            是否成功更新
        """
        try:
            config = self.load_config()
            filters = config.get("filters", {})

            if filter_name not in filters:
                logger.warning(f"过滤器 {filter_name} 不存在")
                return False

            # 更新配置
            filters[filter_name].update(updates)

            # 保存配置文件
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(f"过滤器 {filter_name} 配置更新成功")
            return True

        except Exception as e:
            logger.error(f"更新过滤器配置时发生异常: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取配置统计信息
        
        Returns:
            统计信息字典
        """
        try:
            config = self.load_config()
            recognizers = config.get("recognizers", {})
            
            total_recognizers = 0
            enabled_recognizers = 0
            category_stats = {}
            layer_stats = {"rule": 0, "context": 0, "nlp": 0}
            
            for category_name, category_config in recognizers.items():
                category_recognizers = category_config.get("recognizers", {})
                category_count = len(category_recognizers)
                category_enabled = 0
                
                for recognizer_config in category_recognizers.values():
                    total_recognizers += 1
                    
                    if recognizer_config.get("enabled", True):
                        enabled_recognizers += 1
                        category_enabled += 1
                    
                    layer = recognizer_config.get("layer", "rule")
                    layer_stats[layer] = layer_stats.get(layer, 0) + 1
                
                category_stats[category_name] = {
                    "total": category_count,
                    "enabled": category_enabled,
                    "disabled": category_count - category_enabled
                }
            
            return {
                "total_recognizers": total_recognizers,
                "enabled_recognizers": enabled_recognizers,
                "disabled_recognizers": total_recognizers - enabled_recognizers,
                "category_stats": category_stats,
                "layer_stats": layer_stats,
                "config_file": str(self.config_file_path),
                "last_modified": datetime.fromtimestamp(
                    self.config_file_path.stat().st_mtime
                ).isoformat() if self.config_file_path.exists() else None
            }
            
        except Exception as e:
            logger.error(f"获取配置统计信息失败: {e}")
            return {}


# 全局配置管理器实例
_global_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_file_path: Optional[str] = None) -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Args:
        config_file_path: 配置文件路径
        
    Returns:
        ConfigManager实例
    """
    global _global_config_manager
    
    if _global_config_manager is None:
        _global_config_manager = ConfigManager(config_file_path)
    
    return _global_config_manager


def initialize_config_system(config_file_path: Optional[str] = None) -> bool:
    """
    初始化配置系统
    
    Args:
        config_file_path: 配置文件路径
        
    Returns:
        是否成功初始化
    """
    try:
        manager = get_config_manager(config_file_path)
        return manager.reload_config()
    except Exception as e:
        logger.error(f"初始化配置系统失败: {e}")
        return False
