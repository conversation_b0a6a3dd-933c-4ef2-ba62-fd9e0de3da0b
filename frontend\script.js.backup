// 动态获取API地址
function getApiBaseUrl() {
    // 优先级：
    // 1. 固定配置的IP地址
    // 2. 当前页面的主机地址
    // 3. 本地地址

    const fixedIP = '***************'; // 您指定的固定IP
    const currentHost = window.location.hostname;

    // 如果当前访问的是固定IP，直接使用
    if (currentHost === fixedIP) {
        return `http://${fixedIP}:50505`;
    }

    // 如果当前是localhost或127.0.0.1，尝试固定IP
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        return `http://${fixedIP}:50505`;
    }

    // 否则使用当前主机地址
    return `http://${currentHost}:50505`;
}

// 全局配置
const CONFIG = {
    DEIDENTIFY_API: `${getApiBaseUrl()}/deidentify`,
    OCR_API: 'http://*************:8011/ocr/single',
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_FORMATS: ['image/jpeg', 'image/png', 'image/gif']
};

// 在页面加载时显示当前API配置
console.log('当前API配置:', CONFIG.DEIDENTIFY_API);

// 实体类型中文映射（完整版 - 支持40+种敏感信息类型）
const ENTITY_TYPE_MAPPING = {
    // === 个人身份信息类 ===
    'CHINESE_ID': '身份证号',
    'PASSPORT': '护照号',
    'PERSON': '人名',
    'PII': '个人身份信息',
    'ETHNICITY': '民族',
    'NRP': '民族/国籍',
    'EDUCATION_LEVEL': '学历学位',

    // === 联系方式类 ===
    'MOBILE_PHONE': '手机号码',
    'PHONE_NUMBER': '电话号码',
    'EMAIL_ADDRESS': '邮箱地址',
    'WECHAT_ID': '微信号',
    'QQ_NUMBER': 'QQ号码',
    'LANDLINE_PHONE': '固定电话',
    'COMMUNICATION_CONTENT': '通信内容',

    // === 医疗信息类 ===
    'MEDICAL_RECORD_ID': '病案号',
    'MEDICAL_INSURANCE': '医保卡号',
    'MEDICAL_POSITION': '医疗职位',
    'MEDICAL_ORGANIZATION': '医疗机构',
    'MEDICAL_LICENSE': '医疗执照',
    'HEALTH_DATA': '健康数据',
    'FAMILY_RELATIONSHIP': '家庭关系',

    // === 地理位置类 ===
    'COMPLETE_ADDRESS': '完整地址',
    'GPE': '地名',
    'LOCATION': '位置信息',
    'GPS_COORDINATE': 'GPS坐标',

    // === 金融信息类 ===
    'BANK_CARD': '银行卡号',
    'CREDIT_CARD': '信用卡号',
    'IBAN_CODE': '国际银行账号',
    'FINANCIAL_DATA': '金融数据',
    'CRYPTO': '加密货币',

    // === 机构信息类 ===
    'ORG': '机构名称',
    'COMPANY_WITH_PARENTHESES': '公司名称（含括号）',
    'ORGANIZATION': '组织机构',

    // === 车辆和设备类 ===
    'VEHICLE_INFO': '车辆信息',
    'LICENSE_PLATE': '车牌号',
    'DEVICE_SERIAL': '设备序列号',
    'ENHANCED_MEDICAL_NUMBER': '医疗设备编号',

    // === 网络和技术类 ===
    'URL': '网址',
    'IP_ADDRESS': 'IP地址',
    'CUSTOM_URL': '自定义网址',

    // === 隐私和其他类 ===
    'PRIVACY_INFO': '隐私信息',
    'CONTEXT_PRIVACY_INFO': '上下文隐私信息',
    'DATE_TIME': '日期时间',

    // === 兜底类型 ===
    'UNKNOWN': '未知类型',
    'OTHER': '其他信息'
};

// 敏感信息分组配置
const ENTITY_GROUPS = {
    'personal': {
        name: '个人身份信息',
        icon: 'fas fa-user',
        color: '#e74c3c',
        types: ['PERSON', 'CHINESE_ID', 'PASSPORT', 'PII', 'ETHNICITY', 'NRP']
    },
    'contact': {
        name: '联系方式',
        icon: 'fas fa-phone',
        color: '#3498db',
        types: ['MOBILE_PHONE', 'PHONE_NUMBER', 'EMAIL_ADDRESS', 'WECHAT_ID', 'COMMUNICATION_CONTENT']
    },
    'medical': {
        name: '医疗信息',
        icon: 'fas fa-heartbeat',
        color: '#2ecc71',
        types: ['MEDICAL_RECORD_ID', 'MEDICAL_INSURANCE', 'MEDICAL_POSITION', 'MEDICAL_ORGANIZATION', 'MEDICAL_LICENSE', 'HEALTH_DATA']
    },
    'location': {
        name: '地理位置',
        icon: 'fas fa-map-marker-alt',
        color: '#f39c12',
        types: ['COMPLETE_ADDRESS', 'GPE', 'LOCATION', 'GPS_COORDINATE']
    },
    'financial': {
        name: '金融信息',
        icon: 'fas fa-credit-card',
        color: '#9b59b6',
        types: ['BANK_CARD', 'CREDIT_CARD', 'IBAN_CODE', 'FINANCIAL_DATA', 'CRYPTO']
    },
    'organization': {
        name: '机构信息',
        icon: 'fas fa-building',
        color: '#34495e',
        types: ['ORG']
    },
    'other': {
        name: '其他信息',
        icon: 'fas fa-ellipsis-h',
        color: '#95a5a6',
        types: ['EDUCATION_LEVEL', 'VEHICLE_INFO', 'DEVICE_SERIAL', 'BIOMETRIC_FEATURE', 'PRIVACY_INFO', 'URL', 'IP_ADDRESS', 'DATE_TIME']
    }
};

// 全局变量
let currentResults = null;
let currentOriginalText = null; // 存储当前的原文本
let highlightEnabled = true;

// DOM元素 - 添加安全的元素获取
function safeGetElement(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`⚠️ 未找到元素: #${id}`);
    }
    return element;
}

function safeQuerySelectorAll(selector) {
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) {
        console.warn(`⚠️ 未找到元素: ${selector}`);
    }
    return elements;
}

const elements = {
    tabBtns: safeQuerySelectorAll('.tab-btn'),
    tabContents: safeQuerySelectorAll('.tab-content'),
    textInput: safeGetElement('text-input'),
    fileInput: safeGetElement('file-input'),
    uploadArea: safeGetElement('upload-area'),
    imagePreview: safeGetElement('image-preview'),
    previewImg: safeGetElement('preview-img'),
    // 批量图片相关元素
    batchImagesContainer: safeGetElement('batch-images-container'),
    imagesGrid: safeGetElement('images-grid'),
    imageCount: safeGetElement('image-count'),
    clearAllImagesBtn: safeGetElement('clear-all-images-btn'),
    processAllImagesBtn: safeGetElement('process-all-images-btn'),
    // 结果展示相关元素
    loading: safeGetElement('loading'),
    loadingText: safeGetElement('loading-text'),
    resultsSection: safeGetElement('results-section'),
    deidentifiedText: safeGetElement('deidentified-text'),
    highlightedText: safeGetElement('highlighted-text'),
    sensitiveInfoList: safeGetElement('sensitive-info-list'),
    filteredInfoList: safeGetElement('filtered-info-list'),
    detectedCount: safeGetElement('detected-count'),
    filteredCount: safeGetElement('filtered-count'),
    processingTime: safeGetElement('processing-time'),
    entityCount: safeGetElement('entity-count'),
    errorMessage: safeGetElement('error-message'),
    errorText: safeGetElement('error-text'),
    // 结果标签页相关元素
    resultTabBtns: safeQuerySelectorAll('.result-tab-btn'),
    resultTabContents: safeQuerySelectorAll('.result-tab-content')
};

// 批量图片管理
let batchImages = [];
let currentImageIndex = 0;

// 初始化
document.addEventListener('DOMContentLoaded', async function() {
    initializeEventListeners();
    initializeDragAndDrop();
    initRulesConfig(); // 初始化规则配置功能

    // 自动检测最佳API地址
    console.log('页面加载完成，开始检测API连接...');
    await detectBestApiUrl();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 标签页切换
    if (elements.tabBtns && elements.tabBtns.length > 0) {
        elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', () => switchTab(btn.dataset.tab));
        });
        console.log(`✓ 标签页切换事件已绑定 (${elements.tabBtns.length}个标签)`);
    } else {
        console.warn('⚠️ 未找到标签页按钮 .tab-btn');
    }

    // 按钮事件 - 添加null检查
    const clearTextBtn = document.getElementById('clear-text-btn');
    if (clearTextBtn) {
        clearTextBtn.addEventListener('click', clearText);
        console.log('✓ 清空按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到清空按钮 #clear-text-btn');
    }

    const processTextBtn = document.getElementById('process-text-btn');
    if (processTextBtn) {
        processTextBtn.addEventListener('click', processText);
        console.log('✓ 开始脱敏按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到开始脱敏按钮 #process-text-btn');
    }

    // 这些按钮在批量上传模式下不存在，移除或添加条件检查
    const removeImageBtn = document.getElementById('remove-image-btn');
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeImage);
        console.log('✓ 移除图片按钮事件已绑定');
    }

    const processImageBtn = document.getElementById('process-image-btn');
    if (processImageBtn) {
        processImageBtn.addEventListener('click', processImage);
        console.log('✓ 处理图片按钮事件已绑定');
    }

    const toggleHighlightBtn = document.getElementById('toggle-highlight');
    if (toggleHighlightBtn) {
        toggleHighlightBtn.addEventListener('click', toggleHighlight);
        console.log('✓ 切换高亮按钮事件已绑定');
    }

    const exportResultsBtn = document.getElementById('export-results');
    if (exportResultsBtn) {
        exportResultsBtn.addEventListener('click', exportResults);
        console.log('✓ 导出结果按钮事件已绑定');
    }

    // 添加示例数据按钮事件
    const loadExampleBtn = document.getElementById('load-example-btn');
    if (loadExampleBtn) {
        loadExampleBtn.addEventListener('click', loadExampleData);
        console.log('✓ 加载示例按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到加载示例按钮 #load-example-btn');
    }

    // 添加API检测按钮事件
    const refreshApiBtn = document.getElementById('refresh-api-btn');
    if (refreshApiBtn) {
        refreshApiBtn.addEventListener('click', async () => {
            const icon = refreshApiBtn.querySelector('i');
            const originalClass = icon.className;

            // 显示加载状态
            icon.className = 'fas fa-spinner fa-spin';
            refreshApiBtn.disabled = true;

            try {
                await detectBestApiUrl();
            } finally {
                // 恢复按钮状态
                icon.className = originalClass;
                refreshApiBtn.disabled = false;
            }
        });
    }

    // 添加全部展开/折叠按钮事件
    const toggleAllGroupsBtn = document.getElementById('toggle-all-groups');
    if (toggleAllGroupsBtn) {
        toggleAllGroupsBtn.addEventListener('click', toggleAllGroups);
    }

    // 添加卡片折叠功能
    initializeCardToggle();

    // 添加悬浮导航功能
    initializeFloatingNavigation();

    // 文件选择
    if (elements.fileInput) {
        console.log('找到文件输入元素，绑定change事件');
        elements.fileInput.addEventListener('change', handleFileSelect);
    } else {
        console.error('未找到文件输入元素 #file-input');
    }

    // 选择文件按钮点击事件
    const selectFilesBtn = document.getElementById('select-files-btn');
    if (selectFilesBtn) {
        console.log('找到选择文件按钮，绑定点击事件');
        selectFilesBtn.addEventListener('click', function(e) {
            console.log('选择文件按钮被点击');
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 阻止默认行为
            elements.fileInput.click();
        });
    } else {
        console.error('未找到选择文件按钮 #select-files-btn');
    }

    // 批量图片相关事件
    if (elements.clearAllImagesBtn) {
        elements.clearAllImagesBtn.addEventListener('click', clearAllImages);
    }

    if (elements.processAllImagesBtn) {
        elements.processAllImagesBtn.addEventListener('click', processAllImages);
    }

    // 结果标签页切换
    if (elements.resultTabBtns && elements.resultTabBtns.length > 0) {
        elements.resultTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                switchResultTab(e.target.dataset.tab);
            });
        });
        console.log(`✓ 结果标签页切换事件已绑定 (${elements.resultTabBtns.length}个标签)`);
    } else {
        console.warn('⚠️ 未找到结果标签页按钮 .result-tab-btn');
    }

    // 图片网格事件委托
    if (elements.imagesGrid) {
        elements.imagesGrid.addEventListener('click', handleImageGridClick);
        console.log('✓ 图片网格事件委托已绑定');
    }

    // 回车键快捷处理
    if (elements.textInput) {
        elements.textInput.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                processText();
            }
        });
        console.log('✓ 文本输入快捷键已绑定');
    }

    console.log('🎉 所有事件监听器初始化完成');
}

// 初始化拖拽上传
function initializeDragAndDrop() {
    if (!elements.uploadArea) {
        console.warn('⚠️ 未找到上传区域，跳过拖拽上传初始化');
        return;
    }

    elements.uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    elements.uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    elements.uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            // 支持批量拖拽上传
            handleBatchFileUpload(files);
        }
    });

    elements.uploadArea.addEventListener('click', function(e) {
        // 只有当点击的不是按钮时才触发文件选择
        if (e.target.tagName !== 'BUTTON' && !e.target.closest('button')) {
            if (elements.fileInput) {
                elements.fileInput.click();
            }
        }
    });

    console.log('✓ 拖拽上传功能已初始化');
}

// 切换标签页
function switchTab(tabName) {
    elements.tabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });
    
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
}

// 清空文本
function clearText() {
    console.log('清空文本按钮被点击');
    if (elements.textInput) {
        elements.textInput.value = '';
        console.log('✓ 文本已清空');
    } else {
        console.error('❌ 文本输入框不存在');
    }
    hideResults();
}

// 处理文本脱敏
async function processText() {
    console.log('开始脱敏按钮被点击');

    if (!elements.textInput) {
        console.error('❌ 文本输入框不存在');
        showError('文本输入框不存在');
        return;
    }

    const text = elements.textInput.value.trim();
    if (!text) {
        console.log('⚠️ 文本为空');
        showError('请输入待脱敏的文本');
        return;
    }

    console.log('✓ 开始处理文本脱敏，文本长度:', text.length);

    try {
        showLoading('正在进行脱敏处理...');
        const results = await callDeidentifyAPI(text);
        showResults(results, text);
    } catch (error) {
        showError('脱敏处理失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 文件选择处理
function handleFileSelect(e) {
    console.log('handleFileSelect 被调用，文件数量:', e.target.files.length);
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        console.log('开始处理批量文件上传');
        // 支持批量上传
        handleBatchFileUpload(files);
    } else {
        console.log('没有选择文件');
    }
}

// 批量文件上传处理
function handleBatchFileUpload(files) {
    const validFiles = files.filter(file => validateFile(file));

    if (validFiles.length === 0) {
        showError('没有有效的图片文件');
        return;
    }

    // 添加到批量图片列表
    validFiles.forEach(file => {
        const imageId = generateImageId();
        const imageItem = {
            id: imageId,
            file: file,
            status: 'pending', // pending, processing, completed, error
            result: null,
            error: null
        };
        batchImages.push(imageItem);
    });

    // 更新界面
    updateBatchImagesDisplay();
    showBatchImagesContainer();
}

// 生成唯一图片ID
function generateImageId() {
    return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 显示批量图片容器
function showBatchImagesContainer() {
    elements.uploadArea.style.display = 'none';
    elements.batchImagesContainer.style.display = 'block';
}

// 隐藏批量图片容器
function hideBatchImagesContainer() {
    elements.uploadArea.style.display = 'block';
    elements.batchImagesContainer.style.display = 'none';
}

// 更新批量图片显示
function updateBatchImagesDisplay() {
    elements.imageCount.textContent = batchImages.length;
    elements.imagesGrid.innerHTML = '';

    batchImages.forEach(imageItem => {
        const imageElement = createImageItemElement(imageItem);
        elements.imagesGrid.appendChild(imageElement);
    });
}

// 验证文件
function validateFile(file) {
    // 验证文件类型
    if (!CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
        showError(`文件 ${file.name} 格式不支持，请选择 JPG、PNG 或 GIF 格式的图片`);
        return false;
    }

    // 验证文件大小
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        showError(`文件 ${file.name} 过大，请选择小于 10MB 的图片`);
        return false;
    }

    return true;
}

// 创建图片项元素
function createImageItemElement(imageItem) {
    const div = document.createElement('div');
    div.className = `image-item ${imageItem.status}`;
    div.dataset.imageId = imageItem.id;

    const fileSize = formatFileSize(imageItem.file.size);
    const statusText = getStatusText(imageItem.status);

    div.innerHTML = `
        <div class="image-preview-container">
            <img class="image-preview-img" src="${URL.createObjectURL(imageItem.file)}" alt="预览图片">
            <div class="image-status ${imageItem.status}">${statusText}</div>
        </div>
        <div class="image-info">
            <div class="image-filename">${imageItem.file.name}</div>
            <div class="image-size">${fileSize}</div>
        </div>
        ${imageItem.status === 'processing' ? '<div class="image-progress"><div class="image-progress-bar"></div></div>' : ''}
        <div class="image-actions">
            <button class="btn btn-small btn-secondary remove-single-image" data-image-id="${imageItem.id}">
                <i class="fas fa-trash"></i>
                移除
            </button>
            <button class="btn btn-small btn-primary process-single-image" data-image-id="${imageItem.id}"
                    ${imageItem.status === 'processing' ? 'disabled' : ''}>
                <i class="fas fa-play"></i>
                ${imageItem.status === 'completed' ? '重新处理' : '处理'}
            </button>
        </div>
        ${imageItem.result ? createResultPreview(imageItem.result) : ''}
        ${imageItem.error ? `<div class="error-info"><i class="fas fa-exclamation-triangle"></i> ${imageItem.error}</div>` : ''}
    `;

    return div;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'error': '处理失败'
    };
    return statusMap[status] || '未知';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 创建结果预览
function createResultPreview(result) {
    if (!result) return '';

    // 兼容不同的数据格式
    const entities = result.sensitive_words || result.entities || [];
    const entityCount = entities.length;
    const filteredCount = result.filtered_entities ? result.filtered_entities.length : 0;

    return `
        <div class="result-preview">
            <div class="result-summary">
                <span class="result-stat">
                    <i class="fas fa-exclamation-triangle"></i>
                    检测到 ${entityCount} 个敏感信息
                </span>
                ${filteredCount > 0 ? `
                <span class="result-stat">
                    <i class="fas fa-filter"></i>
                    过滤了 ${filteredCount} 个词汇
                </span>
                ` : ''}
            </div>
            <button class="btn btn-small btn-outline view-detail" data-image-id="${result.imageId}">
                <i class="fas fa-eye"></i>
                查看详情
            </button>
        </div>
    `;
}

// 清空所有图片
function clearAllImages() {
    batchImages = [];
    updateBatchImagesDisplay();
    hideBatchImagesContainer();
    // 重置文件输入
    elements.fileInput.value = '';
}

// 批量处理所有图片
async function processAllImages() {
    const pendingImages = batchImages.filter(img => img.status === 'pending' || img.status === 'error');

    if (pendingImages.length === 0) {
        showError('没有待处理的图片');
        return;
    }

    console.log(`开始批量处理 ${pendingImages.length} 张图片`);

    // 禁用批量处理按钮
    elements.processAllImagesBtn.disabled = true;
    elements.processAllImagesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量处理中...';

    let lastCompletedImage = null;

    try {
        for (const imageItem of pendingImages) {
            await processSingleImage(imageItem.id);

            // 记录最后一个成功处理的图片
            if (imageItem.status === 'completed' && imageItem.result) {
                lastCompletedImage = imageItem;
            }

            // 添加小延迟避免过快请求
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 批量处理完成后，自动显示最后一个成功处理的图片结果
        if (lastCompletedImage) {
            console.log(`批量处理完成，自动显示最后处理的图片结果: ${lastCompletedImage.file.name}`);
            viewImageDetail(lastCompletedImage.id);

            // 滚动到结果区域
            setTimeout(() => {
                if (elements.resultsSection && elements.resultsSection.style.display === 'block') {
                    elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 300);
        }

        console.log('✅ 批量处理全部完成');

    } finally {
        // 恢复按钮状态
        elements.processAllImagesBtn.disabled = false;
        elements.processAllImagesBtn.innerHTML = '<i class="fas fa-play"></i> 批量处理';
    }
}

// 处理单张图片
async function processSingleImage(imageId) {
    const imageItem = batchImages.find(img => img.id === imageId);
    if (!imageItem) return;

    // 更新状态为处理中
    imageItem.status = 'processing';
    imageItem.error = null;
    updateImageItemDisplay(imageItem);

    try {
        // 先进行OCR识别
        const ocrResult = await performOCR(imageItem.file);

        if (!ocrResult.success) {
            throw new Error(ocrResult.error || 'OCR识别失败');
        }

        const extractedText = ocrResult.text;

        if (!extractedText || extractedText.trim() === '') {
            throw new Error('未能从图片中提取到文本');
        }

        // 进行脱敏处理
        const deidentifyResult = await performDeidentification(extractedText);

        if (!deidentifyResult.success) {
            throw new Error(deidentifyResult.error || '脱敏处理失败');
        }

        // 保存结果 - 确保数据格式与showResults函数兼容
        imageItem.result = {
            ...deidentifyResult.data,
            imageId: imageId,
            originalText: extractedText,
            // 确保字段名称与showResults函数期望的一致
            sensitive_words: deidentifyResult.data.entities || deidentifyResult.data.sensitive_words || [],
            total_entities: deidentifyResult.data.total_entities || (deidentifyResult.data.entities || deidentifyResult.data.sensitive_words || []).length
        };
        imageItem.status = 'completed';

        console.log(`✓ 图片处理完成: ${imageItem.file.name}，检测到 ${imageItem.result.sensitive_words.length} 个敏感信息`);

        // 如果是单独处理（非批量处理），自动显示结果
        const isInBatchProcessing = elements.processAllImagesBtn && elements.processAllImagesBtn.disabled;
        if (!isInBatchProcessing) {
            console.log('单独处理完成，自动显示结果');
            setTimeout(() => {
                viewImageDetail(imageId);
                // 滚动到结果区域
                if (elements.resultsSection && elements.resultsSection.style.display === 'block') {
                    elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 300);
        }

    } catch (error) {
        console.error('处理图片失败:', error);
        imageItem.status = 'error';
        imageItem.error = error.message;
    }

    // 更新显示
    updateImageItemDisplay(imageItem);
}

// 更新单个图片项显示
function updateImageItemDisplay(imageItem) {
    const imageElement = document.querySelector(`[data-image-id="${imageItem.id}"]`);
    if (imageElement) {
        const newElement = createImageItemElement(imageItem);
        imageElement.parentNode.replaceChild(newElement, imageElement);
    }
}

// 图片网格点击事件处理
function handleImageGridClick(event) {
    const target = event.target;
    const button = target.closest('button');

    if (!button) return;

    const imageId = button.dataset.imageId;

    if (button.classList.contains('remove-single-image')) {
        removeSingleImage(imageId);
    } else if (button.classList.contains('process-single-image')) {
        processSingleImage(imageId);
    } else if (button.classList.contains('view-detail')) {
        viewImageDetail(imageId);
    }
}

// 移除单张图片
function removeSingleImage(imageId) {
    const index = batchImages.findIndex(img => img.id === imageId);
    if (index !== -1) {
        batchImages.splice(index, 1);
        updateBatchImagesDisplay();

        if (batchImages.length === 0) {
            hideBatchImagesContainer();
        }
    }
}

// 查看图片详情
function viewImageDetail(imageId) {
    console.log(`查看图片详情: ${imageId}`);

    const imageItem = batchImages.find(img => img.id === imageId);
    if (!imageItem) {
        console.error(`未找到图片项: ${imageId}`);
        return;
    }

    if (!imageItem.result) {
        console.error(`图片 ${imageItem.file.name} 没有处理结果`);
        showError('该图片还没有处理结果，请先进行处理');
        return;
    }

    console.log(`显示图片 ${imageItem.file.name} 的处理结果:`, imageItem.result);

    // 显示该图片的处理结果
    displayResults(imageItem.result);

    console.log('✓ 图片详情显示完成');
}

// 切换结果标签页
function switchResultTab(tabName) {
    // 更新标签按钮状态
    elements.resultTabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // 更新标签内容显示
    elements.resultTabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
}

// 处理文件
function handleFile(file) {
    // 验证文件类型
    if (!CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
        showError('不支持的文件格式，请选择 JPG、PNG 或 GIF 格式的图片');
        return;
    }

    // 验证文件大小
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        showError('文件大小超过限制，请选择小于 10MB 的图片');
        return;
    }

    // 显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        elements.previewImg.src = e.target.result;
        elements.uploadArea.style.display = 'none';
        elements.imagePreview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

// 移除图片
function removeImage() {
    elements.uploadArea.style.display = 'block';
    elements.imagePreview.style.display = 'none';
    elements.fileInput.value = '';
    hideResults();
}

// 处理图片OCR和脱敏
async function processImage() {
    const file = elements.fileInput.files[0];
    if (!file) {
        showError('请先选择图片');
        return;
    }

    try {
        showLoading('正在提取图片文本...');
        
        // 转换为base64
        const base64 = await fileToBase64(file);
        
        // 调用OCR API
        const ocrText = await callOCRAPI(base64);
        
        if (!ocrText.trim()) {
            showError('未能从图片中提取到文本内容');
            return;
        }

        // 更新加载状态
        elements.loadingText.textContent = '正在进行脱敏处理...';
        
        // 调用脱敏API
        const results = await callDeidentifyAPI(ocrText);
        showResults(results, ocrText);
        
    } catch (error) {
        showError('处理失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 文件转base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            // 移除data:image/...;base64,前缀
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// 调用OCR API
async function callOCRAPI(base64Image) {
    const response = await fetch(CONFIG.OCR_API, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            image: base64Image
        })
    });

    if (!response.ok) {
        throw new Error(`OCR服务请求失败: ${response.status}`);
    }

    const data = await response.json();

    // 根据API文档，提取路径为 result.markdown_result
    if (data.result && data.result.markdown_result) {
        return data.result.markdown_result;
    } else {
        throw new Error('OCR服务返回数据格式错误');
    }
}

// 批量处理专用的OCR函数
async function performOCR(file) {
    try {
        console.log(`开始OCR处理: ${file.name}`);

        // 转换文件为base64
        const base64 = await fileToBase64(file);

        // 调用OCR API
        const text = await callOCRAPI(base64);

        console.log(`OCR处理完成: ${file.name}, 提取文本长度: ${text.length}`);

        return {
            success: true,
            text: text
        };
    } catch (error) {
        console.error(`OCR处理失败: ${file.name}`, error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 批量处理专用的脱敏函数
async function performDeidentification(text) {
    try {
        console.log(`开始脱敏处理，文本长度: ${text.length}`);

        // 调用脱敏API
        const results = await callDeidentifyAPI(text);

        console.log(`脱敏处理完成，检测到 ${results.sensitive_words.length} 个敏感信息`);

        return {
            success: true,
            data: results
        };
    } catch (error) {
        console.error('脱敏处理失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 测试API连接
async function testApiConnection(apiUrl) {
    try {
        const healthUrl = apiUrl.replace('/deidentify', '/health');
        const response = await fetch(healthUrl, {
            method: 'GET',
            timeout: 5000
        });
        return response.ok;
    } catch (error) {
        return false;
    }
}

// 自动检测最佳API地址
async function detectBestApiUrl() {
    const candidates = [
        'http://***************:50505',  // 固定IP
        'http://127.0.0.1:50505',        // 本地地址
        'http://localhost:50505',        // 本地主机
        `http://${window.location.hostname}:50505`  // 当前主机
    ];

    console.log('正在检测最佳API地址...');

    for (const baseUrl of candidates) {
        console.log(`测试: ${baseUrl}`);
        if (await testApiConnection(baseUrl)) {
            const apiUrl = `${baseUrl}/deidentify`;
            console.log(`✓ 找到可用API: ${apiUrl}`);
            CONFIG.DEIDENTIFY_API = apiUrl;

            // 更新页面显示
            updateApiStatus(apiUrl, true);
            return apiUrl;
        }
    }

    console.log('❌ 未找到可用的API服务');
    updateApiStatus(CONFIG.DEIDENTIFY_API, false);
    return null;
}

// 更新API状态显示
function updateApiStatus(apiUrl, isConnected) {
    // 在页面上显示API状态
    let statusElement = document.getElementById('api-status');
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'api-status';
        statusElement.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 13px;
            z-index: 1000;
            max-width: 350px;
            word-break: break-all;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        `;
        document.body.appendChild(statusElement);

        // 点击状态框可以隐藏
        statusElement.addEventListener('click', () => {
            statusElement.style.opacity = '0';
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 300);
        });
    }

    const baseUrl = apiUrl.replace('/deidentify', '');

    if (isConnected) {
        statusElement.style.backgroundColor = 'rgba(212, 237, 218, 0.95)';
        statusElement.style.color = '#155724';
        statusElement.style.border = '1px solid #c3e6cb';
        statusElement.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                <i class="fas fa-check-circle" style="color: #28a745;"></i>
                <strong>API连接正常</strong>
            </div>
            <div style="font-size: 11px; opacity: 0.8;">
                服务地址: ${baseUrl}<br>
                点击此处关闭提示
            </div>
        `;
    } else {
        statusElement.style.backgroundColor = 'rgba(248, 215, 218, 0.95)';
        statusElement.style.color = '#721c24';
        statusElement.style.border = '1px solid #f5c6cb';
        statusElement.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                <strong>API连接失败</strong>
            </div>
            <div style="font-size: 11px; opacity: 0.8;">
                尝试地址: ${baseUrl}<br>
                请检查服务是否运行<br>
                点击此处关闭提示
            </div>
        `;
    }

    // 重置透明度
    statusElement.style.opacity = '1';
}

// 调用脱敏API
async function callDeidentifyAPI(text) {
    // 如果当前API不可用，尝试重新检测
    if (!(await testApiConnection(CONFIG.DEIDENTIFY_API.replace('/deidentify', '')))) {
        console.log('当前API不可用，尝试重新检测...');
        const newApiUrl = await detectBestApiUrl();
        if (!newApiUrl) {
            throw new Error('无法连接到脱敏API服务，请检查服务是否正在运行');
        }
    }

    const response = await fetch(CONFIG.DEIDENTIFY_API, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: text
        })
    });

    if (!response.ok) {
        throw new Error(`脱敏服务请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data;
}

// 显示结果
function showResults(results, originalText) {
    console.log('showResults 被调用');
    console.log('结果数据:', results);
    console.log('原文长度:', originalText ? originalText.length : 0);

    currentResults = results;
    currentOriginalText = originalText; // 存储原文本

    // 检查必要的DOM元素
    if (!elements.resultsSection) {
        console.error('结果区域元素不存在');
        return;
    }

    // 显示脱敏后的文本
    if (elements.deidentifiedText) {
        elements.deidentifiedText.textContent = results.deidentified_text || '无脱敏文本';
        console.log('✓ 脱敏文本已显示');
    } else {
        console.warn('脱敏文本元素不存在');
    }

    // 显示高亮的原文
    if (originalText && results.sensitive_words) {
        displayHighlightedText(originalText, results.sensitive_words);
        console.log('✓ 高亮文本已显示');
    } else {
        console.warn('无法显示高亮文本：原文或敏感词为空');
    }

    // 显示敏感信息列表
    const sensitiveWords = results.sensitive_words || [];
    displaySensitiveInfoList(sensitiveWords);
    console.log(`✓ 敏感信息列表已显示，共 ${sensitiveWords.length} 项`);

    // 显示过滤信息详情
    const filteredEntities = results.filtered_entities || [];
    displayFilteredInfo(filteredEntities);
    console.log(`✓ 过滤信息已显示，共 ${filteredEntities.length} 项`);

    // 更新统计信息
    if (elements.processingTime) {
        elements.processingTime.textContent = Math.round((results.processing_time || 0) * 1000);
    }
    if (elements.entityCount) {
        elements.entityCount.textContent = results.total_entities || sensitiveWords.length;
    }

    // 更新标签页计数
    if (elements.detectedCount) {
        elements.detectedCount.textContent = sensitiveWords.length;
        console.log(`✓ 检测到的敏感信息计数已更新: ${sensitiveWords.length}`);
    }
    if (elements.filteredCount) {
        elements.filteredCount.textContent = filteredEntities.length;
        console.log(`✓ 过滤词汇计数已更新: ${filteredEntities.length}`);
    }

    // 确保脱敏结果卡片默认为折叠状态
    try {
        const deidentifiedContent = document.getElementById('deidentified-content');
        const deidentifiedToggle = document.querySelector('[data-target="deidentified-content"]');
        if (deidentifiedContent && deidentifiedToggle) {
            const toggleIcon = deidentifiedToggle.nextElementSibling?.querySelector('i');
            if (toggleIcon) {
                deidentifiedContent.classList.add('collapsed');
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-right');
            }
        }
    } catch (error) {
        console.warn('设置脱敏结果卡片折叠状态时出错:', error);
    }

    // 显示结果区域
    elements.resultsSection.style.display = 'block';
    console.log('✓ 结果区域已显示');

    // 显示悬浮导航栏
    const floatingNav = document.getElementById('floating-nav');
    if (floatingNav) {
        floatingNav.style.display = 'block';
        console.log('✓ 悬浮导航栏已显示');
    }

    // 滚动到结果区域
    setTimeout(() => {
        elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
        console.log('✓ 已滚动到结果区域');
    }, 100);

    console.log('🎉 showResults 完成，结果已全部显示');
}

// 显示高亮文本
function displayHighlightedText(originalText, sensitiveWords) {
    if (!highlightEnabled || !sensitiveWords.length) {
        elements.highlightedText.textContent = originalText;
        return;
    }

    let highlightedHTML = '';
    let lastIndex = 0;

    // 按位置正序排序
    const sortedWords = [...sensitiveWords].sort((a, b) => a.start - b.start);

    sortedWords.forEach(word => {
        // 添加敏感词之前的普通文本
        highlightedHTML += escapeHtml(originalText.substring(lastIndex, word.start));

        // 添加高亮的敏感词
        const entityTypeCN = ENTITY_TYPE_MAPPING[word.entity_type] || word.entity_type;
        const sensitiveText = originalText.substring(word.start, word.end);

        highlightedHTML += `<span class="sensitive-highlight" data-type="${entityTypeCN}" title="${entityTypeCN} (置信度: ${Math.round(word.confidence * 100)}%)">${escapeHtml(sensitiveText)}</span>`;

        lastIndex = word.end;
    });

    // 添加最后剩余的普通文本
    highlightedHTML += escapeHtml(originalText.substring(lastIndex));

    elements.highlightedText.innerHTML = highlightedHTML;
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示敏感信息列表（分组展示）
function displaySensitiveInfoList(sensitiveWords) {
    if (!sensitiveWords.length) {
        elements.sensitiveInfoList.innerHTML = '<p style="text-align: center; color: #6c757d;">未检测到敏感信息</p>';
        return;
    }

    // 按类型分组敏感信息
    const groupedData = groupSensitiveWords(sensitiveWords);

    // 生成分组HTML
    const groupsHTML = Object.keys(groupedData).map(groupKey => {
        const group = ENTITY_GROUPS[groupKey];
        const items = groupedData[groupKey];

        if (!items.length) return '';

        return createGroupHTML(groupKey, group, items);
    }).filter(html => html).join('');

    elements.sensitiveInfoList.innerHTML = groupsHTML;

    // 添加折叠/展开事件监听器
    addGroupToggleListeners();
}

// 按类型分组敏感信息
function groupSensitiveWords(sensitiveWords) {
    const grouped = {};

    // 初始化分组
    Object.keys(ENTITY_GROUPS).forEach(key => {
        grouped[key] = [];
    });

    // 分组敏感信息
    sensitiveWords.forEach(word => {
        let assigned = false;

        // 查找对应的分组
        for (const [groupKey, groupConfig] of Object.entries(ENTITY_GROUPS)) {
            if (groupConfig.types.includes(word.entity_type)) {
                grouped[groupKey].push(word);
                assigned = true;
                break;
            }
        }

        // 如果没有找到对应分组，放入"其他信息"
        if (!assigned) {
            grouped['other'].push(word);
        }
    });

    return grouped;
}

// 创建分组HTML
function createGroupHTML(groupKey, group, items) {
    const itemsHTML = items.map(word => {
        const entityTypeCN = ENTITY_TYPE_MAPPING[word.entity_type] || word.entity_type;
        const confidence = Math.round(word.confidence * 100);

        return `
            <div class="sensitive-item-compact">
                <div class="item-text-compact">${escapeHtml(word.text)}</div>
                <div class="item-meta">
                    <span class="item-type-compact">${entityTypeCN}</span>
                    <span class="item-confidence-compact">${confidence}%</span>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="sensitive-group">
            <div class="group-header" data-group="${groupKey}">
                <div class="group-title">
                    <i class="${group.icon}" style="color: ${group.color}"></i>
                    <span class="group-name">${group.name}</span>
                    <span class="group-count">${items.length}项</span>
                </div>
                <button class="group-toggle" aria-label="展开/折叠">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="group-content" data-group="${groupKey}">
                <div class="sensitive-items-grid">
                    ${itemsHTML}
                </div>
            </div>
        </div>
    `;
}

// 添加分组折叠/展开事件监听器
function addGroupToggleListeners() {
    document.querySelectorAll('.group-header').forEach(header => {
        header.addEventListener('click', function() {
            const groupKey = this.dataset.group;
            const content = document.querySelector(`.group-content[data-group="${groupKey}"]`);
            const toggle = this.querySelector('.group-toggle i');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.classList.remove('fa-chevron-right');
                toggle.classList.add('fa-chevron-down');
            } else {
                content.classList.add('collapsed');
                toggle.classList.remove('fa-chevron-down');
                toggle.classList.add('fa-chevron-right');
            }

            // 更新全部展开/折叠按钮状态
            updateToggleAllButton();
        });
    });
}

// 全部展开/折叠分组
function toggleAllGroups() {
    const allContents = document.querySelectorAll('.group-content');
    const allToggles = document.querySelectorAll('.group-toggle i');
    const toggleBtn = document.getElementById('toggle-all-groups');

    // 检查是否有折叠的分组
    const hasCollapsed = Array.from(allContents).some(content => content.classList.contains('collapsed'));

    if (hasCollapsed) {
        // 全部展开
        allContents.forEach(content => content.classList.remove('collapsed'));
        allToggles.forEach(toggle => {
            toggle.classList.remove('fa-chevron-right');
            toggle.classList.add('fa-chevron-down');
        });
        toggleBtn.innerHTML = '<i class="fas fa-compress-alt"></i> 全部折叠';
    } else {
        // 全部折叠
        allContents.forEach(content => content.classList.add('collapsed'));
        allToggles.forEach(toggle => {
            toggle.classList.remove('fa-chevron-down');
            toggle.classList.add('fa-chevron-right');
        });
        toggleBtn.innerHTML = '<i class="fas fa-expand-alt"></i> 全部展开';
    }
}

// 更新全部展开/折叠按钮状态
function updateToggleAllButton() {
    const toggleBtn = document.getElementById('toggle-all-groups');
    if (!toggleBtn) return;

    const allContents = document.querySelectorAll('.group-content');
    const hasCollapsed = Array.from(allContents).some(content => content.classList.contains('collapsed'));

    if (hasCollapsed) {
        toggleBtn.innerHTML = '<i class="fas fa-expand-alt"></i> 全部展开';
    } else {
        toggleBtn.innerHTML = '<i class="fas fa-compress-alt"></i> 全部折叠';
    }
}

// 切换高亮显示
function toggleHighlight() {
    highlightEnabled = !highlightEnabled;

    if (currentResults && currentOriginalText) {
        displayHighlightedText(currentOriginalText, currentResults.sensitive_words);
    }

    const btn = document.getElementById('toggle-highlight');
    btn.innerHTML = highlightEnabled ?
        '<i class="fas fa-eye"></i> 关闭高亮' :
        '<i class="fas fa-eye-slash"></i> 开启高亮';
}

// 导出结果
function exportResults() {
    if (!currentResults) {
        showError('没有可导出的结果');
        return;
    }

    const exportData = {
        timestamp: new Date().toISOString(),
        processing_time: currentResults.processing_time,
        total_entities: currentResults.total_entities,
        deidentified_text: currentResults.deidentified_text,
        sensitive_words: currentResults.sensitive_words.map(word => ({
            text: word.text,
            entity_type: word.entity_type,
            entity_type_cn: ENTITY_TYPE_MAPPING[word.entity_type] || word.entity_type,
            confidence: word.confidence,
            start: word.start,
            end: word.end
        }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `hipaa_deidentify_results_${new Date().getTime()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 显示加载状态
function showLoading(message = '正在处理中...') {
    elements.loadingText.textContent = message;
    elements.loading.style.display = 'block';
    hideError();
}

// 隐藏加载状态
function hideLoading() {
    elements.loading.style.display = 'none';
}

// 显示错误
function showError(message) {
    elements.errorText.textContent = message;
    elements.errorMessage.style.display = 'flex';
    hideLoading();
}

// 隐藏错误
function hideError() {
    elements.errorMessage.style.display = 'none';
}

// 隐藏结果
function hideResults() {
    elements.resultsSection.style.display = 'none';

    // 隐藏悬浮导航栏
    const floatingNav = document.getElementById('floating-nav');
    if (floatingNav) {
        floatingNav.style.display = 'none';
    }

    currentResults = null;
    currentOriginalText = null;
}

// 初始化卡片折叠功能
function initializeCardToggle() {
    document.addEventListener('click', function(e) {
        if (e.target.closest('.clickable-header')) {
            const header = e.target.closest('.clickable-header');
            const targetId = header.dataset.target;
            const content = document.getElementById(targetId);
            const toggle = header.querySelector('.card-toggle i');

            if (content && toggle) {
                if (content.classList.contains('collapsed')) {
                    content.classList.remove('collapsed');
                    toggle.classList.remove('fa-chevron-right');
                    toggle.classList.add('fa-chevron-down');
                } else {
                    content.classList.add('collapsed');
                    toggle.classList.remove('fa-chevron-down');
                    toggle.classList.add('fa-chevron-right');
                }
            }
        }
    });
}

// 初始化悬浮导航功能
function initializeFloatingNavigation() {
    const floatingNav = document.getElementById('floating-nav');
    const navToggle = document.getElementById('nav-toggle');
    const backTopBtn = document.getElementById('nav-back-top');

    // 导航栏折叠/展开功能
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            floatingNav.classList.toggle('collapsed');
        });
    }

    // 返回顶部功能
    if (backTopBtn) {
        backTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 导航项点击功能
    document.addEventListener('click', function(e) {
        if (e.target.closest('.nav-item')) {
            e.preventDefault();
            const navItem = e.target.closest('.nav-item');
            const targetId = navItem.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // 更新活动状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                navItem.classList.add('active');

                // 滚动到目标元素
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // 添加高亮效果
                targetElement.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.5)';
                setTimeout(() => {
                    targetElement.style.boxShadow = '';
                }, 2000);
            }
        }
    });

    // 滚动时更新活动状态
    let ticking = false;
    function updateActiveNavItem() {
        if (!ticking) {
            requestAnimationFrame(() => {
                const sections = ['highlighted-section', 'deidentified-section', 'details-section'];
                const windowHeight = window.innerHeight;

                let activeSection = null;

                sections.forEach(sectionId => {
                    const element = document.getElementById(sectionId);
                    if (element) {
                        const rect = element.getBoundingClientRect();
                        if (rect.top <= windowHeight / 2 && rect.bottom >= windowHeight / 2) {
                            activeSection = sectionId;
                        }
                    }
                });

                // 更新导航项活动状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                    if (activeSection && item.getAttribute('href') === `#${activeSection}`) {
                        item.classList.add('active');
                    }
                });

                ticking = false;
            });
        }
        ticking = true;
    }

    // 监听滚动事件
    window.addEventListener('scroll', updateActiveNavItem);
}

// 加载示例数据
function loadExampleData() {
    const exampleText = `患者基本信息：
姓名：张三
性别：男
年龄：35岁
身份证号：110101199001011234
联系电话：13812345678
微信号：zhangsan_2024
家庭住址：北京市朝阳区建国路88号阳光大厦1201室

就诊信息：
病案号：BH20240315001
医保卡号：1101011990010112345
就诊医院：北京协和医院
主治医师：李主任医师
科室：心内科

家属联系信息：
紧急联系人：王丽（妻子）
联系电话：13987654321
工作单位：北京大学第一医院

其他信息：
民族：汉族
学历：本科
邮箱：<EMAIL>
车牌号：京A12345`;

    console.log('加载示例按钮被点击');

    if (!elements.textInput) {
        console.error('❌ 文本输入框不存在');
        showError('文本输入框不存在');
        return;
    }

    elements.textInput.value = exampleText;
    console.log('✓ 示例数据已加载');

    // 切换到文本输入标签页
    switchTab('text');

    // 滚动到文本输入区域
    elements.textInput.scrollIntoView({ behavior: 'smooth' });
    elements.textInput.focus();
}

// ==================== 规则配置功能 ====================

// 初始化规则配置功能
function initRulesConfig() {
    // 获取所有分类头部
    const categoryHeaders = document.querySelectorAll('.category-header');

    // 为每个分类头部添加点击事件
    categoryHeaders.forEach(header => {
        header.addEventListener('click', function() {
            toggleCategory(this);
        });
    });

    // 获取所有功能按钮并添加开发中提示
    const developmentButtons = document.querySelectorAll('.add-tag-btn, .keyword-tag');
    developmentButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            showDevelopmentNotice();
        });
    });

    // 为开关添加事件监听（仅UI展示）
    const switches = document.querySelectorAll('.switch input');
    switches.forEach(switchInput => {
        switchInput.addEventListener('change', function(e) {
            e.stopPropagation();
            // 这里只是UI展示，实际功能待开发
            console.log('规则开关状态改变:', this.checked);

            // 添加视觉反馈
            const ruleItem = this.closest('.rule-item');
            if (ruleItem) {
                if (this.checked) {
                    ruleItem.style.opacity = '1';
                    ruleItem.classList.remove('disabled');
                } else {
                    ruleItem.style.opacity = '0.6';
                    ruleItem.classList.add('disabled');
                }
            }
        });
    });
}

// 切换分类展开/折叠状态
function toggleCategory(header) {
    const content = header.nextElementSibling;
    const isExpanded = header.classList.contains('expanded');

    if (isExpanded) {
        // 折叠
        header.classList.remove('expanded');
        content.classList.add('collapsed');
    } else {
        // 展开
        header.classList.add('expanded');
        content.classList.remove('collapsed');
    }
}

// 显示开发中提示
function showDevelopmentNotice() {
    // 创建提示框
    const notice = document.createElement('div');
    notice.className = 'development-popup';
    notice.innerHTML = `
        <div class="popup-content">
            <div class="popup-header">
                <i class="fas fa-info-circle"></i>
                <h3>功能开发中</h3>
            </div>
            <div class="popup-body">
                <p>此功能正在开发中，敬请期待！</p>
                <p class="popup-hint">该功能将支持自定义Presidio敏感信息检测规则配置</p>
            </div>
            <div class="popup-footer">
                <button class="btn btn-primary" onclick="closeDevelopmentNotice()">
                    <i class="fas fa-check"></i>
                    我知道了
                </button>
            </div>
        </div>
        <div class="popup-overlay" onclick="closeDevelopmentNotice()"></div>
    `;

    document.body.appendChild(notice);

    // 添加显示动画
    setTimeout(() => {
        notice.classList.add('show');
    }, 10);
}

// 关闭开发中提示
function closeDevelopmentNotice() {
    const notice = document.querySelector('.development-popup');
    if (notice) {
        notice.classList.remove('show');
        setTimeout(() => {
            notice.remove();
        }, 300);
    }
}

// 显示过滤信息详情
function displayFilteredInfo(filteredEntities) {
    if (!elements.filteredInfoList) return;

    if (!filteredEntities || filteredEntities.length === 0) {
        elements.filteredInfoList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-filter"></i>
                <p>暂无被过滤的词汇</p>
                <small>过滤器会自动识别并过滤掉误报的医学术语</small>
            </div>
        `;
        return;
    }

    // 按过滤器类型分组
    const groupedFiltered = groupFilteredEntitiesByType(filteredEntities);

    let html = '';
    for (const [filterType, entities] of Object.entries(groupedFiltered)) {
        html += `
            <div class="filtered-group">
                <div class="filtered-group-header">
                    <h4>
                        <i class="fas fa-filter"></i>
                        ${getFilterTypeDisplayName(filterType)}
                        <span class="count">(${entities.length})</span>
                    </h4>
                </div>
                <div class="filtered-group-content">
        `;

        entities.forEach(entity => {
            html += `
                <div class="filtered-item">
                    <div class="filtered-item-header">
                        <span class="filtered-entity">${entity.text}</span>
                        <span class="filter-type">${getFilterTypeDisplayName(entity.filter_type)}</span>
                    </div>
                    <div class="filtered-details">
                        <span class="entity-type-badge">${ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type}</span>
                        ${entity.context ? `<div class="filtered-context">上下文: "${entity.context}"</div>` : ''}
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    elements.filteredInfoList.innerHTML = html;
}

// 按过滤器类型分组
function groupFilteredEntitiesByType(filteredEntities) {
    const groups = {};

    filteredEntities.forEach(entity => {
        const filterType = entity.filter_type || 'unknown';
        if (!groups[filterType]) {
            groups[filterType] = [];
        }
        groups[filterType].push(entity);
    });

    return groups;
}

// 获取过滤器类型显示名称
function getFilterTypeDisplayName(filterType) {
    const typeMap = {
        'exact_match': '精确匹配过滤',
        'specialized_rules': '特殊实体规则过滤',
        'pattern_match': '模式匹配过滤',
        'context_filter': '上下文过滤',
        'length_filter': '长度过滤',
        'unknown': '未知过滤器'
    };

    return typeMap[filterType] || filterType;
}

// 为了兼容性，添加displayResults别名
function displayResults(results) {
    console.log('displayResults 被调用，结果数据:', results);

    if (!results) {
        console.error('displayResults: 结果数据为空');
        return;
    }

    const originalText = results.originalText || '';
    console.log(`调用 showResults，原文长度: ${originalText.length}`);

    if (results.originalText) {
        showResults(results, results.originalText);
    } else {
        showResults(results, '');
    }

    console.log('✓ displayResults 完成');
}
