#!/bin/bash
"""
HIPAA医疗数据脱敏服务 - Linux服务器Docker管理脚本

统一的Docker容器管理脚本，支持构建、运行、停止、监控等功能。
针对32核CPU Linux服务器优化。
"""

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
IMAGE_NAME="hipaa-deidentify"
CONTAINER_NAME="hipaa-deidentify-service"
MOUNT_DIR="/var/lib/docker/containers/hipaa-deidentify"
PORT=50505
WORKERS=5  # 针对32核CPU优化
FRONTEND_PORT=50506  # 前端服务端口
FRONTEND_PID_FILE="/tmp/hipaa-frontend.pid"
BACKEND_ONLY=false  # 是否仅启动后端服务

show_help() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}HIPAA医疗数据脱敏服务 Docker管理脚本${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "用法: $0 [命令] [选项]"
    echo -e ""
    echo -e "${GREEN}主要命令:${NC}"
    echo -e "  ${YELLOW}build${NC}     - 构建Docker镜像"
    echo -e "  ${YELLOW}run${NC}       - 运行后端容器服务"
    echo -e "  ${YELLOW}stop${NC}      - 停止容器服务"
    echo -e "  ${YELLOW}restart${NC}   - 重启容器服务"
    echo -e "  ${YELLOW}logs${NC}      - 查看服务日志"
    echo -e "  ${YELLOW}status${NC}    - 查看服务状态"
    echo -e "  ${YELLOW}test${NC}      - 测试服务连接"
    echo -e "  ${YELLOW}clean${NC}     - 清理容器和镜像"
    echo -e "  ${YELLOW}deploy${NC}    - 一键部署（构建+运行+前端）"
    echo -e ""
    echo -e "${GREEN}服务启动命令:${NC}"
    echo -e "  ${YELLOW}frontend${NC}  - 仅启动前端演示页面"
    echo -e "  ${YELLOW}stop-frontend${NC} - 停止前端服务"
    echo -e "  ${YELLOW}full-start${NC} - 启动后端+前端完整服务"
    echo -e "  ${YELLOW}full-stop${NC}  - 停止后端+前端完整服务"
    echo -e ""
    echo -e "${GREEN}监控命令:${NC}"
    echo -e "  ${YELLOW}stats${NC}     - 查看资源使用统计"
    echo -e "  ${YELLOW}health${NC}    - 健康检查"
    echo -e "  ${YELLOW}shell${NC}     - 进入容器Shell"
    echo -e ""
    echo -e "${GREEN}配置选项:${NC}"
    echo -e "  --workers N      设置工作进程数量 (默认: ${WORKERS})"
    echo -e "  --port N         设置后端服务端口 (默认: ${PORT})"
    echo -e "  --frontend-port N 设置前端服务端口 (默认: ${FRONTEND_PORT})"
    echo -e "  --mount DIR      设置挂载目录 (默认: ${MOUNT_DIR})"
    echo -e "  --backend-only   仅启动后端服务，不启动前端"
    echo -e ""
    echo -e "${GREEN}示例:${NC}"
    echo -e "  $0                              # 一键部署服务（默认：后端+前端）"
    echo -e "  $0 deploy                       # 一键部署后端+前端服务"
    echo -e "  $0 deploy --backend-only        # 仅部署后端服务"
    echo -e "  $0 full-start                   # 启动完整服务（后端+前端）"
    echo -e "  $0 run --workers 5              # 使用5个工作进程运行后端"
    echo -e "  $0 run --port 50505             # 使用50505端口运行后端"
    echo -e "  $0 frontend --frontend-port 50506 # 使用50506端口启动前端"
    echo -e "  $0 logs                         # 查看实时日志"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --workers)
                WORKERS="$2"
                shift 2
                ;;
            --port)
                PORT="$2"
                shift 2
                ;;
            --frontend-port)
                FRONTEND_PORT="$2"
                shift 2
                ;;
            --mount)
                MOUNT_DIR="$2"
                shift 2
                ;;
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
        exit 1
    fi
}

# 检查Python是否安装
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python未安装，请先安装Python 3.6或更高版本${NC}"
        return 1
    fi
    echo -e "${BLUE}✓ 使用Python: $PYTHON_CMD${NC}"
    return 0
}

# 启动前端服务
start_frontend() {
    echo -e "${BLUE}🌐 启动前端演示页面...${NC}"
    echo -e "${YELLOW}前端端口: ${FRONTEND_PORT}${NC}"

    # 检查Python
    if ! check_python; then
        return 1
    fi

    # 检查前端目录和文件
    if [ ! -d "frontend" ]; then
        echo -e "${RED}❌ 未找到frontend目录${NC}"
        return 1
    fi

    if [ ! -f "frontend/start_frontend.py" ]; then
        echo -e "${RED}❌ 未找到前端启动脚本${NC}"
        return 1
    fi

    # 停止现有前端服务
    stop_frontend_service

    # 启动前端服务（后台运行）
    cd frontend
    echo -e "${YELLOW}⏳ 正在启动前端服务...${NC}"

    # 更新前端配置中的API地址
    update_frontend_config

    nohup $PYTHON_CMD start_frontend.py --port $FRONTEND_PORT --host 0.0.0.0 --no-browser > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$FRONTEND_PID_FILE"

    cd ..

}

# 更新前端配置
update_frontend_config() {
    echo -e "${BLUE}🔧 更新前端API配置...${NC}"

    # 更新script.js中的API地址
    if [ -f "script.js" ]; then
        # 使用sed替换API地址，确保使用50505端口
        sed -i.bak "s|DEIDENTIFY_API:.*|DEIDENTIFY_API: 'http://127.0.0.1:${PORT}/deidentify',|g" script.js
        echo -e "${GREEN}✓ API地址已更新为: http://127.0.0.1:${PORT}/deidentify${NC}"
    else
        echo -e "${YELLOW}⚠️ 未找到script.js文件${NC}"
    fi
}

# 停止前端服务
stop_frontend_service() {
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo -e "${YELLOW}⚠️ 停止现有前端服务 (PID: $FRONTEND_PID)...${NC}"
            kill $FRONTEND_PID 2>/dev/null || true
            sleep 2
            # 强制杀死如果还在运行
            if kill -0 $FRONTEND_PID 2>/dev/null; then
                kill -9 $FRONTEND_PID 2>/dev/null || true
            fi
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi

    # 额外检查：杀死所有可能的前端进程
    pkill -f "start_frontend.py" 2>/dev/null || true

    echo -e "${GREEN}✓ 前端服务已停止${NC}"
}

# 创建挂载目录
create_directories() {
    echo -e "${BLUE}📁 创建挂载目录: ${MOUNT_DIR}${NC}"
    sudo mkdir -p "$MOUNT_DIR"
    sudo chown -R $USER:$USER "$MOUNT_DIR"
    echo -e "${GREEN}✓ 挂载目录创建成功${NC}"
}

# 构建Docker镜像
build_image() {
    echo -e "${BLUE}🔨 开始构建Docker镜像...${NC}"
    echo -e "${YELLOW}镜像名称: ${IMAGE_NAME}${NC}"

    docker build -t "$IMAGE_NAME" .

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Docker镜像构建成功${NC}"
    else
        echo -e "${RED}❌ Docker镜像构建失败${NC}"
        exit 1
    fi
}

# 停止并删除容器
stop_container() {
    if docker ps -a --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        echo -e "${YELLOW}⚠️ 停止现有容器...${NC}"
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
        echo -e "${GREEN}✓ 容器已停止并删除${NC}"
    else
        echo -e "${BLUE}ℹ️ 没有发现运行中的容器${NC}"
    fi
}

# 运行容器
run_container() {
    echo -e "${BLUE}🚀 启动HIPAA脱敏服务容器...${NC}"
    echo -e "${YELLOW}容器名称: ${CONTAINER_NAME}${NC}"
    echo -e "${YELLOW}服务端口: ${PORT}${NC}"
    echo -e "${YELLOW}工作进程: ${WORKERS}${NC}"
    echo -e "${YELLOW}挂载目录: ${MOUNT_DIR}${NC}"
    echo -e "${YELLOW}网络模式: 宿主机网络 (host)${NC}"

    stop_container
    create_directories

    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        --network host \
        -v "$MOUNT_DIR:/app/data" \
        -e HOST=0.0.0.0 \
        -e PORT=${PORT} \
        -e WORKERS=${WORKERS} \
        -e LOG_LEVEL=info \
        -e PYTHONUNBUFFERED=1 \
        "$IMAGE_NAME" \
        python -c "
import uvicorn
uvicorn.run(
    'api.main:app',
    host='0.0.0.0',
    port=${PORT},
    workers=${WORKERS},
    log_level='info',
    access_log=True,
    loop='uvloop',
    http='httptools',
    reload=False,
    proxy_headers=True,
    forwarded_allow_ips='*'
)"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 容器启动成功${NC}"
        echo -e "${GREEN}服务地址: http://localhost:${PORT} (宿主机网络)${NC}"
        echo -e "${GREEN}API文档: http://localhost:${PORT}/docs${NC}"
        echo -e "${BLUE}ℹ️ 使用宿主机网络，所有端口直接在宿主机可用${NC}"

        # 等待服务启动
        echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
        sleep 3

        # 检查服务状态
        if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "${CONTAINER_NAME}.*Up"; then
            echo -e "${GREEN}✓ 服务运行正常${NC}"
        else
            echo -e "${RED}❌ 服务启动可能有问题，请查看日志${NC}"
        fi
    else
        echo -e "${RED}❌ 容器启动失败${NC}"
        exit 1
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}📋 查看服务日志 (Ctrl+C 退出)${NC}"
    docker logs -f "$CONTAINER_NAME"
}

# 重启服务
restart_service() {
    echo -e "${BLUE}🔄 重启服务...${NC}"
    stop_container
    sleep 2
    run_container
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}📊 服务状态信息${NC}"
    echo -e "${BLUE}========================================${NC}"

    if docker ps --format 'table {{.Names}}\t{{.Status}}\t{{Ports}}' | grep -q "$CONTAINER_NAME"; then
        echo -e "${GREEN}✓ 容器运行状态:${NC}"
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "$CONTAINER_NAME"

        echo -e "\n${GREEN}✓ 容器详细信息:${NC}"
        docker inspect "$CONTAINER_NAME" --format='
容器ID: {{.Id}}
镜像: {{.Config.Image}}
创建时间: {{.Created}}
启动时间: {{.State.StartedAt}}
重启次数: {{.RestartCount}}
网络模式: {{.HostConfig.NetworkMode}}
端口映射: {{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{(index $conf 0).HostPort}}{{end}}'
    else
        echo -e "${RED}❌ 容器未运行${NC}"
    fi
}

# 查看资源使用统计
show_stats() {
    echo -e "${BLUE}📈 资源使用统计${NC}"
    if docker ps --format '{{.Names}}' | grep -q "$CONTAINER_NAME"; then
        docker stats --no-stream "$CONTAINER_NAME"
    else
        echo -e "${RED}❌ 容器未运行${NC}"
    fi
}

# 健康检查
health_check() {
    echo -e "${BLUE}🏥 健康检查${NC}"
    if curl -s "http://localhost:${PORT}/health" > /dev/null; then
        echo -e "${GREEN}✓ 服务健康检查通过${NC}"
        curl -s "http://localhost:${PORT}/health" | python -m json.tool
    else
        echo -e "${RED}❌ 服务健康检查失败${NC}"
        echo -e "${YELLOW}请检查服务是否正在运行: $0 status${NC}"
    fi
}

# 测试服务连接
test_service() {
    echo -e "${BLUE}🧪 测试服务连接${NC}"

    # 测试健康检查接口
    echo -e "${YELLOW}测试健康检查接口...${NC}"
    if curl -s "http://localhost:${PORT}/health" > /dev/null; then
        echo -e "${GREEN}✓ 健康检查接口正常${NC}"
    else
        echo -e "${RED}❌ 健康检查接口失败${NC}"
        return 1
    fi

    # 测试脱敏接口
    echo -e "${YELLOW}测试脱敏接口...${NC}"
    test_result=$(curl -s -X POST "http://localhost:${PORT}/deidentify" \
        -H "Content-Type: application/json" \
        -d '{"text": "患者张三，身份证110101199001011234"}')

    if echo "$test_result" | grep -q "deidentified_text"; then
        echo -e "${GREEN}✓ 脱敏接口正常${NC}"
        echo -e "${BLUE}测试结果:${NC}"
        echo "$test_result" | python -m json.tool | head -10
    else
        echo -e "${RED}❌ 脱敏接口失败${NC}"
        echo "$test_result"
        return 1
    fi
}

# 进入容器Shell
enter_shell() {
    echo -e "${BLUE}🐚 进入容器Shell${NC}"
    if docker ps --format '{{.Names}}' | grep -q "$CONTAINER_NAME"; then
        docker exec -it "$CONTAINER_NAME" bash
    else
        echo -e "${RED}❌ 容器未运行${NC}"
    fi
}

# 清理容器和镜像
clean_all() {
    echo -e "${YELLOW}⚠️ 这将删除容器和镜像，确定继续吗? (y/N)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🧹 清理容器和镜像...${NC}"
        stop_container

        if docker images --format '{{.Repository}}' | grep -q "$IMAGE_NAME"; then
            docker rmi "$IMAGE_NAME"
            echo -e "${GREEN}✓ 镜像已删除${NC}"
        fi

        echo -e "${GREEN}✓ 清理完成${NC}"
    else
        echo -e "${BLUE}取消清理操作${NC}"
    fi
}

# 一键部署（后端+前端）
deploy_service() {
    if [[ "$BACKEND_ONLY" == "true" ]]; then
        echo -e "${BLUE}🚀 开始一键部署HIPAA脱敏后端服务${NC}"
        echo -e "${YELLOW}模式: 仅后端服务${NC}"
    else
        echo -e "${BLUE}🚀 开始一键部署HIPAA脱敏完整服务${NC}"
        echo -e "${YELLOW}模式: 后端+前端服务${NC}"
    fi
    echo -e "${BLUE}========================================${NC}"

    check_docker
    build_image
    run_container

    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}🎉 后端服务部署完成！${NC}"
    echo -e "${GREEN}后端服务地址: http://localhost:${PORT}${NC}"
    echo -e "${GREEN}API文档: http://localhost:${PORT}/docs${NC}"

    # # 运行后端测试
    # echo -e "${YELLOW}运行后端服务测试...${NC}"
    # sleep 5
    # test_service

    # 根据参数决定是否启动前端
    if [[ "$BACKEND_ONLY" == "true" ]]; then
        echo -e "${BLUE}========================================${NC}"
        echo -e "${GREEN}🎉 后端服务部署成功！${NC}"
        echo -e "${GREEN}后端API: http://localhost:${PORT}${NC}"
        echo -e "${GREEN}API文档: http://localhost:${PORT}/docs${NC}"
        echo -e "${BLUE}========================================${NC}"

        echo -e "${YELLOW}📋 使用说明:${NC}"
        echo -e "  1. 后端API可供其他应用调用"
        echo -e "  2. 查看API文档了解接口详情"
        echo -e "  3. 如需前端页面，运行: $0 frontend"
        echo -e ""
        echo -e "${YELLOW}🔧 管理命令:${NC}"
        echo -e "  $0 status        # 查看服务状态"
        echo -e "  $0 logs          # 查看后端日志"
        echo -e "  $0 frontend      # 启动前端页面"
        echo -e "  $0 stop          # 停止后端服务"
    else
        echo -e "${BLUE}========================================${NC}"
        echo -e "${BLUE}🌐 启动前端演示页面...${NC}"

        # 启动前端服务
        if start_frontend; then
            echo -e "${BLUE}========================================${NC}"
            echo -e "${GREEN}🎉 完整服务部署成功！${NC}"
            echo -e "${GREEN}后端API: http://localhost:${PORT}${NC}"
            echo -e "${GREEN}前端页面: http://localhost:${FRONTEND_PORT}${NC}"
            echo -e "${GREEN}API文档: http://localhost:${PORT}/docs${NC}"
            echo -e "${BLUE}========================================${NC}"

            echo -e "${YELLOW}📋 使用说明:${NC}"
            echo -e "  1. 访问前端页面进行脱敏演示"
            echo -e "  2. 后端API可供其他应用调用"
            echo -e "  3. 查看API文档了解接口详情"
            echo -e ""
            echo -e "${YELLOW}🔧 管理命令:${NC}"
            echo -e "  $0 status        # 查看服务状态"
            echo -e "  $0 logs          # 查看后端日志"
            echo -e "  tail -f frontend.log  # 查看前端日志"
            echo -e "  $0 full-stop     # 停止所有服务"
        else
            echo -e "${YELLOW}⚠️ 前端服务启动失败，但后端服务正常运行${NC}"
            echo -e "${GREEN}后端服务地址: http://localhost:${PORT}${NC}"
        fi
    fi
}

# 启动完整服务（后端+前端）
start_full_service() {
    echo -e "${BLUE}🚀 启动完整HIPAA脱敏服务${NC}"
    echo -e "${BLUE}========================================${NC}"

    # 启动后端容器
    run_container

    # 等待后端服务稳定
    sleep 5

    # 启动前端服务
    if start_frontend; then
        echo -e "${BLUE}========================================${NC}"
        echo -e "${GREEN}🎉 完整服务启动成功！${NC}"
        echo -e "${GREEN}后端API: http://localhost:${PORT}${NC}"
        echo -e "${GREEN}前端页面: http://localhost:${FRONTEND_PORT}${NC}"
        echo -e "${BLUE}========================================${NC}"
    else
        echo -e "${YELLOW}⚠️ 前端服务启动失败，但后端服务正常运行${NC}"
    fi
}

# 停止完整服务（后端+前端）
stop_full_service() {
    echo -e "${BLUE}🛑 停止完整HIPAA脱敏服务${NC}"
    echo -e "${BLUE}========================================${NC}"

    # 停止前端服务
    stop_frontend_service

    # 停止后端容器
    stop_container

    echo -e "${GREEN}✓ 所有服务已停止${NC}"
}

# 主程序
main() {
    # 解析参数
    parse_args "$@"

    # 获取命令
    command="$1"
    shift || true

    # 检查Docker（除了help命令）
    if [[ "$command" != "help" && "$command" != "" ]]; then
        check_docker
    fi

    case "$command" in
        build)
            build_image
            ;;
        run)
            run_container
            ;;
        stop)
            stop_container
            ;;
        restart)
            restart_service
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        stats)
            show_stats
            ;;
        health)
            health_check
            ;;
        test)
            test_service
            ;;
        shell)
            enter_shell
            ;;
        clean)
            clean_all
            ;;
        deploy)
            deploy_service
            ;;
        frontend)
            start_frontend
            ;;
        stop-frontend)
            stop_frontend_service
            ;;
        full-start)
            start_full_service
            ;;
        full-stop)
            stop_full_service
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            if [[ "$BACKEND_ONLY" == "true" ]]; then
                echo -e "${BLUE}🚀 默认执行一键部署（仅后端）${NC}"
            else
                echo -e "${BLUE}🚀 默认执行一键部署（后端+前端）${NC}"
            fi
            deploy_service
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $command${NC}"
            echo -e "${YELLOW}使用 '$0 help' 查看帮助${NC}"
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
