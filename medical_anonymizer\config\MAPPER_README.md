# 医疗数据脱敏系统 - 配置映射器模块

## 📋 概述

配置映射器模块是动态配置系统的核心组件，负责将JSON配置格式转换为系统内部使用的`RecognizerConfig`格式，实现前端配置与后端实现的完全解耦。

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    JSON配置文件                              │
│              recognizer_config.json                        │
│         (前端友好的配置格式)                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   ConfigMapper                              │
│              配置映射和转换引擎                              │
│         • 格式验证  • 类型转换  • 错误处理                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                RecognizerConfig对象                         │
│              系统内部配置格式                                │
│         (与patterns.py完全兼容)                             │
└─────────────────────────────────────────────────────────────┘
```

### 文件结构

```
medical_anonymizer/config/
├── config_mapper.py           # 配置映射器核心实现
├── config_manager.py          # 高级配置管理功能
├── recognizer_config.json     # JSON配置文件
├── README.md                  # 配置系统总体文档
└── MAPPER_README.md           # 本文档
```

## 🔧 核心功能

### 1. JSON配置加载和验证

```python
from medical_anonymizer.config.config_mapper import ConfigMapper

# 创建配置映射器
mapper = ConfigMapper()

# 加载和验证JSON配置
success = mapper.load_config()
if success:
    print("✅ 配置加载成功")
else:
    print("❌ 配置加载失败")
```

### 2. 配置格式转换

**输入格式 (JSON)**:
```json
{
  "recognizers": {
    "identityDocuments": {
      "recognizers": {
        "certificates": {
          "name": "certificates",
          "displayName": "各类证件",
          "category": "contextual",
          "enabled": true,
          "positiveKeywords": ["护照", "驾照", "军官证"],
          "negativeKeywords": ["假护照"],
          "sources": ["CertificateRecognizer"]
        }
      }
    }
  }
}
```

**输出格式 (RecognizerConfig)**:
```python
RecognizerConfig(
    name="CertificateRecognizer",
    enabled=True,
    context_config=ContextConfig(
        positive_keywords=["护照", "驾照", "军官证"],
        negative_keywords=["假护照"],
        validation_level=ValidationLevel.MEDIUM
    )
)
```

### 3. 动态配置更新

```python
# 重新加载配置
mapper.reload_config()

# 获取特定识别器配置
config = mapper.get_recognizer_config("CertificateRecognizer")

# 验证配置完整性
is_valid = mapper.validate_config()
```

## 📊 配置映射规则

### 识别器类型映射

| JSON Category | 系统类型 | 映射规则 |
|---------------|----------|----------|
| `nlp_recognizer` | NLP识别器 | 只使用enabled字段 |
| `contextual` | 上下文识别器 | 使用完整配置参数 |
| `rule_based` | 规则识别器 | 只使用enabled字段 |

### 字段映射关系

| JSON字段 | RecognizerConfig字段 | 转换规则 |
|----------|---------------------|----------|
| `enabled` | `enabled` | 直接映射 |
| `positiveKeywords` | `context_config.positive_keywords` | 数组转换 |
| `negativeKeywords` | `context_config.negative_keywords` | 数组转换 |
| `sources` | `name` | 多源映射支持 |

### 多源识别器映射

一个JSON配置项可以映射到多个系统识别器：

```json
{
  "certificates": {
    "sources": ["CertificateRecognizer", "PassportRecognizer", "DriverLicenseRecognizer"]
  }
}
```

映射结果：
- `CertificateRecognizer` → 相同配置
- `PassportRecognizer` → 相同配置  
- `DriverLicenseRecognizer` → 相同配置

## 🔍 使用示例

### 基础使用

```python
from medical_anonymizer.config.config_mapper import create_config_mapper, load_and_apply_config

# 方式1: 使用工厂函数
mapper = create_config_mapper()

# 方式2: 直接创建实例
mapper = ConfigMapper("custom_config.json")

# 方式3: 一键加载和应用配置
success = load_and_apply_config()
```

### 高级使用

```python
# 获取映射统计信息
stats = mapper.get_mapping_statistics()
print(f"映射的识别器数量: {stats['mapped_recognizers']}")
print(f"启用的识别器数量: {stats['enabled_recognizers']}")

# 获取特定配置
config = mapper.get_recognizer_config("WeChatRecognizer")
if config:
    print(f"微信识别器状态: {'启用' if config.enabled else '禁用'}")

# 验证配置文件
validation_result = mapper.validate_config()
if validation_result['valid']:
    print("✅ 配置文件验证通过")
else:
    print(f"❌ 配置验证失败: {validation_result['errors']}")
```

### 错误处理

```python
try:
    mapper = ConfigMapper("config.json")
    success = mapper.load_config()
    
    if not success:
        # 获取详细错误信息
        errors = mapper.get_last_errors()
        for error in errors:
            print(f"配置错误: {error}")
            
except FileNotFoundError:
    print("配置文件不存在")
except json.JSONDecodeError as e:
    print(f"JSON格式错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 🧪 配置验证

### 必需字段验证

配置映射器会验证以下必需字段：

```python
# 识别器必需字段
required_fields = [
    "name",           # 识别器名称
    "displayName",    # 显示名称
    "category",       # 识别器类别
    "enabled",        # 启用状态
    "sources"         # 源识别器列表
]

# 上下文识别器额外字段
contextual_fields = [
    "positiveKeywords",  # 正面关键词（可选）
    "negativeKeywords"   # 负面关键词（可选）
]
```

### 数据类型验证

```python
# 类型验证规则
type_validation = {
    "enabled": bool,
    "positiveKeywords": list,
    "negativeKeywords": list,
    "sources": list,
    "category": str
}
```

## 📈 性能优化

### 配置缓存

```python
# 配置映射结果会被缓存
mapper.load_config()  # 第一次加载，执行映射
config1 = mapper.get_recognizer_config("WeChatRecognizer")  # 从缓存获取
config2 = mapper.get_recognizer_config("WeChatRecognizer")  # 从缓存获取
```

### 延迟加载

```python
# 支持延迟加载机制
mapper = ConfigMapper()  # 不立即加载配置
# ... 其他初始化工作
mapper.load_config()     # 需要时才加载
```

### 增量更新

```python
# 支持增量配置更新
mapper.update_recognizer_config("WeChatRecognizer", {
    "enabled": False,
    "positiveKeywords": ["微信", "WeChat", "wx"]
})
```

## 🔧 扩展和自定义

### 自定义映射规则

```python
class CustomConfigMapper(ConfigMapper):
    def custom_mapping_rule(self, json_config, recognizer_name):
        """自定义映射规则"""
        # 实现自定义映射逻辑
        pass
    
    def validate_custom_fields(self, config):
        """自定义字段验证"""
        # 实现自定义验证逻辑
        pass
```

### 配置格式扩展

```python
# 支持扩展JSON配置格式
{
  "recognizers": {
    "customCategory": {
      "recognizers": {
        "customRecognizer": {
          "customField": "customValue",  # 自定义字段
          "enabled": true
        }
      }
    }
  }
}
```

## 🚨 注意事项

### 配置兼容性

- ✅ 向后兼容现有的`patterns.py`配置
- ✅ 支持渐进式迁移到JSON配置
- ✅ 保持与现有代码的接口兼容

### 错误处理

- 🔍 详细的错误日志和诊断信息
- 🛡️ 配置加载失败时的回退机制
- ⚠️ 配置验证失败时的警告提示

### 性能考虑

- ⚡ 配置映射结果缓存
- 🚀 按需加载和延迟初始化
- 📊 内存使用优化

---

**版本**: 2.0.0  
**最后更新**: 2024-12-28  
**维护者**: 张崇文
