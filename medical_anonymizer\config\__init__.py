"""
医疗数据脱敏系统动态配置模块

本模块提供完整的动态配置管理功能，包括：
1. JSON配置文件的加载和解析
2. 配置格式的映射和转换
3. 配置的动态更新和热重载
4. 配置的备份和恢复
5. 配置变更的监听和通知

主要组件：
- ConfigMapper: 配置映射器，负责JSON到内部格式的转换
- ConfigManager: 配置管理器，提供高级配置管理功能
- 配置文件: recognizer_config.json，包含所有识别器和过滤器配置

使用示例：
```python
from medical_anonymizer.config import (
    get_config_manager, 
    initialize_config_system,
    load_and_apply_config
)

# 初始化配置系统
initialize_config_system()

# 获取配置管理器
manager = get_config_manager()

# 启用/禁用识别器
manager.enable_recognizer("WeChatRecognizer")
manager.disable_recognizer("QQNumberRecognizer")

# 获取识别器状态
status = manager.get_recognizer_status()

# 创建配置备份
backup_path = manager.create_backup()

# 重新加载配置
manager.reload_config()
```
"""

from .config_mapper import (
    ConfigMapper,
    create_config_mapper,
    load_and_apply_config
)

from .config_manager import (
    ConfigManager,
    get_config_manager,
    initialize_config_system
)

# 版本信息
__version__ = "1.0.0"

# 导出的主要接口
__all__ = [
    # 配置映射器
    "ConfigMapper",
    "create_config_mapper",
    "load_and_apply_config",
    
    # 配置管理器
    "ConfigManager", 
    "get_config_manager",
    "initialize_config_system",
    
    # 版本信息
    "__version__"
]


def get_version() -> str:
    """获取配置模块版本"""
    return __version__


def get_default_config_path() -> str:
    """获取默认配置文件路径"""
    from pathlib import Path
    return str(Path(__file__).parent / "recognizer_config.json")


def quick_setup(config_file_path: str = None) -> bool:
    """
    快速设置配置系统
    
    Args:
        config_file_path: 配置文件路径，如果为None则使用默认路径
        
    Returns:
        是否成功设置
    """
    try:
        # 初始化配置系统
        success = initialize_config_system(config_file_path)
        
        if success:
            print("✅ 动态配置系统初始化成功")
            
            # 获取配置管理器并显示统计信息
            manager = get_config_manager()
            stats = manager.get_statistics()
            
            print(f"📊 配置统计信息:")
            print(f"   - 总识别器数量: {stats.get('total_recognizers', 0)}")
            print(f"   - 启用识别器数量: {stats.get('enabled_recognizers', 0)}")
            print(f"   - 禁用识别器数量: {stats.get('disabled_recognizers', 0)}")
            
            layer_stats = stats.get('layer_stats', {})
            print(f"   - 规则识别器: {layer_stats.get('rule', 0)}")
            print(f"   - 上下文识别器: {layer_stats.get('context', 0)}")
            print(f"   - NLP识别器: {layer_stats.get('nlp', 0)}")
            
            print(f"📁 配置文件: {stats.get('config_file', 'N/A')}")
            
        else:
            print("❌ 动态配置系统初始化失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 快速设置失败: {e}")
        return False


def show_recognizer_status():
    """显示所有识别器的状态"""
    try:
        manager = get_config_manager()
        status = manager.get_recognizer_status()
        
        if not status:
            print("❌ 无法获取识别器状态")
            return
        
        print("\n📋 识别器状态列表:")
        print("-" * 80)
        
        # 按类别分组显示
        categories = {}
        for recognizer_name, recognizer_info in status.items():
            category = recognizer_info.get("category", "unknown")
            if category not in categories:
                categories[category] = []
            categories[category].append((recognizer_name, recognizer_info))
        
        for category, recognizers in categories.items():
            print(f"\n🏷️  {category.upper()}:")
            
            for recognizer_name, info in recognizers:
                enabled_status = "✅" if info.get("enabled", True) else "❌"
                layer = info.get("layer", "rule").upper()
                display_name = info.get("displayName", recognizer_name)
                
                print(f"   {enabled_status} [{layer:7}] {recognizer_name:25} - {display_name}")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ 显示识别器状态失败: {e}")


def demo_config_operations():
    """演示配置操作功能"""
    try:
        print("\n🚀 动态配置系统演示")
        print("=" * 50)
        
        # 初始化配置系统
        if not quick_setup():
            return
        
        manager = get_config_manager()
        
        # 显示当前状态
        show_recognizer_status()
        
        # 演示配置操作
        print("\n🔧 演示配置操作:")
        
        # 禁用一个识别器
        print("   1. 禁用 QQNumberRecognizer...")
        if manager.disable_recognizer("QQNumberRecognizer"):
            print("      ✅ 成功禁用")
        else:
            print("      ❌ 禁用失败")
        
        # 启用一个识别器
        print("   2. 启用 WeChatRecognizer...")
        if manager.enable_recognizer("WeChatRecognizer"):
            print("      ✅ 成功启用")
        else:
            print("      ❌ 启用失败")
        
        # 创建备份
        print("   3. 创建配置备份...")
        try:
            backup_path = manager.create_backup("demo_backup")
            print(f"      ✅ 备份创建成功: {backup_path}")
        except Exception as e:
            print(f"      ❌ 备份创建失败: {e}")
        
        # 列出备份
        print("   4. 列出所有备份...")
        backups = manager.list_backups()
        if backups:
            print(f"      📁 找到 {len(backups)} 个备份文件:")
            for backup in backups[:3]:  # 只显示前3个
                print(f"         - {backup['name']} ({backup['size']} bytes)")
        else:
            print("      📁 没有找到备份文件")
        
        print("\n✅ 演示完成!")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    # 如果直接运行此模块，则执行演示
    demo_config_operations()
