#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
医疗数据脱敏系统 - 性能对比测试

本脚本对比单线程逐个处理与多线程并行处理的性能差异，
测试不同线程数量的表现，并验证结果一致性。
"""

import sys
import os
import time
import statistics
from typing import List, Dict, Any
import psutil

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_anonymizer import MedicalAnonymizer


# 测试数据：包含各种敏感信息类型的医疗文本
TEST_TEXTS = [
    """
    患者张三，男，35岁，汉族，身份证号110101199001011234，
    联系电话13812345678，微信号zhang_san_123，
    病案号MR20230001，住院号IP20230001，
    住址北京市朝阳区建国门外大街123号。
    
INNOCARE 知情同意书
方案编号：ICP-CL-00123
中办方：北京诺诚健华医药科技有限公司
一项评价奥布替尼联合来那度胺和利妥昔单抗（^²）对比安慰剂联合R^{
治疗复发/难治性边缘区淋巴瘤有效性和安全性的随机、对照、双盲、
IⅢII期临床试验
研究医生： 水
研究机构名称：中国医科大学附属第一医院
研究机构地址：沈阳市和平区南京北街155号
联系电话： 18290200366
研究参与者筛选号： 13001
知情同意书
尊敬的先生/女士：
您好！首先感谢您有意参加本项临床研究。本研究是为了评估奥布替尼联合来那
度胺和利妥昔单抗（^2}）对比安慰剂联合R^2治疗复发/难治性边缘区淋巴瘤患者的疗效
和安全性。
在您决定是否参加之前，请仔细阅读下面的信息，如果您愿意也可以和您的亲人
或朋友商量。如果您有任何不清楚的地方，请直接询问您的医生。
如果您了解了本研究、并自愿参加，您需要签署本研究的知情同意书。签署知情
同意书不会改变您的合法权益，仅表示您已经理解了这些信息并自愿参加本研究。
【研究背景】
边缘区淋巴瘤（MZL）是一组异质性的惰性非霍奇金淋巴瘤，约占所有非霍奇金
淋巴瘤的7~8%。边缘区淋巴瘤通常对各种免疫化疗敏感，但常规疗法仍无法将其治愈。
大多数MZL患者最终会复发，疾病的持续存在或反复复发导致严重的并发症和死亡。
复发/难治性MZL患者，目前仍缺乏公认的标准治疗方法，尤其是缺乏优效的靶向药物。
近年来，布鲁顿氏酪氨酸激酶（BTK）抑制剂已在B细胞恶性疾病中获得了广泛的使
用，在国内已获批用于治疗慢性淋巴细胞白血病/小淋巴细胞淋巴瘤和华氏巨球蛋白血
症。有研究证明，奥布替尼、伊布替尼和泽布替尼治疗复发/难治性MZL患者获得了一
定的疗效，其中奥布替尼已国内获得国家药品监督管理局（NMPA）的批准，用于既
往至少接受过一种治疗的边缘区淋巴瘤（MZL）患者的治疗；泽布替尼也已获得国外
<中国医科大学附属第一医院>专用版，版本号：V2.0，版本日期：2024年03月15日：
<ICP-CL-00123>知情同意书，版本号：V2.0，版本日期：2024年03月08日 第1页，共18页
SOP编号：CL-PM-SOP-003a 版本：4.0
8 扫描全能王创建
INFO:__main__:OCR API调用耗时: 1.02秒
INFO:__main__:正在处理文件: 8797-医大一-13001-访视病历(1).pdf (类型: pdf)
INFO:__main__:开始处理PDF文件: D:\data\脱敏用\36张\8797-医大一-13001-访视病历(1).pdf，共13页

    """,
    """
a 日 INNOCARE 知情同意书
① 0
0 8
知情同意书签字页
研究参与者声明：
我已阅读并理解了本知情同意书的信息，并就本研究和研究医生进行了讨论。
我已经了解研究所涉及的所有程序，包括任何目前研究人员所能够了解到的、已
知或预期的不便、风险、不适或潜在的副作用，以及它们各自所代表的临床含义。
我了解并且同意，如果我参与此项研究，就意味着我允许研究人员将能够查看我
的医疗记录。
我自愿选择参加本研究，我也了解我可以随时退出。该决定不会影响我未来的治
疗或其他受益。
我还明白，本研究是严格保密的。
我已获知，我将收到一份已签名并注明日期的本同意书副本。
我已经经过充分考虑，我所有的问题都得到了满意的回答。在此，我同意参加
<ICP-CL-00123>研究。
我明白，我可以为如下选项做出选择，勾选“是”表示我同意，勾选“否”表示
我不同意。
我愿意参加药代动力学采血和研究。 晋风 □否
通过以下签字，我允许收集我的个人信息作为本临床研究的一部分：
仅使用我的研究参与者编号识别；申办者及其授权代表出于研究方案中描述的目
的进行审查、处理和转移；由适当授权机构进行审查和稽查；发表并发送给中国或其
他国家/地区的监管机构或医疗保险公司；和如果需要，转移至数据保护法可能不太严
格的国家。
工
如本隐私声明所述，可以收集、使用和存档以进行研究；在法规允许的范围内，
转移至中国境外的其他公司并与之共享；转移至第三方和其他公司并与之共享。 彩萍
魏萍 2024年106月121日14时5分
研究参与者正楷/印刷体姓名 签名 日期/时间（24小时制）
__年___月_日_时__分
监护人正楷/印刷体姓名 签名 日期/时间（24小时制）
（研究参与者为无民事行为能力或不完全民事行为能力）
监护人与研究参与者的关系（请在方框内打钩）：口父母口配偶口子女口其他
<中国医科大学附属第一医院>专用版，版本号：V2.0，版本日期：2024年03月15日；
<ICP-CL-00123>知情同意书，版本号：V2.0，版本日期：2024年03月08日 第17页，共18页
SOP编号：CL-PM-SOP-003a 版本：4.0
C 扫描全能王创建
    患者信息：
    姓名：李明
    性别：男
    年龄：45岁
    身份证号：320101197801011234
    联系电话：13912345678
    微信号：liming_2023
    病案号：MR20240001
    住院号：IP20240001
    """,
    """
科室：门诊血液内科 医生：张蕊 就诊时间：2024-06-2115:09 第1贝/共6贝
主诉：确诊malt淋巴瘤16年，右眼脸肿胀半年
现病史：患者于16年前（2008年）因尿频就诊于我院泌尿外科行膀胱肿物切除术，术后病理（382721）提示：伴有
Castleman病变的滤泡树突细胞肉瘤。后于香港伊丽莎白医院会诊病理切片，结论为：低恶度B细胞淋巴瘤，符合结外
边缘区粘膜相关淋巴组织型B-细胞淋巴瘤（MALT），于我院应用R-CHOP方案6个疗程，完全缓解，至2009年12月停药。
半年前无明显诱因出现右眼脸肿胀，逐渐加重，1月前就诊于我院，完善PET-CT，后于2024-6-12住院，完善相关检
查，于2024-06-17行喉镜下肿物切除术并取病理，术后病理（B0808704）回报：（舌根（右））：非霍奇金B细胞淋巴瘤，
结合病史及免疫组化结果倾向边缘区淋巴瘤（MALT淋巴瘤），免疫组化结果：蜡块号：B0808704--：Bcl-2（+），MUM1（部
分+），K67（约2+），MNDA（+），CclinD1）c6-）CD1（），C20（+），CD21（+）CD23（FDC+），D，
CD30（-），CD5（），Pax-5（+）。分子病理结果：蜡块号：B0808704--：EBV阳性对照（有效）、EBV*-）。近期有吞咽困
难，有呼吸困难，无咳嗽、咳痰，无腹痛、腹泻，饮食差，睡眠欠佳，二便正常，体重无明显变化，45岁已绝经。
既往史：一般健康状况：良好，无药物、食物过敏史、否认高血压、冠心病及糖尿病病史。
家族史：-
药物过敏史：无
其他：2024.6.2114:10分在血液科门诊诊室，由本人张蕊医生对受试者讲解“一项评价奥布替尼联合来那度胺和利
妥昔单抗（R2）对比安慰剂联合R2治疗复发、难治性边缘区淋巴瘤有效性和安全性的随机、对照、双盲、II期临床试
验”（方案版本号：V1.0，版本日期：2023年6月28日）知情同意书详细信息、研究流程，并回答了受试者的所有疑
预约挂号网上复诊 门诊服务住院服务 续方购药邮寄到家
缴费预约 结果查询 医保服务健康资讯 预约检查来院就做
公众号 微网站 复诊全集
INFO:__main__:OCR API调用耗时: 1.51秒

    王芳，女，28岁，身份证110101199501011234，
    电话13800138000，微信wangfang_1995，
    病案号MR20240315001，门诊号OP20240315001，
    主治医师：张主任医师，科室：妇产科。
    """,
    """
    患者赵六，身份证号330101198801011234，
    联系方式：15912345678，
    微信账号：zhaoliu_88，
    病历编号：MR20240401，
    住院编号：IP20240401，
    床号：A301。
    """,
    """
Page1of1
中国医科大学附属第一医院 Page
报告单
名：魏萍 性别：女 年龄：61岁
病人编号：0006198969 检查编号：01240429190097 住院号：0006198969
检查日期：2024-05-04 报告日期：2024-05-04 病人来源：
检查方法及部位：：肺部
CT平扫+增强(64排)
检查所见：
扫描显示胸廓对称，双肺上叶部分分支支气管壁增厚，管腔不通畅，周围可见多发不规则
软组织密度条片影、结节影，右肺上叶为著，增强扫描可见轻中度强化，气管及双侧支气
管管壁不均匀增厚，管腔略窄，双肺可见索条影，双肺及叶间胸膜另可见多发微小结节
影，部分呈磨玻璃密度。右肺中叶近胸膜下区可见多发斑点影。双侧肺门略大，纵隔居
中，纵隔及双肺门淋巴结略增大。心脏大小正常。食管下段管壁局限性增厚。
检查结论： 以临猴 《病
Comix 双肺上叶支气管及周围多发病变及气管、双侧支气管壁增厚，考虑恶性可能大，请结合临
魏萍 床及相关检查。食管改变，请结合病史。右肺中叶多发斑点影，性质待定双肺及叶间 胸膜多发微小结节，密切随诊观察双肺陈旧病变。纵隔及双肺门淋巴络增大。 名
23A1084
C2
2024.8.6
13/中国医 报告医生：朱婧怡 审核医生：崔丽贺
*此报告仅供临床医生参考*
张蕊 2R
2024.7.1
Comix
htp://172445//RrtCntt.sx6
XIw
htp:/175
2024-

    申办方：北京某某制药有限公司
    主要研究者：陈医生（主任医师）
    联系方式：<EMAIL>
    研究中心：上海某某医院
    患者编号：P001，身份证：210101199201011234
    """,
    """
### 第15页
Page1 of1
上兴体屋笔一医院
Page1
中国医科大学附属第一医院
放射科核磁诊断报告单
名：魏萍 性别：女 年龄：61岁 门诊/住院
号：0006198969
病区： 病房： 床号： 检查
号：01240705212019
临床诊断：1：淋巴瘤 资源： 检查名称：颅脑MR平扫
+增强(3.0T)
检查所见：
双侧额叶皮层下、侧脑室周围可见散在斑点、片状长T2信号，Flair序列呈高信号，小脑、
脑干内未见异常信号影，形态结构未见异常，脑室系统等大对称，脑沟池裂无明显增宽，
中线结构居中。双侧泪腺增大，右侧为著，增强扫描强化明显。左侧咬肌外侧可见斑片状
等信号影，增强扫描可见明显强化。鼻中隔两侧，舌根后方-会厌上方可见稍长T1信号
.病史 疾
脑内小腔隙灶。I级脑白质疏松。双侧泪腺肿大强化；鼻中隔两侧、左侧咬肌外侧及会
厌上方强化灶，请结合临床及相关检查。
3024.7.9 2人
报告医生：张鑫报告日期：2024-07-08审核医生：张博审核日期：2024-07-08
Comix
Comix
htp:/17

    门诊病历：
    患者孙七，男，52岁，民族：汉族
    身份证：420101197001011234
    手机：18612345678
    微信：sunseven_70
    病案号：MR20240501
    诊断：高血压、糖尿病
    """,
    """
### 第14页
PET-CT
Page 1of1
中国医科大学附属第一医院
报告单
魏萍 性别：女 年龄：61岁
人编号：0006198969 检查编号：03240613193076 住院号：0006198969
检查日期：2024-06-13 报告日期：2024-06-14 病人来源：血液内科一病区
检查方法及部位：：肺部
HRCT(64排)
检查所见：
扫描显示胸廓对称，双肺上叶部分分支支气管壁增厚，管腔不通畅，周围可见多发不规则
软组织密度条片影、结节影，右肺上叶为著，气管及双侧支气管管壁不均匀增厚，管腔
窄，双肺可见索条影，双肺及叶间胸膜另可见多发微小结节影，部分呈磨玻璃密度。右肺
中叶近胸膜下区可见多发斑点影。双侧肺门略大，纵隔居中，纵隔及双肺门淋巴结略增
大。心脏大小正常。食管下段管壁局限性增厚，邻近右肺下叶可见斑片影。
强 C幽排
检查结论：
双肺上叶支气管及周围多发病变及气管、双侧支气管壁增厚，考虑恶性可能大，对比
2024-05-04前片未见明显变化，请结合临床及相关检查。食管改变，请结合病史。右肺中
叶多发斑点影，性质待定。双肺及叶间胸膜多发微小结节，密切随诊观察。双肺陈旧性 病史
病变。纵隔及双肺门淋巴结增大。
20
13/
张
报告医生：袁崇胜 审核医生：崔丽贺
*此报 供临床医生参考*
ComIx 2R 20n0.7.1
oMIX
htp:/1726241
    住院记录：
    姓名：周八，性别：女，年龄：38岁
    证件号码：510101198501011234
    联系电话：17712345678
    社交账号：zhouba_85
    医疗编号：MR20240601，住院号：IP20240601
    主管医生：刘主治医师
    """,
    """
检查方法：空腹4小时以上，静脉注射显像剂平静休息后行PET/CT体部+头部断层显像。
PET图像经衰减校正、迭代法重建后与CT图像进行融合，影像清晰。图像所见：PET示大脑
及小脑FDG摄取、分布未见异常。PET示鼻腔内FDG摄取增高影，最大SUV为6.5，CT示相应
部位软组织影；CT示甲状腺双叶密度不均，FDG摄取未见异常。PET示口咽双侧壁FDG摄取
增高，最大SUV为11.0，CT示相应部位软组织增厚；CT示右侧眼球外侧软组织增厚，FDG摄
取增高，最大SUV为5.2；PET示双侧下颌下腺FDG摄取增高影，最大SUV为4.5，CT示相应部
位未见异常。PET示左侧颌面部皮下及双侧腮腺区FDG摄取增高影，最大SUV为5.6，CT示相
应部位软组织影，最大径约17mm。PET示双颈部及双侧锁骨上FDG摄取增高影，最大SUV为
4.3，CT示相应部位淋巴结影，最大径约14mm。余颌面部及颈部软组织结构、形态及FDG摄
取、分布未见异常。CT示双肺多发结节影、软组织影及斑片影，部分与支气管分界不清，
FDG摄取增高，最大SUV为9.O。PET示主支气管及左右支气管管壁FDG摄取增高影，最大SUV
为4.8，CT示相应部位管壁增厚；PET示纵隔内及双肺门FDG摄取增高影，最大SUV为7.2，
CT示相应部位淋巴结影，最大径约16mm。心脏FDG摄取未见异常。CT示双侧乳腺组织致
密，局部呈结节状，FDG摄取弥漫略增高。CT示双侧腋窝淋巴结影，最大径约12mm，FDG摄
取略增高，最大SUV为2.4。食管FDG摄取未见异常。胃及十二指肠FDG摄取未见异常。CT示
胃小弯侧淋巴结影，最大径约10mm，FDG摄取增高，最大SUV为3.4。肝脏、胆囊FDG摄取未
见异常。脾脏FDG摄取未见异常。胰腺FDG摄取未见异常。CT示右肾低密度影，FDG摄取未
见异常；肾上腺、左肾FDG摄取未见异常。腹部见数个形态不一、条管状浓淡不一的肠
影。腹膜后区FDG摄取未见异常。膀胱内见FDG分布。子宫、附件区FDG摄取未见异常。PET
示左侧臀部皮下团块状FDG摄取增高影，最大SUV为7.2，CT示相应部位软组织影，最大径
约36mm。CT示视野内双上肢肌肉旁淋巴结影，最大径约9mm，FDG摄取增高，最大SUV为
3.7。PET示右侧第8后肋FDG摄取略增高影，最大SUV为2.6，CT示相应部位未见异常；视野
内余骨骼及骨髓FDG摄取未见异常。视野内无特殊。
诊断意见： 1.口咽双侧壁软组织增厚，代谢增高，(左侧领面部皮下及双侧腮腺区、左侧臀部皮下软 以
84-2 组织影，代谢增高。恶性病变待除外；右侧眼球外侧软组织增厚，代谢增高，鼻腔内软组
丙双上肢肌肉旁、胃小弯侧淋巴结影，代谢增高，恶性病变不除外；双侧腋窝淋巴结代谢
略增高，建议密切复查；3.双肺多发结节影、软组织影及斑片影，部分与支气管分界本 清，代谢增高，主支气管及左右支气管管壁增厚，代谢增高，恶性病变累及不除外4.右 )病.
.6 侧第8后肋代谢略增高影，建议密切度5.甲状腺双叶密度不均；双侧下领下腺代谢增 高，炎性改变不除外；双侧乳腺组织致密，局部呈结节状，代谢弥漫略增高，建议定期复
查；右肾低密度影；6.视野内余部未见异常，请结合临床。 as
国医
n/ 2R
2024.7.1
    体检报告：
    受检者：吴九，身份证号：610101199001011234
    联系方式：19812345678
    微信号：wujiu_90
    体检编号：PE20240701
    检查医院：广州某某医院
    体检医生：黄医生
    """,
    """
报告医生：报告日期：2024-04-23审核医生：审核日期：2024-04-23
谢
http://172260445/RIS3/RorContnt.aspxOID=56494&USERID272321

### 第17页
中国医科大学附属第一医院核医学科
PET/CT检查报告
PET/CT号：P070019 登记号： 0006198969 检查日期：2024.04.22
姓名：魏萍 性别： 女 年龄：61岁
身高： 160.00 cm 体重： 53.00 kg 血糖： 5.10 mmol/l
显像剂： 8F-FDG 活度： 6 mCi 给药途径：静脉注射
注射部位：左手背部 注射时间：08:35:25 采集方式：断层采集3D
检查部位：体部+头部 层厚： 3.0mm 衰减校正：
临床诊断：淋巴瘤 设备型号：uMI780
检查方法：
代法重建后与CT图像进行融合，影像清晰。 空腹4小时以上，静脉注射显像剂平静休息后行PET/CT体部+头部断层显像。PET图像经衰减校正、迭
图像所见：
PET示大脑及小脑FDG摄取、分布未见异常。PET示鼻腔内FDG摄取增高影，最大SUV为6.5，CT示
相应部位软组织影；CT示甲状腺双叶密度不均，FDG摄取未见异常。PET示口咽双侧壁FDG摄取增高，最大
SUV为11.0，CT示相应部位软组织增厚；CT示右侧眼球外侧软组织增厚，FDG摄取增高，最大SUV为5.2；
PET示双侧下颌下腺FDG摄取增高影，最大SUV为4.5，CT示相应部位未见异常。PET示左侧颌面部皮下及
双侧腮腺区FDG摄取增高影，最大SUV为5.6，CT示相应部位软组织影，最大径约17mm。PET示双颈部及
双侧锁骨上FDG摄取增高影，最大SUV为4.3，CT示相应部位淋巴结影，最大径约14mm。余颌面部及颈部
软组织结构、形态及FDG摄取、分布未见异常。
CT示双肺多发结节影、软组织影及斑片影，部分与支气管分界不清，FDG摄取增高，最大SUV为9.0。
PET示主支气管及左右支气管管壁FDG摄取增高影，最大SUV为4.8，CT示相应部位管壁增厚；PET示纵隔
内及双肺门FDG摄取增高影，最大SUV为7.2，CT示相应部位淋巴结影，最大径约16mm。心脏FDG摄取
未见异常。CT示双侧乳腺组织致密，局部呈结节状，FDG摄取弥漫略增高。CT示双侧腋窝淋巴结影，最大径
约12mm，FDG摄取略增高，最大SUV为2.4。食管FDG摄取未见异常。
胃及十二指肠FDG摄取未见异常。CT示胃小弯侧淋巴结影，最大径约10mm，FDG摄取增高，最大SUV
为3.4。肝脏、胆囊FDG摄取未见异常。脾脏FDG摄取未见异常。胰腺FDG摄取未见异常。CT示右肾低密
度影，FDG摄取未见异常；肾上腺、左肾FDG摄取未见异常。腹部见数个形态不一、条管状浓淡不一的肠影。
腹膜后区FDG摄取未见异常。膀胱内见FDG分布。子宫、附件区FDG摄取未见异常。PET示左侧臀部皮下
团块状FDG摄取增高影，最大SUV为7.2，CT示相应部位软组织影，最大径约36mm。
CT示视野内双上肢肌肉旁淋巴结影，最大径约9mm，FDG摄取增高，最大SUV为3.7。PET示右侧第8
后肋FDG摄取略增高影，最大SUV为2.6，CT示相应部位未见异常；视野内余骨骼及骨髓FDG摄取未见异
常。视野内无特殊。
诊断意见：
1.口咽双侧壁软组织增厚，代谢增高，左侧颌面部皮下及双侧腮腺区、左侧臀部皮下软组织影，代谢增高，恶
性病变待除外；右侧眼球外侧软组织增厚，代谢增高，鼻腔内软组织影，代谢增高，请结合临床专科检查；
2.双颈部及双侧锁骨上、纵隔内及双肺门、视野内双上肢肌肉旁、胃小弯侧淋巴结影，代谢增高，恶性病变不
除外：双侧腋窝淋巴结代谢略增高，建议密切复查；
3.双肺多发结节影、软组织影及斑片影，部分与支气管分界不清，代谢增高，主支气管及左右支气管管壁增厚，
代谢增高，恶性病变累及不除外；
4.右侧第8后肋代谢略增高影，建议密切复查；
5.甲状腺双叶密度不均；双侧下颌下腺代谢增高，炎性改变不除外；双侧乳腺组织致密，局部呈结节状，代谢
弥漫略增高，建议定期复查；右肾低密度影；
6.视野内余部未见异常，请结合临床。 李 崔热
审核医生： 报告医生： 报告日期： 2024.04.23
地址：辽宁省沈阳市中国医科大学附属第一医院核医学科PET/CT室邮编：110001电话：（024）8328261

    病理报告：
    患者郑十，男，41岁，汉族
    身份证：440101198201011234
    电话号码：13512345678
    即时通讯：zhengshi_82
    病理号：PA20240801
    送检医院：深圳某某医院
    病理医师：林主任
    """,
    """## 病史1.png
1 查房记录
床旁查看患者，患者仍 有血尿 发热 Tmax37.9℃，无尿频、尿痛，查体：全身浅表淋巴结未触及肿大。心律齐，未闻及心脏杂音及心包摩擦
音。双肺听诊清音，未闻及干湿啰音。腹部平软，无压痛、反跳痛、肌紧张。双下肢无可凹性水肿。辅助检查；全血细胞计数+5分类，C-反应蛋白 生化23：白蛋
（CRP）测定：白细胞计数6.4*10^9/L 血小板计数15*10^9/L↓，快速C-反应蛋白102.2mg/L↑，血红蛋白含量58g/L↓。
白27.8g/L 肌酐94umol/L，钾3.54mmol/L.DIC全项 凝血酶原时间13.1s↑，D-二聚体650ng/mLT。 降钙素原（PCT）检测：降钙素
（
巨细胞病毒脱氧核糖核酸扩增定量检测EB病毒脱氧核糖核酸扩增定量检测：EB病毒脱氧核糖核酸（EBV-DNA）<4E+02拷贝/mL，巨细胞病毒脱氧核
糖核酸（CMV-DNA）<1E+03拷贝/mLAML/ALL/MDS基因组合：实时定量PCR检测NPM1-ANPM1-B、NPM1-D突变结果为阴性。
AML/ALL/MDS基因组合以上AML相关融合基因未检测到WT1及EVI1异常高表达，MLL-PTD突变水平正常。陈育红主任医师查房指示：者中年 男性 诊断急性髓系白血病 异基因造血干细胞移植术后，复查骨穿提示 复发 患者今日出租 现发热 考 素感染 停用莫西沙星，加用舒普深抗感染，今
日完善胸部CT评估肺部病变，关注患者生命体征，遵嘱继观
    康复记录：
    康复者：钱一，女，29岁
    身份证件：350101199401011234
    手机号码：15612345678
    微信联系：qianyi_94
    康复编号：RH20240901
    康复中心：福建某某康复医院
    康复师：张康复师
    """,
    """## 病史1.png
1 查房记录
床旁查看患者，患者仍 有血尿 发热 Tmax37.9℃，无尿频、尿痛，查体：全身浅表淋巴结未触及肿大。心律齐，未闻及心脏杂音及心包摩擦
音。双肺听诊清音，未闻及干湿啰音。腹部平软，无压痛、反跳痛、肌紧张。双下肢无可凹性水肿。辅助检查；全血细胞计数+5分类，C-反应蛋白 生化23：白蛋
（CRP）测定：白细胞计数6.4*10^9/L 血小板计数15*10^9/L↓，快速C-反应蛋白102.2mg/L↑，血红蛋白含量58g/L↓。
白27.8g/L 肌酐94umol/L，钾3.54mmol/L.DIC全项 凝血酶原时间13.1s↑，D-二聚体650ng/mLT。 降钙素原（PCT）检测：降钙素
（
巨细胞病毒脱氧核糖核酸扩增定量检测EB病毒脱氧核糖核酸扩增定量检测：EB病毒脱氧核糖核酸（EBV-DNA）<4E+02拷贝/mL，巨细胞病毒脱氧核
糖核酸（CMV-DNA）<1E+03拷贝/mLAML/ALL/MDS基因组合：实时定量PCR检测NPM1-ANPM1-B、NPM1-D突变结果为阴性。
AML/ALL/MDS基因组合以上AML相关融合基因未检测到WT1及EVI1异常高表达，MLL-PTD突变水平正常。陈育红主任医师查房指示：者中年 男性 诊断急性髓系白血病 异基因造血干细胞移植术后，复查骨穿提示 复发 患者今日出租 现发热 考 素感染 停用莫西沙星，加用舒普深抗感染，今
日完善胸部CT评估肺部病变，关注患者生命体征，遵嘱继观
    随访记录：
    随访对象：孙二，男，67岁，退休
    身份证号：230101195601011234
    联系电话：18712345678
    微信账户：suner_56
    随访编号：FU20241001
    随访医院：哈尔滨某某医院
    随访医生：王医生
    """,
    """## 病史1.png
1 查房记录
床旁查看患者，患者仍 有血尿 发热 Tmax37.9℃，无尿频、尿痛，查体：全身浅表淋巴结未触及肿大。心律齐，未闻及心脏杂音及心包摩擦
音。双肺听诊清音，未闻及干湿啰音。腹部平软，无压痛、反跳痛、肌紧张。双下肢无可凹性水肿。辅助检查；全血细胞计数+5分类，C-反应蛋白 生化23：白蛋
（CRP）测定：白细胞计数6.4*10^9/L 血小板计数15*10^9/L↓，快速C-反应蛋白102.2mg/L↑，血红蛋白含量58g/L↓。
白27.8g/L 肌酐94umol/L，钾3.54mmol/L.DIC全项 凝血酶原时间13.1s↑，D-二聚体650ng/mLT。 降钙素原（PCT）检测：降钙素
（
巨细胞病毒脱氧核糖核酸扩增定量检测EB病毒脱氧核糖核酸扩增定量检测：EB病毒脱氧核糖核酸（EBV-DNA）<4E+02拷贝/mL，巨细胞病毒脱氧核
糖核酸（CMV-DNA）<1E+03拷贝/mLAML/ALL/MDS基因组合：实时定量PCR检测NPM1-ANPM1-B、NPM1-D突变结果为阴性。
AML/ALL/MDS基因组合以上AML相关融合基因未检测到WT1及EVI1异常高表达，MLL-PTD突变水平正常。陈育红主任医师查房指示：者中年 男性 诊断急性髓系白血病 异基因造血干细胞移植术后，复查骨穿提示 复发 患者今日出租 现发热 考 素感染 停用莫西沙星，加用舒普深抗感染，今
日完善胸部CT评估肺部病变，关注患者生命体征，遵嘱继观
    急诊记录：
    急诊患者：李三，女，33岁
    身份证：650101199001011234
    紧急联系：13612345678
    微信号码：lisan_90
    急诊编号：ER20241101
    急诊科室：新疆某某医院急诊科
    接诊医生：赵急诊医师
    """
]


class PerformanceTestRunner:
    """性能测试运行器"""
    
    def __init__(self):
        self.anonymizer = MedicalAnonymizer(enable_logging=False)
        self.test_texts = TEST_TEXTS
        
    def run_single_thread_test(self, texts: List[str], include_anonymized_text: bool = False) -> Dict[str, Any]:
        """运行单线程测试"""
        print("  运行单线程测试...")
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        results = []
        for text in texts:
            result = self.anonymizer.detect_sensitive_info(text, include_anonymized_text)
            results.append(result)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        processing_time = end_time - start_time
        memory_usage = end_memory - start_memory
        throughput = len(texts) / processing_time
        
        return {
            "results": results,
            "processing_time": processing_time,
            "memory_usage": memory_usage,
            "throughput": throughput,
            "method": "单线程"
        }
    
    # def run_multi_thread_test(self, texts: List[str], max_workers: int, include_anonymized_text: bool = False) -> Dict[str, Any]:
    #     """运行多线程测试"""
    #     print(f"  运行多线程测试 ({max_workers} 线程)...")
    #
    #     start_time = time.time()
    #     start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    #
    #     results = self.anonymizer.detect_sensitive_info_batch(
    #         texts,
    #         max_workers=max_workers,
    #         include_anonymized_text=include_anonymized_text
    #     )
    #
    #     end_time = time.time()
    #     end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    #
    #     processing_time = end_time - start_time
    #     memory_usage = end_memory - start_memory
    #     throughput = len(texts) / processing_time
    #
    #     return {
    #         "results": results,
    #         "processing_time": processing_time,
    #         "memory_usage": memory_usage,
    #         "throughput": throughput,
    #         "method": f"多线程({max_workers})"
    #     }
    
    def verify_results_consistency(self, single_results: List, multi_results: List) -> bool:
        """验证单线程和多线程结果的一致性"""
        if len(single_results) != len(multi_results):
            return False
        
        for i, (single, multi) in enumerate(zip(single_results, multi_results)):
            # 如果是字典格式（包含脱敏文本），比较entities部分
            if isinstance(single, dict) and isinstance(multi, dict):
                single_entities = single.get('entities', [])
                multi_entities = multi.get('entities', [])
            else:
                single_entities = single
                multi_entities = multi
            
            # 比较实体数量
            if len(single_entities) != len(multi_entities):
                print(f"  ❌ 第{i+1}个文本的实体数量不一致: 单线程{len(single_entities)}, 多线程{len(multi_entities)}")
                return False
            
            # 比较实体内容（按文本内容排序后比较）
            single_sorted = sorted(single_entities, key=lambda x: x['text'])
            multi_sorted = sorted(multi_entities, key=lambda x: x['text'])
            
            for j, (s_entity, m_entity) in enumerate(zip(single_sorted, multi_sorted)):
                if s_entity['text'] != m_entity['text'] or s_entity['entity_type'] != m_entity['entity_type']:
                    print(f"  ❌ 第{i+1}个文本的第{j+1}个实体不一致")
                    return False
        
        return True
    
    def run_performance_comparison(self):
        """运行完整的性能对比测试"""
        print("=== 医疗数据脱敏系统性能对比测试 ===\n")

        # 系统信息
        cpu_count = psutil.cpu_count()
        memory_total = psutil.virtual_memory().total / 1024 / 1024 / 1024  # GB

        print(f"系统信息:")
        print(f"  CPU核心数: {cpu_count}")
        print(f"  总内存: {memory_total:.1f} GB")
        print(f"  测试文本数量: {len(self.test_texts)}")
        print(f"  平均文本长度: {statistics.mean([len(text) for text in self.test_texts]):.0f} 字符\n")

        # 测试配置
        thread_counts = [2, 4, 8]
        test_rounds = 3  # 每种配置测试3轮

        print("开始性能测试...\n")

        # 存储所有测试结果
        all_results = []

        for round_num in range(test_rounds):
            print(f"=== 第 {round_num + 1} 轮测试 ===")

            # 单线程测试
            single_result = self.run_single_thread_test(self.test_texts)
            all_results.append(single_result)

            # 多线程测试
            for workers in thread_counts:
                multi_result = self.run_multi_thread_test(self.test_texts, workers)
                all_results.append(multi_result)

                # 验证结果一致性（仅第一轮）
                if round_num == 0:
                    print(f"  验证 {workers} 线程结果一致性...")
                    is_consistent = self.verify_results_consistency(
                        single_result["results"],
                        multi_result["results"]
                    )
                    if is_consistent:
                        print(f"  ✅ {workers} 线程结果与单线程一致")
                    else:
                        print(f"  ❌ {workers} 线程结果与单线程不一致")

            print()

        # 计算统计数据并生成报告
        self.generate_performance_report(all_results, test_rounds)
    
    def generate_performance_report(self, all_results: List[Dict], test_rounds: int):
        """生成性能报告"""
        print("=== 性能测试报告 ===\n")
        
        # 按方法分组结果
        grouped_results = {}
        for result in all_results:
            method = result["method"]
            if method not in grouped_results:
                grouped_results[method] = []
            grouped_results[method].append(result)
        
        # 计算统计数据
        stats = {}
        for method, results in grouped_results.items():
            times = [r["processing_time"] for r in results]
            throughputs = [r["throughput"] for r in results]
            memories = [r["memory_usage"] for r in results]
            
            stats[method] = {
                "avg_time": statistics.mean(times),
                "std_time": statistics.stdev(times) if len(times) > 1 else 0,
                "min_time": min(times),
                "max_time": max(times),
                "avg_throughput": statistics.mean(throughputs),
                "std_throughput": statistics.stdev(throughputs) if len(throughputs) > 1 else 0,
                "avg_memory": statistics.mean(memories),
                "std_memory": statistics.stdev(memories) if len(memories) > 1 else 0
            }
        
        # 打印详细报告
        print("详细性能数据:")
        print("-" * 100)
        print(f"{'方法':<12} {'平均时间(s)':<12} {'标准差(s)':<10} {'吞吐量(doc/s)':<15} {'内存使用(MB)':<12} {'加速比':<8}")
        print("-" * 100)
        
        baseline_time = stats["单线程"]["avg_time"]
        
        for method in ["单线程", "多线程(2)", "多线程(4)", "多线程(8)"]:
            if method in stats:
                s = stats[method]
                speedup = baseline_time / s["avg_time"]
                print(f"{method:<12} {s['avg_time']:<12.3f} {s['std_time']:<10.3f} "
                      f"{s['avg_throughput']:<15.2f} {s['avg_memory']:<12.1f} {speedup:<8.2f}x")
        
        print("-" * 100)
        
        # 性能建议
        print("\n性能分析与建议:")
        
        best_method = min(stats.keys(), key=lambda x: stats[x]["avg_time"])
        best_speedup = baseline_time / stats[best_method]["avg_time"]
        
        print(f"  🏆 最佳性能: {best_method} (加速比: {best_speedup:.2f}x)")
        
        if best_speedup > 1.5:
            print(f"  ✅ 多线程处理显著提升性能，建议使用 {best_method}")
        elif best_speedup > 1.2:
            print(f"  ⚡ 多线程处理有一定提升，可考虑使用 {best_method}")
        else:
            print(f"  ⚠️  多线程提升有限，单线程可能更适合当前场景")
        
        # 资源使用分析
        max_memory = max(stats[method]["avg_memory"] for method in stats)
        if max_memory > 100:  # 超过100MB
            print(f"  💾 注意内存使用: 最大内存使用 {max_memory:.1f} MB")
        
        print(f"\n  📊 测试配置: {len(self.test_texts)} 个文档, {test_rounds} 轮测试")
        print(f"  🔧 推荐配置: 对于类似规模的文档批处理，建议使用 {best_method}")


def main():
    """主函数"""
    try:
        runner = PerformanceTestRunner()
        # runner.run_performance_comparison()
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
