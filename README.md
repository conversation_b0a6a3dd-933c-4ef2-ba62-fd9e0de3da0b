# HIPAA医疗数据脱敏系统

## 📋 系统简介

HIPAA医疗数据脱敏系统是一个专业的医疗数据隐私保护解决方案。

### 部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd hipaa-deidentify

# 2. 给脚本添加执行权限
chmod +x run_docker.sh

# 3. 一键部署完整服务（后端API + 前端页面）
./run_docker.sh

# 或者仅部署后端API服务
./run_docker.sh --backend-only
```

### 🌐 访问服务


- **前端演示页面**: http://localhost:50505
- **后端API服务**: http://localhost:50505
- **API交互文档**: http://localhost:50505/docs
- **健康检查接口**: http://localhost:50505/health

### ✅ 服务验证

```bash
# 健康检查
curl http://localhost:50505/health

# 脱敏接口测试
curl -X POST "http://localhost:50505/deidentify" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "患者张三，身份证110101199001011234，电话13812345678，就诊于北京协和医院"
  }'

# 查看API文档
open http://localhost:50505/docs  # macOS
# 或在浏览器中访问 http://localhost:50505/docs
```

### 📊 预期响应示例

```json
{
  "deidentified_text": "患者***，身份证***，电话***，就诊于***",
  "detected_entities": [
    {
      "entity_type": "PERSON",
      "text": "张三",
      "start": 2,
      "end": 4,
      "confidence": 0.95
    }
  ],
  "processing_time": 0.123,
  "entity_count": 4
}
```

## 🔧 服务管理

### 常用命令

```bash
# 查看所有可用命令
./run_docker.sh help

# 服务状态管理
./run_docker.sh status    # 查看服务状态
./run_docker.sh logs      # 查看实时日志
./run_docker.sh test      # 测试服务连接
./run_docker.sh health    # 健康检查

# 服务控制
./run_docker.sh stop      # 停止后端服务
./run_docker.sh restart   # 重启后端服务
./run_docker.sh full-stop # 停止所有服务

# 前端服务管理
./run_docker.sh frontend      # 启动前端页面
./run_docker.sh stop-frontend # 停止前端服务

# 系统维护
./run_docker.sh stats     # 查看资源使用
./run_docker.sh shell     # 进入容器Shell
./run_docker.sh clean     # 清理容器和镜像
```

### 高级配置

```bash
# 自定义端口部署
./run_docker.sh deploy --port 50506 --frontend-port 8081

# 性能优化（适合32核CPU服务器）
./run_docker.sh run --workers 8

# 自定义挂载目录
./run_docker.sh run --mount /custom/mount/path
```

## 📖 API文档

### 在线文档
- **Swagger UI**: http://localhost:50505/docs
- **ReDoc**: http://localhost:50505/redoc

### 主要接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/deidentify` | POST | 文本脱敏 |
| `/docs` | GET | API文档 |

## 🏗️ 项目结构

```
hipaa-deidentify/
├── api/                    # FastAPI后端服务
├── frontend/              # 前端演示页面
├── medical_anonymizer/    # 核心脱敏模块
├── utils/                 # 工具类和客户端
├── run_docker.sh         # 统一启动脚本
├── Dockerfile            # Docker镜像配置
└── requirements.txt      # Python依赖
```
