"""
两层过滤器架构主控制器
协调通用精确匹配层和实体类型特殊实体规则过滤层的工作
支持动态配置
"""

from typing import List, Dict, Any, Optional
from .exact_match_filter import ExactMatchFilter, create_exact_match_filter
from .specialized_rules import get_entity_rules, ENTITY_RULES_REGISTRY
import logging

logger = logging.getLogger(__name__)


class TwoLayerFilter:
    """
    两层过滤器架构主控制器
    
    架构设计：
    第一层：通用精确匹配层 - O(1)复杂度的医学术语快速过滤
    第二层：实体类型特殊实体规则过滤层 - 针对特定实体类型的复杂规则处理
    """
    
    def __init__(self, enable_stats: bool = False, context_window: int = 20, config_manager=None):
        """
        初始化两层过滤器

        Args:
            enable_stats: 是否启用统计功能
            context_window: 上下文窗口大小
            config_manager: 配置管理器，用于动态配置
        """
        # 第一层：通用精确匹配过滤器（强制禁用统计功能避免缓存，但支持动态配置）
        self.exact_match_filter = create_exact_match_filter(enable_stats=False, config_manager=config_manager)
        self.config_manager = config_manager

        # 第二层：实体类型特殊实体规则过滤处理器（完全不使用任何缓存）

        # 配置参数
        self.context_window = context_window
        self.enable_stats = enable_stats
        
        # 统计信息
        if enable_stats:
            self.stats = {
                'layer1_filtered': 0,
                'layer2_filtered': 0,
                'total_processed': 0,
                'final_preserved': 0,
                'layer1_filter_rate': 0.0,
                'layer2_filter_rate': 0.0,
                'overall_filter_rate': 0.0
            }
    
    def filter_results(self, results: List[Dict[str, Any]], original_text: str = "") -> List[Dict[str, Any]]:
        """
        两层过滤处理主入口
        
        Args:
            results: 原始识别结果列表
            original_text: 原始文本，用于上下文分析
            
        Returns:
            经过两层过滤后的结果列表
        """
        if not results:
            return []
        
        # 统计初始数量
        initial_count = len(results)
        if self.enable_stats:
            self.stats['total_processed'] += initial_count
        
        # 第一层：通用精确匹配过滤
        layer1_results = self.exact_match_filter.filter_results(results)
        layer1_filtered_count = initial_count - len(layer1_results)
        
        if self.enable_stats:
            self.stats['layer1_filtered'] += layer1_filtered_count
        
        # 第二层：实体类型特殊实体规则过滤
        layer2_results = self._apply_specialized_rules(layer1_results, original_text)
        layer2_filtered_count = len(layer1_results) - len(layer2_results)
        
        if self.enable_stats:
            self.stats['layer2_filtered'] += layer2_filtered_count
            self.stats['final_preserved'] += len(layer2_results)
            self._update_filter_rates()
        
        return layer2_results
    
    def _apply_specialized_rules(self, results: List[Dict[str, Any]], original_text: str) -> List[Dict[str, Any]]:
        """
        应用实体类型特殊实体规则过滤

        Args:
            results: 第一层过滤后的结果
            original_text: 原始文本

        Returns:
            第二层过滤后的结果
        """
        filtered_results = []

        for result in results:
            entity_text = result['text']
            entity_type = result['entity_type']

            # 获取上下文
            context = self._get_context(original_text, result.get('start', 0), result.get('end', 0))

            # 每次都重新创建规则处理器实例，不使用任何缓存
            rules_processor = get_entity_rules(entity_type)

            if rules_processor is None:
                # 没有专门规则的实体类型，直接保留
                filtered_results.append(result)
            else:
                # 应用特殊实体规则过滤
                if not rules_processor.should_exclude(entity_text, context):
                    filtered_results.append(result)
                else:
                    self._log_filtered_entity(entity_text, entity_type, "特殊实体规则过滤", rules_processor.__class__.__name__)

        return filtered_results
    

    
    def _get_context(self, text: str, start: int, end: int) -> str:
        """
        获取实体周围的上下文文本
        
        Args:
            text: 原始文本
            start: 实体开始位置
            end: 实体结束位置
            
        Returns:
            上下文文本
        """
        if not text:
            return ""
        
        context_start = max(0, start - self.context_window)
        context_end = min(len(text), end + self.context_window)
        
        return text[context_start:context_end]
    
    def _log_filtered_entity(self, entity_text: str, entity_type: str, layer: str, processor_name: str = ""):
        """
        记录被过滤的实体

        Args:
            entity_text: 实体文本
            entity_type: 实体类型
            layer: 过滤层级
            processor_name: 处理器名称
        """
        # 启用日志输出
        print(f"[过滤层：{layer}过滤] {entity_type}: '{entity_text}' - {processor_name}")
    
    def _update_filter_rates(self):
        """更新过滤率统计"""
        if self.stats['total_processed'] > 0:
            self.stats['layer1_filter_rate'] = self.stats['layer1_filtered'] / self.stats['total_processed']
            self.stats['layer2_filter_rate'] = self.stats['layer2_filtered'] / (self.stats['total_processed'] - self.stats['layer1_filtered']) if (self.stats['total_processed'] - self.stats['layer1_filtered']) > 0 else 0
            self.stats['overall_filter_rate'] = (self.stats['layer1_filtered'] + self.stats['layer2_filtered']) / self.stats['total_processed']
    
    def get_stats(self) -> Optional[Dict[str, Any]]:
        """
        获取过滤统计信息
        
        Returns:
            统计信息字典，如果未启用统计则返回None
        """
        if not self.enable_stats:
            return None
        
        # 获取第一层过滤器的详细统计
        layer1_stats = {}
        if hasattr(self.exact_match_filter, 'get_stats'):
            layer1_stats = self.exact_match_filter.get_stats()
        
        return {
            'summary': self.stats,
            'layer1_details': layer1_stats,
            'layer2_rules_available': list(ENTITY_RULES_REGISTRY.keys())
        }
    
    def reset_stats(self):
        """重置统计信息"""
        if self.enable_stats:
            self.stats = {
                'layer1_filtered': 0,
                'layer2_filtered': 0,
                'total_processed': 0,
                'final_preserved': 0,
                'layer1_filter_rate': 0.0,
                'layer2_filter_rate': 0.0,
                'overall_filter_rate': 0.0
            }


class TwoLayerFilterConfig:
    """两层过滤器配置类"""

    def __init__(self, config_manager=None):
        self.enable_stats = False
        self.context_window = 20
        self.enable_logging = False
        self.layer1_enabled = True
        self.layer2_enabled = True
        self.preserve_types = {
            "DEVICE_SERIAL", "STRUCTURED_FIELD", "MEDICAL_ORGANIZATION",
            "CHINESE_ID", "MOBILE_PHONE", "BANK_CARD", "WECHAT_ID", "QQ_NUMBER"
        }
        self.config_manager = config_manager
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'enable_stats': self.enable_stats,
            'context_window': self.context_window,
            'enable_logging': self.enable_logging,
            'layer1_enabled': self.layer1_enabled,
            'layer2_enabled': self.layer2_enabled,
            'preserve_types': list(self.preserve_types)
        }


def create_two_layer_filter(config: Optional[TwoLayerFilterConfig] = None, config_manager=None) -> TwoLayerFilter:
    """
    创建两层过滤器实例

    Args:
        config: 过滤器配置，如果为None则使用默认配置
        config_manager: 配置管理器，用于动态配置

    Returns:
        两层过滤器实例
    """
    if config is None:
        config = TwoLayerFilterConfig(config_manager=config_manager)

    return TwoLayerFilter(
        enable_stats=config.enable_stats,
        context_window=config.context_window,
        config_manager=config_manager or config.config_manager
    )


# 向后兼容的工厂函数
def create_medical_filter(enhanced: bool = True, enable_stats: bool = False, config_manager=None) -> TwoLayerFilter:
    """
    创建医疗实体过滤器（向后兼容接口）

    Args:
        enhanced: 兼容参数，新架构中始终启用增强功能
        enable_stats: 是否启用统计功能
        config_manager: 配置管理器，用于动态配置

    Returns:
        两层过滤器实例
    """
    # enhanced 参数保留用于向后兼容，但在新架构中不使用
    _ = enhanced  # 避免未使用变量警告

    config = TwoLayerFilterConfig(config_manager=config_manager)
    config.enable_stats = enable_stats

    return create_two_layer_filter(config, config_manager=config_manager)
