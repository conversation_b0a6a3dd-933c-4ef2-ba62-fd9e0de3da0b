/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 批量图片上传样式 */
.batch-images-container {
    margin-top: 20px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.batch-header h3 {
    color: #333;
    font-size: 1.2em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.batch-actions {
    display: flex;
    gap: 10px;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.image-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.image-item:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.image-item.processing {
    border-color: #ffc107;
    background: #fff8e1;
}

.image-item.completed {
    border-color: #28a745;
    background: #f8fff9;
}

.image-item.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.image-preview-container {
    position: relative;
    margin-bottom: 15px;
}

.image-preview-img {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.image-status {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
}

.image-status.pending {
    background: #6c757d;
}

.image-status.processing {
    background: #ffc107;
    color: #000;
}

.image-status.completed {
    background: #28a745;
}

.image-status.error {
    background: #dc3545;
}

.image-info {
    margin-bottom: 15px;
}

.image-filename {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    word-break: break-all;
}

.image-size {
    color: #666;
    font-size: 0.9em;
}

.image-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.image-progress {
    margin: 10px 0;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    height: 6px;
}

.image-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
}

/* 结果标签页样式 */
.result-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.result-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 0.95em;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-tab-btn:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.result-tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.result-tab-content {
    display: none;
}

.result-tab-content.active {
    display: block;
}

/* 过滤信息样式 */
.filtered-info-list {
    min-height: 200px;
}

.filtered-item {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.filtered-item:hover {
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.filtered-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.filtered-entity {
    font-weight: bold;
    color: #856404;
    font-size: 1.1em;
}

.filter-type {
    background: #fd7e14;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.filtered-context {
    color: #6c757d;
    font-size: 0.9em;
    font-style: italic;
    margin-top: 8px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1em;
    margin-bottom: 8px;
}

.empty-state small {
    font-size: 0.9em;
    opacity: 0.8;
}

/* 过滤组样式 */
.filtered-group {
    margin-bottom: 20px;
}

.filtered-group-header h4 {
    color: #333;
    font-size: 1.1em;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filtered-group-header .count {
    color: #666;
    font-weight: normal;
    font-size: 0.9em;
}

.filtered-group-content {
    margin-left: 20px;
}

.filtered-details {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.entity-type-badge {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75em;
    font-weight: bold;
}

.result-preview {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.result-summary {
    display: flex;
    gap: 15px;
    margin-bottom: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.result-stat {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9em;
    color: #666;
    font-weight: 500;
}

/* 敏感信息统计样式 */
.result-stat.sensitive-stat i {
    color: #ffc107;
}

/* 过滤信息统计样式 */
.result-stat.filtered-stat i {
    color: #6c757d;
}

/* 当过滤数量为0时的样式 */
/* .result-stat.filtered-stat:has-text("过滤词汇 0 个") {
    opacity: 0.7;
} */

.error-info {
    margin-top: 10px;
    padding: 8px 12px;
    background: #f8d7da;
    color: #721c24;
    border-radius: 6px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    width: 1200px;
    margin-left: 10%;
    /*margin-right: auto;*/
    padding: 20px;
    /*padding-right: 240px; !* 为右侧导航栏预留空间 *!*/
}

/* 页面标题 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #ffd700;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 输入区域 */
.input-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.input-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #495057;
}

#text-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    resize: vertical;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

#text-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 15px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* 上传区域 */
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-content i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.upload-content h3 {
    margin-bottom: 10px;
    color: #495057;
}

.upload-content p {
    color: #6c757d;
    margin-bottom: 20px;
}

.image-preview {
    text-align: center;
    padding: 20px;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.image-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 1.1rem;
    color: #6c757d;
}

/* 结果展示区域 - 优化卡片间距 */
.results-section {
    display: flex !important;
    flex-direction: column !important;
    margin-top: 30px !important; /* 添加顶部边距 */
    margin-bottom: 50px !important; /* 添加底部边距 */
    padding: 0 !important; /* 确保没有内边距干扰 */
}

/* 使用margin方式确保卡片间距 - 强制生效 */
.results-section .result-card {
    margin-bottom: 40px !important; /* 每个卡片底部40px间距 */
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* 最后一个卡片不需要底部间距 */
.results-section .result-card:last-child {
    margin-bottom: 0 !important;
}

/* ==================== 规则配置区域样式 ==================== */

/* 规则配置主容器 */
.rules-config-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 30px 0;
    overflow: hidden;
}

/* 配置区域头部 */
.config-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-header h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.config-header h2 i {
    font-size: 1.3rem;
}

/* 开发中提示 */
.development-notice {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.development-notice i {
    font-size: 0.9rem;
}

/* 配置内容区域 */
.config-content {
    padding: 0;
}

/* 规则分类容器 */
.rule-category {
    border-bottom: 1px solid #e9ecef;
}

.rule-category:last-child {
    border-bottom: none;
}

/* 分类头部 */
.category-header {
    padding: 20px 30px;
    background: #fafbfc;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.category-header:hover {
    background: #f1f3f4;
}

.category-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #2c3e50;
}

.category-title i {
    font-size: 1.1rem;
    color: #667eea;
}

.rule-count {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 400;
}

/* 分类控制按钮 */
.category-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 展开按钮 */
.expand-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expand-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.expand-btn i {
    transition: transform 0.3s ease;
}

.category-header.expanded .expand-btn i {
    transform: rotate(180deg);
}

/* 分类内容区域 */
.category-content {
    padding: 0;
    background: white;
    max-height: 800px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.category-content.collapsed {
    max-height: 0;
    padding: 0;
}

/* 规则项容器 */
.rule-item {
    border-bottom: 1px solid #f1f3f4;
    padding: 20px 30px;
    transition: all 0.3s ease;
}

.rule-item:last-child {
    border-bottom: none;
}

.rule-item:hover {
    background: #fafbfc;
}

/* 规则头部 */
.rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.rule-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.rule-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.rule-type {
    font-size: 0.85rem;
    color: #667eea;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* 规则描述 */
.rule-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 12px;
    line-height: 1.5;
}

/* 关键词标签容器 */
.keywords-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

/* 关键词标签 */
.keyword-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid #bbdefb;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.keyword-tag:hover {
    background: #bbdefb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

/* 更多标签提示 */
.keyword-tag.more-tags {
    background: #f5f5f5;
    color: #757575;
    border-color: #e0e0e0;
    cursor: default;
}

.keyword-tag.more-tags:hover {
    background: #eeeeee;
    transform: none;
    box-shadow: none;
}

/* 添加标签按钮 */
.add-tag-btn {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 28px;
}

.add-tag-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
}

.add-tag-btn i {
    font-size: 0.8rem;
}

/* 禁用状态样式 */
.rule-item.disabled {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.rule-item.disabled .keyword-tag {
    background: #f5f5f5;
    color: #9e9e9e;
    border-color: #e0e0e0;
}

.rule-item.disabled .keyword-tag:hover {
    background: #f5f5f5;
    transform: none;
    box-shadow: none;
}

.rule-item.disabled .add-tag-btn {
    background: #f5f5f5;
    border-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
}

.rule-item.disabled .add-tag-btn:hover {
    background: #f5f5f5;
    border-color: #e0e0e0;
    color: #9e9e9e;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
}

.empty-state p {
    margin: 8px 0;
}

.empty-hint {
    font-size: 0.9rem;
    color: #adb5bd;
}

/* ==================== 开发中提示弹窗样式 ==================== */

.development-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.development-popup.show {
    opacity: 1;
    visibility: visible;
}

.popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.popup-content {
    position: relative;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.development-popup.show .popup-content {
    transform: scale(1);
}

.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.popup-header i {
    font-size: 1.5rem;
}

.popup-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.popup-body {
    padding: 30px;
    text-align: center;
}

.popup-body p {
    margin: 0 0 15px 0;
    color: #2c3e50;
    line-height: 1.6;
}

.popup-hint {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.popup-footer {
    padding: 20px 30px 30px;
    text-align: center;
}

.popup-footer .btn {
    min-width: 120px;
}

/* 左侧悬浮导航栏 - 简化设计，避免遮挡 */
.floating-nav {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 200px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    z-index: 1000;
    transition: all 0.3s ease;
    overflow: hidden;
}

.floating-nav.collapsed {
    width: 50px;
}

.nav-header {
    background: #f8f9fa;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
}

.nav-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: opacity 0.3s ease;
}

.floating-nav.collapsed .nav-title span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-toggle {
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #6c757d;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.nav-toggle:hover {
    background: #e9ecef;
    color: #495057;
}

.nav-toggle i {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.floating-nav.collapsed .nav-toggle i {
    transform: rotate(180deg);
}

.nav-content {
    padding: 16px 0;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    border-radius: 6px;
    text-decoration: none;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
}

.nav-item.active {
    background: #667eea;
    color: white;
}

.nav-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    transition: all 0.3s ease;
    white-space: nowrap;
}

.floating-nav.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.floating-nav.collapsed .nav-item {
    justify-content: center;
    padding: 10px;
}

.nav-footer {
    margin-top: 16px;
    padding: 0 16px;
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.nav-back-top {
    width: 100%;
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.nav-back-top:hover {
    background: #f8f9fa;
    color: #495057;
}

.floating-nav.collapsed .nav-back-top {
    padding: 8px;
}

.floating-nav.collapsed .nav-footer {
    padding: 0 16px;
    padding-top: 16px;
}

.result-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0; /* 确保卡片本身没有外边距 */
}

.card-header {
    background: #f8f9fa;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 可折叠卡片样式 */
.collapsible-card .clickable-header {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.collapsible-card .clickable-header:hover {
    background: #e9ecef;
}

.card-toggle {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.card-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.card-toggle i {
    transition: transform 0.3s ease;
}

.card-content.collapsed {
    max-height: 0;
    padding: 0 30px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-content:not(.collapsed) {
    max-height: none;
    padding: 30px;
    transition: all 0.3s ease;
}

.result-stats {
    display: flex;
    gap: 20px;
    margin-top: 8px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 10px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.stat-item i {
    font-size: 0.8rem;
}

.stat-item span {
    font-weight: 600;
    color: #495057;
}

.card-content {
    padding: 30px;
}

.text-display {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    line-height: 1.8;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: 1px solid #e9ecef;
}

/* 敏感信息高亮 */
.sensitive-highlight {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-weight: 500;
    position: relative;
    cursor: help;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

.sensitive-highlight:hover {
    background: linear-gradient(135deg, #ee5a52, #dc4c64);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4);
}

.sensitive-highlight:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.sensitive-highlight:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}

/* 敏感信息列表 - 表格展示 */
.sensitive-info-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 敏感信息表格容器 */
.sensitive-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

/* 敏感信息表格 */
.sensitive-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.sensitive-table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 18px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.sensitive-table th:first-child {
    border-top-left-radius: 12px;
}

.sensitive-table th:last-child {
    border-top-right-radius: 12px;
}

.sensitive-table td {
    padding: 15px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.sensitive-table tr:hover {
    background: #f8f9fa;
}

.sensitive-table tr:last-child td {
    border-bottom: none;
}

/* 表格单元格内容样式 */
.sensitive-text-cell {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    word-break: break-word;
}

.detector-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    color: white;
}

.detector-badge .detector-name {
    white-space: nowrap;
}

.detector-regex { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.detector-dictionary { background: linear-gradient(135deg, #3498db, #2980b9); }
.detector-context { background: linear-gradient(135deg, #f39c12, #e67e22); }
.detector-nlp_model { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
.detector-hybrid { background: linear-gradient(135deg, #2ecc71, #27ae60); }

.entity-type-badge {
    background: #6c757d;
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.position-info {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    color: #6c757d;
    display: inline-block;
}

/* 敏感信息分组 */
.sensitive-group {
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.group-header {
    background: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.group-header:hover {
    background: #e9ecef;
}

.group-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #495057;
}

.group-title i {
    font-size: 1.1rem;
}

.group-name {
    font-size: 1rem;
}

.group-count {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.group-toggle {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.group-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.group-toggle i {
    transition: transform 0.3s ease;
}

.group-content {
    padding: 20px;
    transition: all 0.3s ease;
    max-height: 1000px;
    overflow: hidden;
}

.group-content.collapsed {
    max-height: 0;
    padding: 0 20px;
}

/* 敏感信息网格布局 */
.sensitive-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
}

.sensitive-item-compact {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border-left: 3px solid #667eea;
    transition: all 0.3s ease;
}

.sensitive-item-compact:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-text-compact {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    word-break: break-word;
    line-height: 1.4;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.item-type-compact {
    font-size: 0.8rem;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
    flex: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-confidence-compact {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
    background: rgba(40, 167, 69, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 40px;
    text-align: center;
}

/* 错误提示 */
.error-message {
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

/* ==================== 侧边栏图片导航样式 ==================== */

/* 侧边栏主容器 */
.image-sidebar-navigation {
    position: fixed;
    top: 80px;
    right: -300px; /* 初始隐藏在右侧 */
    width: 280px;
    height: calc(100vh - 100px);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* 确保侧边栏不影响主内容布局 */
    pointer-events: auto;
}

/* 侧边栏可见状态 */
.image-sidebar-navigation.visible {
    right: 0;
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-title {
    flex: 1;
}

.sidebar-header h4 {
    margin: 0 0 6px 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-header h4 i {
    color: #6c757d;
}

/* 侧边栏图例 */
.sidebar-legend {
    display: flex;
    gap: 12px;
    margin-top: 4px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.legend-item small {
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 500;
}

.legend-item i {
    font-size: 0.7rem;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.sidebar-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #495057;
}

/* 侧边栏内容区域 */
.sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 侧边栏控制按钮区域 - 改为水平布局 */
.sidebar-controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-btn {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #495057;
    font-size: 0.9rem;
}

.sidebar-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.sidebar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 位置指示器 - 调整为水平布局 */
.sidebar-position {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
    white-space: nowrap;
}

.separator {
    margin: 0 4px;
}

/* 侧边栏缩略图区域 - 调整间距适应更大图片 */
.sidebar-thumbnails {
    flex: 1;
    overflow-y: auto;
    padding: 16px 12px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 侧边栏缩略图项 - 调整间距适应更大图片 */
.sidebar-thumbnail {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.sidebar-thumbnail:hover {
    background: rgba(0, 0, 0, 0.05);
}

.sidebar-thumbnail.active {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

/* 缩略图图片容器 - 扩大尺寸 */
.sidebar-thumbnail .thumbnail-image {
    position: relative;
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.sidebar-thumbnail .thumbnail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 缩略图覆盖层 */
.sidebar-thumbnail .thumbnail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sidebar-thumbnail:hover .thumbnail-overlay {
    opacity: 1;
}

.thumbnail-number {
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

/* 状态图标 */
.status-success {
    color: #28a745;
}

.status-error {
    color: #dc3545;
}

.status-processing {
    color: #ffc107;
}

/* 缩略图信息 */
.sidebar-thumbnail .thumbnail-info {
    flex: 1;
    min-width: 0;
}

.sidebar-thumbnail .thumbnail-name {
    font-size: 0.75rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-thumbnail .thumbnail-stats {
    font-size: 0.7rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 统计项样式 */
.sidebar-thumbnail .stat-item {
    display: flex;
    align-items: center;
    gap: 3px;
}

.sidebar-thumbnail .stat-item.sensitive i {
    color: #ffc107;
}

.sidebar-thumbnail .stat-item.filtered i {
    color: #6c757d;
}

.sidebar-thumbnail .stat-count {
    font-weight: 500;
    min-width: 12px;
    text-align: center;
}

/* 重新处理按钮样式 */
.sidebar-thumbnail .reprocess-btn {
    /* background: #28a745; */
    border: none;
    border-radius: 4px;
    color: white;
    padding: 4px 6px;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 20px;
    margin-left: 4px;
}

.sidebar-thumbnail .reprocess-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

.sidebar-thumbnail .reprocess-btn:active {
    transform: scale(0.95);
}

.sidebar-thumbnail .reprocess-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.sidebar-thumbnail .reprocess-btn i {
    font-size: 0.65rem;
}

/* 调整统计区域布局以容纳重新处理按钮 */
.sidebar-thumbnail .thumbnail-stats {
    font-size: 0.7rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* 暂无图片提示 */
.no-images-message {
    text-align: center;
    color: #6c757d;
    font-size: 0.8rem;
    padding: 20px;
    font-style: italic;
}

/* 确保主内容区域不受侧边栏影响 */
.container {
    margin-right: 0 !important; /* 主内容区域始终保持原始位置 */
    transition: none !important; /* 禁用过渡动画，避免布局跳动 */
}

.sticky-nav-content {
    padding: 16px;
}

/* 吸附式导航中的控件样式调整 */
.sticky-image-navigation .image-navigation-controls {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
}

.sticky-image-navigation .nav-main-controls {
    margin-bottom: 12px;
}

.sticky-image-navigation .nav-thumbnails-container {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 12px;
}

/* 吸附状态下的样式微调 */
.sticky-image-navigation.sticky .nav-thumbnails-container {
    border-top: 1px solid rgba(0, 0, 0, 0.15);
}

.sticky-image-navigation.sticky .nav-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(222, 226, 230, 0.8);
}

.sticky-image-navigation.sticky .nav-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 1);
    border-color: #adb5bd;
}

/* 图片导航控件样式（保留用于非悬浮场景） */
.image-navigation-container {
    margin-bottom: 20px;
    display: none; /* 默认隐藏，现在由悬浮导航栏替代 */
}

.image-navigation-controls {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
}

/* 主导航控件 */
.nav-main-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.nav-btn {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
}

.nav-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.nav-btn:disabled {
    background: #f8f9fa;
    color: #adb5bd;
    cursor: not-allowed;
    opacity: 0.6;
}

.nav-position-indicator {
    text-align: center;
    flex: 1;
    margin: 0 16px;
}

.current-image-info {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.current-image-info i {
    color: #6c757d;
    margin-right: 6px;
}

.position-text {
    display: block;
    font-size: 0.85rem;
    color: #6c757d;
}

/* 缩略图导航 */
.nav-thumbnails-container {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.nav-thumbnails-scroll {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 8px 0;
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.nav-thumbnails-scroll::-webkit-scrollbar {
    height: 6px;
}

.nav-thumbnails-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.nav-thumbnails-scroll::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.nav-thumbnail {
    flex-shrink: 0;
    width: 80px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
}

.nav-thumbnail:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.nav-thumbnail.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.thumbnail-image {
    position: relative;
    width: 100%;
    height: 60px;
    background: #f8f9fa;
    overflow: hidden;
}

.thumbnail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px;
}

.thumbnail-index {
    background: rgba(255,255,255,0.9);
    color: #495057;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

.status-success { color: #28a745; }
.status-error { color: #dc3545; }
.status-processing { color: #ffc107; }
.status-pending { color: #6c757d; }

.thumbnail-info {
    padding: 6px 4px;
    background: #ffffff;
}

.thumbnail-name {
    font-size: 0.7rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
    line-height: 1.2;
}

.thumbnail-stats {
    font-size: 0.65rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 2px;
}

.thumbnail-stats i {
    color: #ffc107;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        padding-right: 15px; /* 移动端取消右侧边距 */
        padding-bottom: 100px; /* 为底部导航栏预留空间 */
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .tab-content {
        padding: 20px;
    }

    /* 移动端结果展示区域间距优化 */
    .results-section {
        margin-top: 25px !important;
        margin-bottom: 120px !important; /* 为底部导航预留更多空间 */
        padding: 0 !important; /* 确保没有内边距干扰 */
    }

    /* 移动端卡片间距 */
    .results-section .result-card {
        margin: 0 0 30px 0 !important; /* 移动端每个卡片底部30px间距 */
    }

    .card-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .header-left {
        width: 100%;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .result-stats {
        flex-direction: column;
        gap: 8px;
        margin-top: 10px;
    }

    /* 图片结果预览 - 移动端适配 */
    .result-summary {
        gap: 10px;
        margin-bottom: 8px;
    }

    .result-stat {
        font-size: 0.8em;
    }

    /* 规则配置区域 - 移动端适配 */
    .config-header {
        padding: 20px 20px;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .config-header h2 {
        font-size: 1.2rem;
    }

    .development-notice {
        align-self: stretch;
        justify-content: center;
    }

    .category-header {
        padding: 15px 20px;
    }

    .category-title {
        flex: 1;
        min-width: 0;
    }

    .category-title span:first-of-type {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .rule-count {
        display: none; /* 移动端隐藏规则数量 */
    }

    .category-content {
        padding: 0;
    }

    .rule-item {
        padding: 15px 20px;
    }

    .rule-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .rule-info {
        width: 100%;
    }

    .rule-name {
        font-size: 0.95rem;
    }

    .rule-type {
        font-size: 0.8rem;
    }

    .keywords-tags {
        gap: 6px;
    }

    .keyword-tag {
        font-size: 0.75rem;
        padding: 3px 8px;
    }

    .add-tag-btn {
        min-width: 28px;
        height: 24px;
    }

    /* 右侧悬浮导航栏 - 移动端适配 */
    .floating-nav {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
        width: calc(100% - 40px);
        max-width: 350px;
        border-radius: 12px;
    }

    .floating-nav.collapsed {
        width: calc(100% - 40px);
        max-width: 350px;
    }

    .nav-header {
        padding: 10px 14px;
    }

    .nav-title {
        font-size: 0.85rem;
    }

    .nav-content {
        padding: 12px 0;
    }

    .nav-menu {
        flex-direction: row;
        justify-content: space-around;
        padding: 0 14px;
        gap: 2px;
    }

    .nav-item {
        flex-direction: column;
        gap: 3px;
        padding: 8px 6px;
        text-align: center;
        min-width: 55px;
        font-size: 0.75rem;
    }

    .nav-item i {
        font-size: 1.1rem;
    }

    .nav-footer {
        display: none;
    }

    .floating-nav.collapsed .nav-text {
        opacity: 1;
        width: auto;
        overflow: visible;
    }

    .floating-nav.collapsed .nav-item {
        justify-content: center;
        padding: 8px 6px;
    }

    /* 移动端侧边栏导航样式 */
    .image-sidebar-navigation {
        /* 移动端改为底部抽屉式 */
        top: auto;
        bottom: -400px; /* 初始隐藏在底部 */
        right: 0;
        left: 0;
        width: 100%;
        height: 380px;
        border-radius: 20px 20px 0 0;
        transition: bottom 0.3s ease;
    }

    .image-sidebar-navigation.visible {
        bottom: 0;
        right: 0; /* 重置right属性 */
    }

    /* 移动端侧边栏头部 */
    .sidebar-header {
        padding: 12px 20px;
    }

    .sidebar-header h4 {
        font-size: 0.85rem;
        margin-bottom: 4px;
    }

    .sidebar-legend {
        gap: 10px;
    }

    .legend-item small {
        font-size: 0.65rem;
    }

    .legend-item i {
        font-size: 0.65rem;
    }

    /* 移动端控制按钮 */
    .sidebar-controls {
        flex-direction: row;
        justify-content: center;
        gap: 20px;
        padding: 12px;
    }

    .sidebar-btn {
        width: 36px;
        height: 36px;
        font-size: 0.8rem;
    }

    /* 移动端缩略图 - 水平滚动 */
    .sidebar-thumbnails {
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 12px;
        gap: 16px;
        -webkit-overflow-scrolling: touch;
    }

    .sidebar-thumbnail {
        flex-direction: column;
        min-width: 80px;
        gap: 8px;
        padding: 8px;
    }

    .sidebar-thumbnail .thumbnail-image {
        width: 80px;
        height: 60px;
    }

    .sidebar-thumbnail .thumbnail-info {
        text-align: center;
    }

    .sidebar-thumbnail .thumbnail-name {
        font-size: 0.7rem;
    }

    .sidebar-thumbnail .thumbnail-stats {
        font-size: 0.65rem;
        justify-content: center;
        gap: 6px;
    }

    .sidebar-thumbnail .stat-item {
        gap: 2px;
    }

    .sidebar-thumbnail .stat-count {
        min-width: 10px;
    }

    /* 移动端重新处理按钮 */
    .sidebar-thumbnail .reprocess-btn {
        padding: 3px 5px;
        font-size: 0.65rem;
        min-width: 20px;
        height: 18px;
        margin-left: 3px;
    }

    .sidebar-thumbnail .reprocess-btn i {
        font-size: 0.6rem;
    }

    /* 移动端主内容区域保持不变 */
    .container {
        margin-right: 0 !important; /* 主内容区域始终保持原始位置 */
    }

    /* 移动端图片导航样式 */
    .image-navigation-controls {
        padding: 12px;
        margin-bottom: 12px;
    }

    .nav-main-controls {
        margin-bottom: 12px;
    }

    .nav-btn {
        width: 36px;
        height: 36px;
    }

    .nav-position-indicator {
        margin: 0 12px;
    }

    .current-image-info {
        font-size: 0.8rem;
        margin-bottom: 2px;
    }

    .position-text {
        font-size: 0.75rem;
    }

    .nav-thumbnail {
        width: 70px;
    }

    .thumbnail-image {
        height: 50px;
    }

    .thumbnail-name {
        font-size: 0.65rem;
    }

    .thumbnail-stats {
        font-size: 0.6rem;
    }

    .result-stats {
        flex-direction: column;
        gap: 10px;
    }

    .input-actions,
    .image-actions {
        flex-direction: column;
    }
    
    /* 敏感信息分组 - 移动端适配 */
    .group-header {
        padding: 12px 15px;
    }

    .group-title {
        gap: 8px;
    }

    .group-name {
        font-size: 0.9rem;
    }

    .group-count {
        font-size: 0.7rem;
        padding: 1px 6px;
    }

    .group-content {
        padding: 15px;
    }

    .sensitive-items-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .sensitive-item-compact {
        padding: 10px;
    }

    .item-text-compact {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .item-meta {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
    }

    .item-type-compact,
    .item-confidence-compact {
        text-align: center;
        font-size: 0.75rem;
    }

    /* 敏感信息表格 - 移动端适配 */
    .sensitive-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .sensitive-table {
        min-width: 600px;
        font-size: 0.9rem;
    }

    .sensitive-table th,
    .sensitive-table td {
        padding: 12px 10px;
    }

    .sensitive-text-cell {
        font-size: 0.95rem;
        max-width: 150px;
        word-break: break-word;
    }

    .detector-badge {
        font-size: 0.8rem;
        padding: 4px 8px;
    }

    .detector-badge .detector-name {
        display: none; /* 移动端隐藏检测器名称，只显示图标 */
    }

    .entity-type-badge {
        font-size: 0.75rem;
        padding: 3px 8px;
    }

    .position-info {
        font-size: 0.8rem;
        padding: 3px 6px;
    }
}
