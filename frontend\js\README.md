# 医疗数据脱敏系统 - 前端模块化架构

## 📁 模块结构

本系统采用模块化架构，将原来的单一大文件（1800+行）拆分为6个专注的功能模块，提高代码的可维护性和调试便利性。

### 🏗️ 模块依赖关系

```
main.js (主入口)
├── event-handlers.js (事件处理)
│   ├── dom-utils.js (DOM工具)
│   ├── api-client.js (API客户端)
│   ├── data-formatters.js (数据格式化)
│   ├── batch-image-processing.js (批量图片处理)
│   └── result-display.js (结果显示)
└── 其他模块...
```

## 📋 模块详细说明

### 1. `dom-utils.js` - DOM工具模块
**功能**: 提供安全的DOM元素访问和操作功能
- ✅ 安全的元素获取函数 (`safeGetElement`, `safeQuerySelectorAll`)
- ✅ 统一的DOM元素集合管理
- ✅ 常用DOM操作封装 (显示/隐藏、添加/移除类、设置内容等)
- ✅ 事件监听器安全绑定
- ✅ DOM就绪状态检查

**导出**: `window.DOMUtils`

### 2. `api-client.js` - API客户端模块
**功能**: 处理所有与后端API的通信
- ✅ OCR API调用 (`callOCRAPI`, `performOCR`)
- ✅ 脱敏API调用 (`callDeidentifyAPI`, `performDeidentification`)
- ✅ 文件转base64处理 (`fileToBase64`)
- ✅ API健康检查和自动检测最佳服务地址
- ✅ 统一的错误处理和重试机制

**导出**: `window.APIClient`

### 3. `data-formatters.js` - 数据格式化模块
**功能**: 处理数据转换、格式化和实体类型映射
- ✅ 40+种敏感信息类型的中文映射 (`ENTITY_TYPE_MAPPING`)
- ✅ 文件大小、时间戳、处理时间格式化
- ✅ 文件验证和状态文本转换
- ✅ 过滤器类型显示名称映射
- ✅ 结果数据格式统一化 (`normalizeResultData`)
- ✅ 敏感文本高亮处理

**导出**: `window.DataFormatters`

### 4. `batch-image-processing.js` - 批量图片处理模块
**功能**: 处理批量图片上传、处理状态管理和界面更新
- ✅ 批量文件上传处理 (`handleBatchFileUpload`)
- ✅ 图片处理状态管理 (pending, processing, completed, error)
- ✅ 单张和批量图片处理 (`processSingleImage`, `processAllImages`)
- ✅ 图片项界面元素创建和更新
- ✅ 结果预览和详情查看
- ✅ 图片网格事件处理

**导出**: `window.BatchImageProcessing`

### 5. `result-display.js` - 结果显示模块
**功能**: 处理脱敏结果的显示、标签页切换和UI更新
- ✅ 结果主显示函数 (`showResults`, `displayResults`)
- ✅ 敏感信息列表显示 (`displaySensitiveInfoList`)
- ✅ 过滤信息详情显示 (`displayFilteredInfo`)
- ✅ 高亮文本显示 (`displayHighlightedText`)
- ✅ 结果标签页切换 (`switchResultTab`)
- ✅ 图片详情查看 (`viewImageDetail`)
- ✅ 结果导出功能

**导出**: `window.ResultDisplay`

### 6. `event-handlers.js` - 事件处理模块
**功能**: 处理所有用户界面交互和事件监听器绑定
- ✅ 标签页切换事件处理
- ✅ 文本输入相关事件 (清空、处理、加载示例)
- ✅ 文件上传相关事件 (选择、拖拽)
- ✅ 批量处理相关事件
- ✅ 拖拽上传初始化
- ✅ 快捷键支持 (Ctrl+Enter处理文本)
- ✅ UI工具函数 (错误显示、加载状态)

**导出**: `window.EventHandlers`, `window.UIUtils`

### 7. `main.js` - 主入口文件
**功能**: 协调各个模块的加载和初始化
- ✅ 模块加载状态监控
- ✅ 应用初始化协调
- ✅ 健康检查执行
- ✅ 全局错误处理
- ✅ 应用信息显示

**导出**: `window.MedicalDeidentificationApp`

## 🔄 加载顺序

HTML中的脚本按以下顺序加载，确保依赖关系正确：

```html
<!-- 1. 基础工具模块 -->
<script src="js/dom-utils.js"></script>
<script src="js/data-formatters.js"></script>

<!-- 2. API通信模块 -->
<script src="js/api-client.js"></script>

<!-- 3. 功能模块 -->
<script src="js/batch-image-processing.js"></script>
<script src="js/result-display.js"></script>

<!-- 4. 事件处理模块 -->
<script src="js/event-handlers.js"></script>

<!-- 5. 主应用入口 -->
<script src="js/main.js"></script>
```

## 🎯 模块化的优势

### 1. **可维护性提升**
- 每个模块职责单一，代码结构清晰
- 修改某个功能只需要关注对应模块
- 减少了代码耦合，降低维护成本

### 2. **调试便利性**
- 问题定位更加精确，可以快速找到相关模块
- 每个模块都有详细的控制台日志
- 模块加载状态监控，便于排查加载问题

### 3. **代码复用性**
- 工具函数模块可以被多个功能模块复用
- 统一的API客户端避免重复代码
- 数据格式化函数可以在不同场景下使用

### 4. **团队协作友好**
- 不同开发者可以专注于不同模块
- 减少代码冲突的可能性
- 模块接口清晰，便于协作开发

### 5. **性能优化潜力**
- 可以根据需要实现模块的懒加载
- 便于进行代码分割和优化
- 模块化结构便于实现缓存策略

## 🔧 开发指南

### 添加新功能
1. 确定功能属于哪个模块，或是否需要新建模块
2. 在对应模块中添加功能函数
3. 更新模块的导出对象
4. 在其他需要使用的模块中引用

### 调试技巧
1. 查看浏览器控制台的模块加载日志
2. 使用模块特定的日志前缀快速定位问题
3. 利用模块的健康检查功能验证状态

### 最佳实践
1. 保持模块职责单一
2. 避免模块间的循环依赖
3. 使用统一的错误处理模式
4. 添加充分的日志和注释

## 📊 迁移对比

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| 文件数量 | 1个主文件 | 7个模块文件 |
| 代码行数 | 1800+行 | 平均250行/模块 |
| 功能定位 | 需要搜索整个文件 | 直接定位到相关模块 |
| 调试难度 | 高 | 低 |
| 维护成本 | 高 | 低 |
| 团队协作 | 困难 | 容易 |

## 🚀 未来扩展

模块化架构为未来功能扩展提供了良好的基础：

1. **新增处理类型**: 可以轻松添加新的文件处理模块
2. **API扩展**: 在api-client模块中添加新的API接口
3. **UI组件**: 可以创建独立的UI组件模块
4. **数据分析**: 可以添加专门的数据分析和统计模块
5. **插件系统**: 基于模块化架构实现插件机制
