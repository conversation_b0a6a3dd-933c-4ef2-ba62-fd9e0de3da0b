## 识别器

**32个识别器的完整分布**

**🔧 26个自定义识别器（按功能分组）：**

1. 核心身份识别器组（6个）：
   - ChineseIDRecognizer - 身份证号
   - MobilePhoneRecognizer - 手机号
   - PassportRecognizer - 护照号
   - BankCardRecognizer - 银行卡
   - LandlinePhoneRecognizer - 固话
   - BiometricRecognizer - 生物特征
2. 医疗专业识别器组（4个）：
   - MedicalRecordRecognizer - 病案号
   - MedicalInsuranceRecognizer - 医保卡
   - MedicalPositionRecognizer - 医疗职位
   - MedicalNumberRecognizer - 医疗编号
3. 社交通信识别器组（4个）：
   - WeChatRecognizer - 微信号
   - QQNumberRecognizer - QQ号
   - CommunicationContentRecognizer - 通信内容
   - URLRecognizer - 自定义URL
4. 个人隐私识别器组（5个）：
   - EthnicityRecognizer - 民族
   - PrivacyInfoRecognizer - 隐私信息
   - ContextPrivacyInfoRecognizer - 上下文隐私
   - EducationLevelRecognizer - 学历学位
5. 地理位置识别器组（4个）：
   - CompleteAddressRecognizer - 完整地址
   - LicensePlateRecognizer - 车牌号
   - GPSCoordinateRecognizer - GPS坐标
   - VehicleInfoRecognizer - 车辆信息
6. 机构组织识别器组（3个）：
   - OrganizationRecognizer - 机构名称
   - CompanyWithParenthesesRecognizer - 带括号公司
   - StructuredFieldRecognizer - 结构化字段

**5个 NLP 识别器：**

- PERSON - 人名识别
- ORG - 机构名识别
- GPE - 地名识别
- LOCATION - 位置识别
- NRP - 民族国籍识别

**⚙️ 1个Presidio内置识别器：**

- EMAIL_ADDRESS - 邮箱识别





## 总体流程图





```mermaid
flowchart TD
    A[医疗文本输入] --> B{文本验证}
    B -->|验证失败| C[返回空结果]
    B -->|验证通过| D[MedicalAnonymizer.detect_sensitive_info]
    
    D --> E[初始化分析器配置]
    E --> F[Presidio AnalyzerEngine.analyze]
    
    F --> G[加载32个识别器]
    G --> H[识别器优先级调整]
    
    H --> I[并行执行识别器]
    I --> J[自定义识别器组]
    I --> K[spaCy NLP识别器]
    I --> L[Presidio内置识别器]
    
    J --> M[中文身份证识别器<br/>手机号识别器<br/>病案号识别器<br/>微信号识别器<br/>银行卡识别器<br/>民族识别器<br/>医疗机构识别器<br/>等25个自定义识别器]
    
    K --> N[人名识别 PERSON<br/>机构名识别 ORG<br/>地名识别 GPE/LOCATION<br/>民族识别 NRP]
    
    L --> O[邮箱识别 EMAIL_ADDRESS<br/>网址识别 URL<br/>IP地址识别<br/>加密货币识别]
    
    M --> P[收集识别结果]
    N --> P
    O --> P
    
    P --> Q[格式化检测结果]
    Q --> R[移除重叠结果]
    R --> S{启用医学术语过滤器?}
    
    S -->|是| T[医学术语过滤器处理]
    S -->|否| U[跳过过滤]
    
    T --> V[检查医学术语排除词典]
    V --> W[应用上下文过滤规则]
    W --> X[过滤误识别实体]
    X --> Y[生成最终实体列表]
    
    U --> Y
    
    Y --> Z{需要生成脱敏文本?}
    Z -->|否| AA[返回敏感实体列表]
    Z -->|是| BB[执行脱敏处理]
    
    BB --> CC[转换为Presidio格式]
    CC --> DD[配置脱敏操作符]
    
    DD --> EE[为35个实体类型<br/>配置mask操作符]
    EE --> FF[mask参数配置:<br/>chars_to_mask: 10<br/>masking_char: *<br/>from_end: false]
    
    FF --> GG[Presidio AnonymizerEngine.anonymize]
    GG --> HH[应用mask策略]
    
    HH --> II[生成脱敏文本]
    II --> JJ[返回完整结果]
    
    JJ --> KK[包含敏感实体列表<br/>和脱敏后文本]
    

    
    %% 样式定义
    classDef inputOutput fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef recognizer fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef filter fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef anonymize fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class A,C,AA,KK,RR inputOutput
    class D,E,F,G,H,I,P,Q,R,CC,DD,GG,HH,II,JJ,MM,NN,OO,PP,QQ process
    class B,S,Z,LL decision
    class J,K,L,M,N,O recognizer
    class T,V,W,X,Y,U filter
    class BB,EE,FF anonymize
```





## 识别器

（识别器不完整）

[超链接](https://mermaid-live.nodejs.cn/edit#pako:eNqFV_1TGlcX_ld2ttOZdubqy4IoMu90hiAxCCp-5QszmRUW2SmCs2Bbm2ZGGj8g4kdrjNEYP5pUnRg1aVpDQOM_w92Fn_ovvGfvXVZ2wddMZvXufc5zzj7n3HuOj9hQIiywTjYSS_wYivJSihnsGI4z8M8VlJ_PyZvvyl9W8MyfD5impu-YG8GAJCTFsJhgXHE-NvmzIHnio2JceEBtbhCUO1g-mcWZQ7x-IH88wLO58u6BBqBPN4F1BJV370r5Kfzh13_PNpT9YmX9DZ79S3mb1s01qw6C9wQppoNPCf1CKDEaF8H_f0ek_3xHNwLRRLzxTrcQFkN8zC-GhHiyMWRIil2-N0TrId5vkmgLORptcpx3T-phVmYWlPNjzegmgXdWg3UNeh4Oers9-HirdL6ifCjirXnitnTxSlldD3j6B3p7ENPb34mYzoAHMf5et2vQq77r6Q8YAukkzLeobFkaiCbw3Ft8vFH6nDUrd4uYeIPyTh5fPCkXDkvFcx2jFKcNsK4gzhWV52ul_Eopv3ElzBesbCwr--nSxa6cPrkS5g_iP7bwWQE_3S4_aeSUPr00Qi44zLqjUElJwdthSlA17DRe-kR5yGv56DXOrMsHu_hsCec-QxKG2QcGUiuQdidGxJjQqDTk7Ly8WTByclzpfEFefY-P1ipvc-DSTGkDygCfTI4npJSZ7-kbZWZf5zNbtoDlDT7-vZuXwibLysppeTeHF3brjemzi-ZHFUkrZZWhjkdZm5F3MyaViD5OpruffhZiKIqudD-aB-ulB288OSHx8ZBZNqgRKN2GwWoktkuSQCIppsREvJ4D6qyczoHaNbGWTxbwTN7JlPLFUrEIIJzPIAZ0xa8PK6sXylHW7Krl0lWvNMqDB_5Kd5BseWu6gTSwXVn_AzHlk6dydgoxeOFQebYNr2od0qePngA1EXcEd5Q31wD-cgzHonEGQG75RCsuOAV4OaOTa7Sq-n19PRNjI4JkIu7rM5Lam2pK1cyjJsCTisbFkJiaNFfp-9_ktaUGossfsrABX_9yC37Cp9tbS_m3FG52oMp-kx8TY8AeI4ono-K4WYvjU1w4wjMflY_FBv6UzGmp8Bek92SB_ITzi9On9ZL76W1CLojE2HhMSAmucBjaULLOX05e_RtvvsevphrIr2ymcf5XnCuUd9cq6RVTBjQvaga0LhGI1feZ8vmeks0ZM0HfyTtn4MV0Z2ikajo6AwPuBJxYMV7PCnv41bK8M1fDqhSXlMI7XNj7f8RqGjzxqHpIw9opaFg82kVMToFy9vyqW8bLEdruy_ZdOnuBZzJKYb_8_gloW73QrBSnrWyGVUvtqoszrAx2XQa7LoOdz2DnM9j5DHY-g53fYOc32PkNdn6DHX12k1c9IGr5-As09HoZaI9oZur7LfNN5fCFjvuWAK3NjHFOYL4p5Y9MIFszU52p8OwMeAXQ-eIlyJSjHhJjbxB_VluGnN0nzyl5s6719xJkICg_O628nMWL23h_nmKU4oq8tWmgDRBwX5Bu4eVF_GZaWZ6V_0krB_Maso9g-oO0InHueSlfgKLFc7PEqtrW-wlsICjnsnBBwQfJi7_jwpK2O0B2B0FjOh9V5hbw0g51WzMWgZl6j0L5v_wbhFU2pinEpMYgYRt6pLaKoz15E6aRo_LFnFx885gChlTAL3j5RHl28AtzG9ziwjP4XTNY_B1CoBcScd7t6fC6Xf6Hnrtu_9AADGEPYT2oO6Vsyl6asN0Jlj99BG_UoSGu2ySuu-BOfj0lb_9ZO_xReHl_GoYX4pSOgKQN6R9BIyPb1XnwSgCMjmrLfFrKz8PETulNOt0l8dyDeHQczszKW8v0hqQxZhcg7-rM_-IEL-9Bcmv96YT3CNX9IHRIObMsb04pxQz9PJxZMw_6dyiYLu6ThcsVVP-imCvIq0vy9LamTH1Ffv01I-98UguNHDT6MhSD0atDiDBifHwixUTEWMz5lWCLWCNhlExJie8F51dce1tr2Kotm34Uw6mo0zr-k4lgXEqEoIVoFBGbYI_YdYq2ES7CX0sh6ZdsNRBHxC44dBabwyHYQtexwBgnVKOIQBwW3T5ibwtZLNfZj9F7vxqCBbTgdAqLpa29deR6LcQf-NBkNYqQ0CKEdIqQlXPYr6UQtC5U5eAiDqFd52h1tEdsjus4wDSlawlaOITLD4mMhCzW8HUMiYlUTVk4BD7SepmNiJ0bsV_BUMPDuGhx1b66gdyoA3nQTdSJbqFu1IN6UQD1oX40gAarpVRr4EVdyIf8NSVi2OWQ14q8NgTNUk1_7V4Xh7qsqMuGoCFqqa3d9nHIZ0U-G4K-p6WtdtvPIb8V-W0I2ls1JbX7Q-g2uoPuonua2LV795HLpSnIInZUEsOsMyVNCIgdE6QxXl2yj1SDYTYVFcaEYdYJv4aFCD8RSw2zw_HHYDbOx-8nEmNVSykxMRplnRE-loTVxHgYhp8OkR-V-EuIEA8LkjsxEU-xTs7OEQ7W-Yj9iXU22Tmu2WGxWtodrS3tDoelpRWxk_Ces9mbHe2tNou9zca1cFbO9hixPxPHXHMbGFjsdvjfyjmsDsfj_wEzKCdz)



```mermaid
flowchart TD
    A[文本输入] --> B[Presidio AnalyzerEngine]
    B --> C[识别器注册表]
    
    C --> D[第一层：移除冲突识别器]
    D --> E[移除DateRecognizer<br/>移除PhoneRecognizer<br/>移除MedicalLicenseRecognizer<br/>移除UrlRecognizer]
    
    E --> F[第二层：spaCy识别器配置]
    F --> G[移除DATE_TIME实体类型<br/>保留PERSON, ORG, GPE, LOCATION, NRP]
    
    G --> H[第三层：注册自定义识别器]
    H --> I[核心身份识别器组]
    H --> J[医疗专业识别器组]
    H --> K[隐私信息识别器组]
    H --> L[增强功能识别器组]
    
    I --> I1["ChineseIDRecognizer<br/>身份证号识别<br/>正则模式匹配"]
    I --> I2["MobilePhoneRecognizer<br/>手机号识别<br/>11位数字验证"]
    I --> I3["PassportRecognizer<br/>护照号识别"]
    I --> I4["BankCardRecognizer<br/>银行卡号识别"]
    
    J --> J1["MedicalRecordRecognizer<br/>病案号识别<br/>模式: MR数字, 病案数字"]
    J --> J2["MedicalInsuranceRecognizer<br/>医保卡号识别"]
    J --> J3["MedicalPositionRecognizer<br/>医疗职位识别<br/>词典: 主任医师, 护士长等"]
    J --> J4["MedicalOrganizationRecognizer<br/>医疗机构识别<br/>模式: 医院, 诊所, 卫生院等"]
    
    K --> K1["WeChatRecognizer<br/>微信号识别<br/>模式: 字母数字组合"]
    K --> K2["QQNumberRecognizer<br/>QQ号识别<br/>5-11位数字"]
    K --> K3["EthnicityRecognizer<br/>民族识别<br/>词典: 汉族, 回族等56个民族"]
    
    L --> L1["CompleteAddressRecognizer<br/>完整地址识别<br/>模式: 省市区街道组合"]
    L --> L2["LicensePlateRecognizer<br/>车牌号识别<br/>车牌格式验证"]
    L --> L3["GPSCoordinateRecognizer<br/>GPS坐标识别<br/>经纬度格式验证"]
    L --> L4["MedicalNumberRecognizer<br/>增强医疗编号识别"]
    
    I1 --> M[识别器优先级调整]
    I2 --> M
    I3 --> M
    I4 --> M
    J1 --> M
    J2 --> M
    J3 --> M
    J4 --> M
    K1 --> M
    K2 --> M
    K3 --> M
    K4 --> M
    L1 --> M
    L2 --> M
    L3 --> M
    L4 --> M
    
    M --> N["设置识别器优先级<br/>1. 自定义识别器 (高优先级)<br/>2. spaCy识别器 (中优先级)<br/>3. Presidio内置 (低优先级)"]
    
    N --> O[并行执行所有识别器]
    O --> P[收集原始识别结果]
    
    P --> Q[结果后处理流程]
    Q --> R[格式化为标准结构]
    R --> S[按位置排序]
    S --> T["移除重叠结果<br/>保留置信度更高的结果"]
    
    T --> U{医学术语过滤}
    U -->|启用| V["应用医学排除词典<br/>MEDICAL_EXCLUSION_DICT"]
    U -->|禁用| W[跳过过滤]
    
    V --> X["检查实体类型过滤规则<br/>PERSON: 医学术语排除<br/>LOCATION: 医学术语排除<br/>ORG: 上下文过滤"]
    
    X --> Y["上下文分析<br/>检查前后文是否为医学术语"]
    Y --> Z[生成最终实体列表]
    
    W --> Z
    Z --> AA[输出敏感实体结果]
    
    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef recognizer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef core fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef medical fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef privacy fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef enhanced fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef filter fill:#fff8e1,stroke:#fbc02d,stroke-width:2px
    classDef output fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    class A input
    class B,C,D,E,F,G,H,M,N,O,P,Q,R,S,T process
    class I,J,K,L recognizer
    class I1,I2,I3,I4 core
    class J1,J2,J3,J4 medical
    class K1,K2,K3,K4 privacy
    class L1,L2,L3,L4 enhanced
    class U,V,W,X,Y filter
    class Z,AA output
```





```mermaid
flowchart TD
    A[文本输入] --> B[Presidio AnalyzerEngine]
    B --> C[识别器注册表]
    
    C --> D[第一层：移除冲突识别器]
    D --> E["移除DateRecognizer<br/>移除PhoneRecognizer<br/>移除MedicalLicenseRecognizer<br/>移除UrlRecognizer"]
    
    E --> F[第二层：spaCy识别器配置]
    F --> G["移除DATE_TIME实体类型<br/>保留PERSON, ORG, GPE, LOCATION, NRP"]
    
    G --> H[第三层：注册26个自定义识别器]
    H --> I[核心身份识别器组 - 6个]
    H --> J[医疗专业识别器组 - 4个]
    H --> K[社交通信识别器组 - 4个]
    H --> L[个人隐私识别器组 - 5个]
    H --> M[地理位置识别器组 - 4个]
    H --> N[机构组织识别器组 - 3个]
    
    I --> I1["1. ChineseIDRecognizer - 身份证号<br/>2. MobilePhoneRecognizer - 手机号<br/>3. PassportRecognizer - 护照号<br/>4. BankCardRecognizer - 银行卡<br/>5. LandlinePhoneRecognizer - 固话<br/>6. BiometricRecognizer - 生物特征"]
    
    J --> J1["7. MedicalRecordRecognizer - 病案号<br/>8. MedicalInsuranceRecognizer - 医保卡<br/>9. MedicalPositionRecognizer - 医疗职位<br/>10. MedicalNumberRecognizer - 医疗编号"]
    
    K --> K1["11. WeChatRecognizer - 微信号<br/>12. QQNumberRecognizer - QQ号<br/>13. CommunicationContentRecognizer - 通信内容<br/>14. URLRecognizer - 自定义URL"]
    
    L --> L1["15. EthnicityRecognizer - 民族<br/>16. FamilyRelationshipRecognizer - 家庭关系<br/>17. PrivacyInfoRecognizer - 隐私信息<br/>18. ContextPrivacyInfoRecognizer - 上下文隐私<br/>19. EducationLevelRecognizer - 学历学位"]
    
    M --> M1["20. CompleteAddressRecognizer - 完整地址<br/>21. LicensePlateRecognizer - 车牌号<br/>22. GPSCoordinateRecognizer - GPS坐标<br/>23. VehicleInfoRecognizer - 车辆信息"]
    
    N --> N1["24. OrganizationRecognizer - 机构名称<br/>25. CompanyWithParenthesesRecognizer - 带括号公司<br/>26. StructuredFieldRecognizer - 结构化字段"]
    
    I1 --> O[识别器优先级调整]
    J1 --> O
    K1 --> O
    L1 --> O
    M1 --> O
    N1 --> O
    
    O --> P["设置识别器优先级<br/>1. 自定义识别器 (高优先级)<br/>2. spaCy识别器 (中优先级)<br/>3. Presidio内置 (低优先级)"]
    
    P --> Q[并行执行32个识别器]
    Q --> R[spaCy NLP识别器 - 5个]
    Q --> S[Presidio内置识别器 - 1个]
    
    R --> R1["27. PERSON - 人名识别<br/>28. ORG - 机构名识别<br/>29. GPE - 地名识别<br/>30. LOCATION - 位置识别<br/>31. NRP - 民族国籍识别"]
    
    S --> S1["32. EMAIL_ADDRESS - 邮箱识别<br/>(其他内置识别器已被移除或替换)"]
    
    R1 --> T[收集所有识别结果]
    S1 --> T
    
    T --> U[结果后处理流程]
    U --> V[格式化为标准结构]
    V --> W[按位置排序]
    W --> X["移除重叠结果<br/>保留置信度更高的结果"]
    
    X --> Y{医学术语过滤}
    Y -->|启用| Z["应用医学排除词典<br/>MEDICAL_EXCLUSION_DICT"]
    Y -->|禁用| AA[跳过过滤]
    
    Z --> BB["检查实体类型过滤规则<br/>PERSON: 医学术语排除<br/>LOCATION: 医学术语排除<br/>ORG: 上下文过滤"]
    
    BB --> CC["上下文分析<br/>检查前后文是否为医学术语"]
    CC --> DD[生成最终实体列表]
    
    AA --> DD
    DD --> EE[输出敏感实体结果]
    
    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef recognizer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef core fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef medical fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef social fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef privacy fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef location fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef organization fill:#fff8e1,stroke:#fbc02d,stroke-width:2px
    classDef spacy fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef presidio fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef filter fill:#fff8e1,stroke:#fbc02d,stroke-width:2px
    classDef output fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    class A input
    class B,C,D,E,F,G,H,O,P,Q,T,U,V,W,X process
    class I,J,K,L,M,N recognizer
    class I1 core
    class J1 medical
    class K1 social
    class L1 privacy
    class M1 location
    class N1 organization
    class R,R1 spacy
    class S,S1 presidio
    class Y,Z,AA,BB,CC filter
    class DD,EE output
```