"""
验证配置完整性

检查所有29个识别器是否都包含在JSON配置中
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from medical_anonymizer.config import create_config_mapper
from medical_anonymizer.recognizers.patterns import UNIFIED_RECOGNIZER_CONFIGS


def validate_completeness():
    """验证配置完整性"""
    print("🔍 验证配置完整性...")
    
    # 获取原始配置中的所有识别器
    original_recognizers = set(UNIFIED_RECOGNIZER_CONFIGS.keys())
    print(f"📋 原始配置中的识别器数量: {len(original_recognizers)}")
    
    # 获取JSON配置映射后的识别器
    mapper = create_config_mapper()
    config = mapper.load_config()
    mapped_configs = mapper.map_all_recognizers()
    json_recognizers = set(mapped_configs.keys())
    print(f"📋 JSON配置映射后的识别器数量: {len(json_recognizers)}")
    
    # 检查缺失的识别器
    missing_in_json = original_recognizers - json_recognizers
    extra_in_json = json_recognizers - original_recognizers
    
    print("\n📊 完整性检查结果:")
    
    if missing_in_json:
        print(f"❌ JSON配置中缺失的识别器 ({len(missing_in_json)}个):")
        for recognizer in sorted(missing_in_json):
            print(f"   - {recognizer}")
    else:
        print("✅ JSON配置包含了所有原始识别器")
    
    if extra_in_json:
        print(f"⚠️ JSON配置中多余的识别器 ({len(extra_in_json)}个):")
        for recognizer in sorted(extra_in_json):
            print(f"   - {recognizer}")
    else:
        print("✅ JSON配置没有多余的识别器")
    
    # 按类别统计
    print("\n📋 按类别统计:")
    recognizers = config.get("recognizers", {})
    
    total_config_items = 0
    total_mapped_recognizers = 0
    
    for category_name, category_config in recognizers.items():
        category_recognizers = category_config.get("recognizers", {})
        config_count = len(category_recognizers)
        total_config_items += config_count
        
        # 统计映射的识别器数量
        mapped_count = 0
        for recognizer_config in category_recognizers.values():
            sources = recognizer_config.get("sources", [])
            mapped_count += len(sources)
        
        total_mapped_recognizers += mapped_count
        
        display_name = category_config.get("displayName", category_name)
        print(f"   {display_name}: {config_count} 个配置项 → {mapped_count} 个识别器")
    
    print(f"\n📊 总计:")
    print(f"   配置项总数: {total_config_items}")
    print(f"   映射识别器总数: {total_mapped_recognizers}")
    print(f"   原始识别器总数: {len(original_recognizers)}")
    
    # 检查分类规范
    print("\n🏷️ 分类规范检查:")
    
    contextual_count = 0
    nlp_count = 0
    invalid_category = []
    
    for category_name, category_config in recognizers.items():
        category_recognizers = category_config.get("recognizers", {})
        
        for recognizer_name, recognizer_config in category_recognizers.items():
            category = recognizer_config.get("category", "")
            
            if category == "contextual":
                contextual_count += 1
                # 检查是否有关键词配置
                positive_keywords = recognizer_config.get("positiveKeywords")
                negative_keywords = recognizer_config.get("negativeKeywords")
                
                if positive_keywords is None and not negative_keywords:
                    print(f"   ⚠️ {recognizer_name}: contextual类型但没有关键词配置")
                    
            elif category == "nlp_recognizer":
                nlp_count += 1
                # 检查是否错误配置了关键词
                if "positiveKeywords" in recognizer_config or "negativeKeywords" in recognizer_config:
                    print(f"   ⚠️ {recognizer_name}: nlp_recognizer类型但配置了关键词")
                    
            else:
                invalid_category.append(recognizer_name)
    
    print(f"   contextual类型: {contextual_count} 个")
    print(f"   nlp_recognizer类型: {nlp_count} 个")
    
    if invalid_category:
        print(f"   ❌ 无效分类: {invalid_category}")
    else:
        print("   ✅ 所有识别器分类正确")
    
    # 总结
    success = (
        len(missing_in_json) == 0 and 
        len(extra_in_json) == 0 and 
        len(invalid_category) == 0 and
        total_mapped_recognizers == len(original_recognizers)
    )
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 配置完整性验证通过！")
        print("✅ 所有29个识别器都正确包含在JSON配置中")
        print("✅ 分类规范正确")
        print("✅ 参数配置符合要求")
    else:
        print("❌ 配置完整性验证失败")
        print("请根据上述检查结果修复配置")
    
    return success


def list_all_recognizers():
    """列出所有识别器的详细信息"""
    print("\n📋 所有识别器详细信息:")
    print("-" * 80)
    
    # 按类型分组
    rule_based = []
    contextual = []
    nlp_based = []
    
    for name, config in UNIFIED_RECOGNIZER_CONFIGS.items():
        if config.is_nlp_based:
            nlp_based.append(name)
        elif config.context_config:
            contextual.append(name)
        else:
            rule_based.append(name)
    
    print(f"🔧 规则识别器 ({len(rule_based)}个):")
    for name in sorted(rule_based):
        print(f"   - {name}")
    
    print(f"\n🎯 上下文识别器 ({len(contextual)}个):")
    for name in sorted(contextual):
        print(f"   - {name}")
    
    print(f"\n🤖 NLP识别器 ({len(nlp_based)}个):")
    for name in sorted(nlp_based):
        print(f"   - {name}")
    
    print(f"\n📊 总计: {len(rule_based) + len(contextual) + len(nlp_based)} 个识别器")


def main():
    """主函数"""
    print("🧪 配置完整性验证")
    print("=" * 60)
    
    try:
        success = validate_completeness()
        list_all_recognizers()
        
        return success
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
