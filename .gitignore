# ===================================================================
# Python Stubs, Caches, and Byte-compiled files
# 编译文件、缓存和类型存根
# ===================================================================
__pycache__/
*.py[cod]
*$py.class
*.so

.idea/
./.idea/
.idea


# Type Stubs
*.pyi

# Caches for py.test and mypy
.pytest_cache/
.mypy_cache/
.pyre/
.ruff_cache/

# ===================================================================
# Virtual Environments
# ===================================================================
# 标准的 venv/env 目录
venv/
env/
.venv/
.env/

# pipenv, poetry 等工具的虚拟环境目录
.direnv/
.python-version

# ===================================================================
# Build and Distribution Artifacts
# 打包和分发生成的文件
# ===================================================================
build/
dist/
downloads/
eggs/
*.egg-info/
sdist/
wheelhouse/
*.egg
*.whl
*/.cursor/
.cursor/
# ===================================================================
# Secrets and Sensitive Configuration (!!!)
# 密钥和敏感配置文件 (!!!)
# ===================================================================
# 环境变量文件。保留 .env.example 或 .env.template 作为模板
.env
.env.*
!.env.example
!.env.template

# 常见的密钥文件名
*.pem
*.key
secrets.ini
secrets.json
*.creds

# ===================================================================
# IDE and Editor Configuration
# IDE 和编辑器配置文件
# ===================================================================
# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom-build.json

# Jupyter Notebook
.ipynb_checkpoints

# ===================================================================
# Testing and Coverage Reports
# 测试和代码覆盖率报告
# ===================================================================
htmlcov/
.coverage
.coverage.*
nosetests.xml
coverage.xml
*.prof
*.lprof
*.prof
*.out

# Tox and Nox
.tox/
.nox/

# ===================================================================
# Databases and Logs
# 本地数据库和日志文件
# ===================================================================
*.db
*.sqlite
*.sqlite3
*.log
*.log.*

# ===================================================================
# OS-specific files
# 操作系统生成的文件
# ===================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
desktop.ini