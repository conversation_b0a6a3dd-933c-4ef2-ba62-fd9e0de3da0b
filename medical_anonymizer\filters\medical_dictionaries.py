"""
医学术语词典配置
支持动态配置的医学术语词典模块，用于通用精确匹配层
"""

from typing import Set, Optional
import logging

logger = logging.getLogger(__name__)

# 默认静态配置（作为回退）- 简化版本
_DEFAULT_MEDICAL_TEST_ITEMS = [
    "葡萄糖", "蛋白", "酮体", "胆红素", "白细胞", "红细胞", "血红蛋白", "血小板",
    "血糖", "胆固醇", "肌酐", "尿素氮", "尿酸", "总蛋白", "白蛋白", "谷丙转氨酶",
    "谷草转氨酶", "碱性磷酸酶", "钠", "钾", "氯", "钙", "磷", "镁", "铁", "锌"
]

# 动态配置存储
_dynamic_medical_terms: Optional[Set[str]] = None

# 默认静态配置（作为回退）
_DEFAULT_MEDICAL_TERMS = [
    # 器官和组织
    "心脏", "肝脏", "肾脏", "肺", "脾脏", "胰腺", "胃", "肠", "膀胱", "前列腺",
    "子宫", "卵巢", "乳腺", "甲状腺", "肾上腺", "垂体", "大脑", "小脑", "脊髓",

    # 细胞和组织类型
    "上皮细胞", "内皮细胞", "间质细胞", "纤维细胞", "脂肪细胞", "肌细胞",
    "神经细胞", "胶质细胞", "干细胞", "免疫细胞", "巨噬细胞", "树突细胞",

    # 病理术语
    "炎症", "感染", "肿瘤", "癌症", "良性", "恶性", "转移", "浸润", "坏死",
    "纤维化", "硬化", "增生", "萎缩", "变性", "水肿", "出血", "血栓",

    # 检查方法
    "镜检", "培养", "染色", "免疫组化", "分子诊断", "基因检测", "流式细胞术",
    "电泳", "色谱", "质谱", "酶联免疫", "放射免疫", "化学发光", "荧光",

    # 医学单位和符号
    "阴性", "阳性", "正常", "异常", "升高", "降低", "未见", "少量", "中量", "大量",

        # 病理反射
    "Hoffmann征", "Babinski征", "Brudzinski征", "Kernig征",
    "Chaddock征", "Oppenheim征", "Gordon征", "Schaefer征",
    "Rossolimo征", "Mendel征", "Beevor征",

    # 仅英文名称
    "Hoffmann", "Babinski", "Brudzinski", "Kernig", "Chaddock",
    "Oppenheim", "Gordon", "Schaefer", "Rossolimo", "Mendel", "Beevor",

    # 生理反射
    "Cremasteric", "Plantar", "Abdominal", "Corneal", "Gag",
    "Biceps", "Triceps", "Brachioradialis", "Patellar", "Achilles",

        # 核酸相关
    "DNA", "RNA", "mRNA", "tRNA", "rRNA", "cDNA", "mtDNA", "cfDNA",
    "脱氧核糖核酸", "核糖核酸", "信使RNA", "转运RNA", "核糖体RNA",
    "互补DNA", "线粒体DNA", "游离DNA",

    # 分子技术
    "PCR", "qPCR", "RT-PCR", "ddPCR", "LAMP", "RPA",
    "聚合酶链反应", "实时定量PCR", "逆转录PCR",

    # 测序技术
    "NGS", "WGS", "WES", "RNA-seq", "ChIP-seq", "ATAC-seq",
    "下一代测序", "全基因组测序", "全外显子测序",

    # 基因变异
    "SNP", "CNV", "INDEL", "SV", "SNV", "MNV",
    "单核苷酸多态性", "拷贝数变异", "插入缺失", "结构变异",

    # 其他
    "CRISPR", "GWAS", "eQTL", "FISH", "ISH", "IHC",

        # 常见医学缩写
    "SG", "GLU", "PRO", "PH", "KET", "UBG", "BLD", "LEU", "NIT", "BII",
    "WBC", "RBC", "BACT", "YLC", "SPERM", "SRC", "EC", "MUS", "CAST",
    "LPF", "HPF", "UL", "DL", "ML", "MG", "G", "L", "M", "CM", "MM",

    # 检验项目代码
    "ALT", "AST", "ALP", "LDH", "CK", "CK-MB", "TnI", "TnT", "BNP",
    "PCT", "CRP", "ESR", "RF", "ANA", "ANCA", "HBsAg", "HBsAb", "HBcAb",
    "HCV", "HIV", "TP", "VDRL", "RPR", "TPPA", "CEA", "AFP", "PSA", "CA125",

        # 医学专业术语
    "Blood", "Urine", "Serum", "Plasma", "Cell", "Tissue", "Organ",
    "Protein", "Glucose", "Cholesterol", "Hemoglobin", "Antibody",
    "Antigen", "Enzyme", "Hormone", "Vitamin", "Mineral", "Electrolyte",

    # 检验设备和方法
    "Analyzer", "Microscope", "Centrifuge", "Incubator", "Spectrophotometer",
    "Chromatography", "Electrophoresis", "PCR", "ELISA", "Flow", "Cytometry",

    # 检验相关术语
    "Result", "Analysis", "Examination", "Treatment", "Therapy", "Medicine"
]

# 默认静态配置（作为回退）
_DEFAULT_DRUG_NAMES = [
    # BTK抑制剂
    "奥布替尼", "伊布替尼", "泽布替尼", "阿卡替尼",

    # 单克隆抗体
    "利妥昔单抗", "奥妥珠单抗", "奥法妥木单抗", "贝伐珠单抗",
    "曲妥珠单抗", "帕妥珠单抗", "阿德妥珠单抗",

    # 免疫调节剂
    "来那度胺", "泊马度胺", "沙利度胺",

    # 化疗药物
    "阿糖胞苷", "地西他滨", "阿扎胞苷", "氟达拉滨",
    "苯达莫司汀", "环磷酰胺", "异环磷酰胺",
    "表柔比星", "阿霉素", "柔红霉素", "伊达比星",
    "长春新碱", "长春花碱", "长春瑞滨",
    "紫杉醇", "多西他赛", "白蛋白紫杉醇",

    # 靶向药物
    "维奈克拉", "伊马替尼", "达沙替尼", "尼洛替尼",
    "博舒替尼", "普纳替尼", "舒尼替尼", "索拉非尼",

    # 激素类
    "泼尼松", "甲泼尼龙", "地塞米松", "氢化可的松"
]

# 默认静态配置（作为回退）
_DEFAULT_DISEASE_NAMES = [
    # 淋巴瘤类型
    "边缘区淋巴瘤", "弥漫大B细胞淋巴瘤", "滤泡性淋巴瘤",
    "套细胞淋巴瘤", "霍奇金淋巴瘤", "非霍奇金淋巴瘤",
    "MALT淋巴瘤", "DLBCL", "FL", "MCL", "HL", "NHL",

    # 白血病类型
    "急性髓系白血病", "急性淋巴细胞白血病", "慢性髓系白血病",
    "慢性淋巴细胞白血病", "急性早幼粒细胞白血病",
    "AML", "ALL", "CML", "CLL", "APL",

    # 其他血液病
    "多发性骨髓瘤", "华氏巨球蛋白血症", "骨髓增生异常综合征",
    "骨髓纤维化", "真性红细胞增多症", "原发性血小板增多症",
    "MM", "WM", "MDS", "PMF", "PV", "ET",

    # 医学术语
    "边缘区", "华氏", "Castleman病", "噬血细胞综合征"
]

# 默认静态配置（作为回退）
_DEFAULT_OTHER_NAMES = [
    '参考区间','参考区'
]

class DynamicMedicalDictionary:
    """动态医学词典管理器"""

    def __init__(self):
        self._medical_terms_cache: Optional[Set[str]] = None
        self._config_manager = None

    def initialize_from_config(self, config_manager):
        """从配置管理器初始化动态词典"""
        self._config_manager = config_manager
        self._rebuild_cache()

    def _rebuild_cache(self):
        """重建医学术语缓存"""
        if not self._config_manager:
            # 使用默认静态配置
            self._medical_terms_cache = frozenset(
                _DEFAULT_MEDICAL_TEST_ITEMS +
                _DEFAULT_MEDICAL_TERMS +
                _DEFAULT_DRUG_NAMES +
                _DEFAULT_DISEASE_NAMES +
                _DEFAULT_OTHER_NAMES
            )
            logger.info("使用默认静态医学术语配置")
            return

        try:
            # 从JSON配置获取过滤器配置
            filter_config = self._config_manager.get_filter_config()
            if not filter_config:
                raise ValueError("无法获取过滤器配置")

            all_terms = []

            # 合并所有过滤器类别的术语
            for category_name, category_config in filter_config.items():
                if isinstance(category_config, dict) and 'values' in category_config:
                    all_terms.extend(category_config['values'])

            self._medical_terms_cache = frozenset(all_terms)
            logger.info(f"从动态配置加载了 {len(self._medical_terms_cache)} 个医学术语")

        except Exception as e:
            logger.warning(f"动态配置加载失败，使用默认配置: {e}")
            # 回退到默认配置
            self._medical_terms_cache = frozenset(
                _DEFAULT_MEDICAL_TEST_ITEMS +
                _DEFAULT_MEDICAL_TERMS +
                _DEFAULT_DRUG_NAMES +
                _DEFAULT_DISEASE_NAMES +
                _DEFAULT_OTHER_NAMES
            )

    def is_medical_term(self, text: str) -> bool:
        """
        O(1)复杂度的医学术语检查

        Args:
            text: 待检查的文本

        Returns:
            bool: 是否为医学术语
        """
        if self._medical_terms_cache is None:
            self._rebuild_cache()
        return text in self._medical_terms_cache

    def get_all_terms(self) -> Set[str]:
        """获取所有医学术语"""
        if self._medical_terms_cache is None:
            self._rebuild_cache()
        return self._medical_terms_cache

    def reload_config(self):
        """重新加载配置"""
        self._rebuild_cache()

# 全局动态词典实例
_dynamic_dictionary = DynamicMedicalDictionary()

def initialize_dynamic_dictionary(config_manager):
    """初始化动态词典"""
    global _dynamic_dictionary
    _dynamic_dictionary.initialize_from_config(config_manager)

def is_medical_term(text: str) -> bool:
    """
    O(1)复杂度的医学术语检查、其他名词

    Args:
        text: 待检查的文本

    Returns:
        bool: 是否为医学术语
    """
    return _dynamic_dictionary.is_medical_term(text)

def get_all_medical_terms() -> Set[str]:
    """获取所有医学术语"""
    return _dynamic_dictionary.get_all_terms()

def reload_medical_dictionary():
    """重新加载医学词典"""
    _dynamic_dictionary.reload_config()

# 向后兼容的静态接口（已弃用，但保留以避免破坏现有代码）
ALL_MEDICAL_TERMS = None  # 将在首次访问时动态生成

def _get_legacy_all_medical_terms():
    """获取传统的ALL_MEDICAL_TERMS（向后兼容）"""
    global ALL_MEDICAL_TERMS
    if ALL_MEDICAL_TERMS is None:
        ALL_MEDICAL_TERMS = get_all_medical_terms()
    return ALL_MEDICAL_TERMS
