# HIPAA医疗数据去标识化系统 - Docker镜像

FROM nexus.smo-clinplus.com:8184/python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# 设置工作目录
WORKDIR /app

# 安装系统依赖
# RUN apt-get update && apt-get install -y \
#     wget \
#     curl \
#     build-essential \
#     && apt-get clean \
#     && rm -rf /var/lib/apt/lists/*

# Step 6: Switch to Tsinghua mirror and install dependencies
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list.d/debian.sources \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
       wget \
       curl \
       build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制项目依赖文件
COPY requirements.txt /app/

# 安装项目Python依赖
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt


# 安装torch的CPU版本（兼容Python 3.11）
#RUN pip install --no-cache-dir \
#    -i https://pypi.tuna.tsinghua.edu.cn/simple \
#    --extra-index-url https://download.pytorch.org/whl/cpu \
#    torch==2.1.2 \
#    torchvision==0.16.2

RUN wget http://***************:8081/python-package/torch-2.1.2+cpu.cxx11.abi-cp311-cp311-linux_x86_64.whl && wget http://***************:8081/python-package/torchvision-0.16.2+cpu-cp311-cp311-linux_x86_64.whl \
    && pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple torch-2.1.2+cpu.cxx11.abi-cp311-cp311-linux_x86_64.whl && pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple torchvision-0.16.2+cpu-cp311-cp311-linux_x86_64.whl \
    && rm torch-2.1.2+cpu.cxx11.abi-cp311-cp311-linux_x86_64.whl && rm torchvision-0.16.2+cpu-cp311-cp311-linux_x86_64.whl


# 下载并安装spaCy中文语言模型
RUN wget http://***************:8081/python-package/zh_core_web_trf-3.7.2-py3-none-any.whl \
    && pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple zh_core_web_trf-3.7.2-py3-none-any.whl \
    && rm zh_core_web_trf-3.7.2-py3-none-any.whl

# 验证spaCy模型安装
RUN python -c "import spacy; nlp = spacy.load('zh_core_web_trf'); print('✓ 中文模型安装成功')"

# 复制项目代码
COPY . /app/

# 设置目录权限（以root用户运行）
RUN chmod -R 755 /app

# 验证安装
RUN python -c "import medical_anonymizer; print('✓ 医疗脱敏系统导入成功')" \
    && python -c "import spacy; print('✓ spaCy导入成功')" \
    && python -c "import presidio_analyzer; print('✓ Presidio导入成功')" \
    && python -c "import fastapi; print('✓ FastAPI导入成功')" \
    && python -c "import pydantic; print('✓ Pydantic导入成功')"

# 暴露端口
EXPOSE 50505

# 设置入口点 - 保持容器运行，由外部脚本启动服务
CMD ["tail", "-f", "/dev/null"]
