# 医疗数据脱敏系统识别器重构指南

## 重构概述

本次重构将原本混合在 `recognizers.py` 中的两种不同识别逻辑进行了分离，提高了代码的可维护性和可理解性。

## 重构前后对比

### 重构前
- 所有识别器混合在单一文件 `recognizers.py` 中
- 纯规则判断和上下文判断逻辑混合
- 难以区分不同类型的识别器

### 重构后
- **纯规则判断识别器** → `rule_based_recognizers.py`
- **上下文判断识别器** → `context_based_recognizers.py`
- **兼容性适配器** → `recognizers_adapter.py`
- **原始文件** → `recognizers.py` (保留复杂识别器)

## 新文件结构

```
medical_anonymizer/
├── rule_based_recognizers.py      # 纯规则判断识别器
├── context_based_recognizers.py   # 上下文判断识别器
├── recognizers_adapter.py         # 兼容性适配器
├── recognizers.py                 # 原始文件(保留复杂识别器)
└── core.py                        # 核心模块(已更新导入)
```

## 识别器分类

### 纯规则判断识别器 (rule_based_recognizers.py)
**特征**: 只使用正则表达式模式，高置信度，无需上下文验证

- `ChineseIDRecognizer` - 中国身份证号码 (置信度: 0.98)
- `MobilePhoneRecognizer` - 中国手机号码 (置信度: 0.98)
- `LicensePlateRecognizer` - 车牌号 (置信度: 0.9)
- `LandlinePhoneRecognizer` - 座机电话 (置信度: 0.85)
- `MedicalInsuranceRecognizer` - 医保社保卡号 (置信度: 0.95)
- `BankCardRecognizer` - 银行卡号 (置信度: 0.95)

### 上下文判断识别器 (context_based_recognizers.py)
**特征**: 使用正则表达式+上下文验证，中等置信度，需要上下文关键词

- `WeChatRecognizer` - 微信号 (重写analyze方法)
- `PassportRecognizer` - 护照号 (重写analyze方法)
- `MedicalRecordRecognizer` - 病案号 (使用context参数)
- `QQNumberRecognizer` - QQ号码 (使用context参数)
- `EthnicityRecognizer` - 民族信息 (使用context参数)
- `FamilyRelationshipRecognizer` - 家庭关系 (使用context参数)
- `MedicalPositionRecognizer` - 医疗职位 (使用context参数)
- `BiometricRecognizer` - 生物特征 (使用context参数)
- `OrganizationRecognizer` - 医疗机构 (使用context参数)
- `CompanyWithParenthesesRecognizer` - 带括号公司名 (使用context参数)
- `EducationLevelRecognizer` - 学历学位 (使用context参数)

## Presidio集成机制

### 集成流程
1. **初始化**: `MedicalAnonymizer` 类在 `_init_analyzer()` 中初始化 Presidio AnalyzerEngine
2. **注册**: 通过 `_register_medical_recognizers()` 注册所有识别器
3. **获取**: 使用 `get_all_recognizers()` 或 `get_core_recognizers()` 获取识别器实例
4. **添加**: 每个识别器通过 `analyzer.registry.add_recognizer(recognizer)` 注册到 Presidio

### 识别器基类
所有识别器都继承自 Presidio 的 `PatternRecognizer` 基类：
```python
from presidio_analyzer import PatternRecognizer, Pattern

class CustomRecognizer(PatternRecognizer):
    def __init__(self):
        patterns = [Pattern(name="pattern_name", regex="...", score=0.9)]
        super().__init__(
            supported_entity="ENTITY_TYPE",
            patterns=patterns,
            name="RecognizerName",
            supported_language="zh"
        )
```

## 兼容性保证

### 导入兼容性
原有的导入方式完全兼容：
```python
# 这些导入方式仍然有效
from medical_anonymizer.recognizers import ChineseIDRecognizer
from medical_anonymizer.core import MedicalAnonymizer
```

### 函数兼容性
核心函数保持不变：
```python
# 这些函数调用方式不变
recognizers = get_all_recognizers()
core_recognizers = get_core_recognizers()
```

### 使用方式兼容性
```python
# 原有的使用方式完全不变
anonymizer = MedicalAnonymizer()
result = anonymizer.detect_sensitive_info("测试文本")
```

## 新增功能

### 按类型获取识别器
```python
from medical_anonymizer.recognizers_adapter import get_recognizers_by_type

recognizers_by_type = get_recognizers_by_type()
print(f"纯规则识别器: {recognizers_by_type['rule_based_names']}")
print(f"上下文识别器: {recognizers_by_type['context_based_names']}")
```

### 获取统计信息
```python
from medical_anonymizer.recognizers_adapter import get_recognizer_statistics

stats = get_recognizer_statistics()
print(f"总识别器数量: {stats['total_recognizers']}")
print(f"纯规则识别器占比: {stats['rule_based_percentage']}%")
print(f"上下文识别器占比: {stats['context_based_percentage']}%")
```

## 实施步骤总结

1. ✅ **创建纯规则判断模块** - `rule_based_recognizers.py`
2. ✅ **创建上下文判断模块** - `context_based_recognizers.py`
3. ✅ **创建兼容性适配器** - `recognizers_adapter.py`
4. ✅ **更新核心模块导入** - 修改 `core.py` 中的导入语句
5. ✅ **保持向后兼容** - 确保所有现有代码无需修改

## 测试验证

### 基本功能测试
```python
# 测试基本功能是否正常
from medical_anonymizer import MedicalAnonymizer

anonymizer = MedicalAnonymizer()
result = anonymizer.detect_sensitive_info("张三的身份证号是110101199001011234")
print(result)
```

### 识别器分类测试
```python
# 测试识别器分类是否正确
from medical_anonymizer.recognizers_adapter import get_recognizer_statistics

stats = get_recognizer_statistics()
assert stats['total_recognizers'] > 0
assert stats['rule_based_count'] > 0
assert stats['context_based_count'] > 0
```

## 注意事项

1. **保持兼容性**: 所有现有代码无需修改即可正常运行
2. **逐步迁移**: 可以逐步将代码迁移到使用新的分类模块
3. **性能影响**: 重构不会影响识别性能，只是代码组织方式的改变
4. **扩展性**: 新的分类结构便于后续添加新的识别器类型

## 后续优化建议

1. **进一步细分**: 可以考虑将复杂识别器也进行分类
2. **配置化**: 将识别器的启用/禁用配置化
3. **性能优化**: 对不同类型的识别器采用不同的优化策略
4. **测试覆盖**: 为每个分类模块添加专门的单元测试
