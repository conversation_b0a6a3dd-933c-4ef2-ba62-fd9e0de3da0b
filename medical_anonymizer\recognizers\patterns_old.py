"""
医疗数据脱敏系统的模式和词典定义

本模块包含用于检测中文医疗文本中敏感信息的所有正则表达式模式和基于词典的识别规则。
"""

from typing import Dict, List, Any, Optional, Union
from enum import Enum


# ============================================================================
# 上下文验证级别和配置
# ============================================================================

class ValidationLevel(Enum):
    """上下文验证级别"""
    STRICT = "strict"      # 严格验证：必须有正面关键词且无负面关键词
    MODERATE = "moderate"  # 中等验证：有正面关键词或无负面关键词
    LENIENT = "lenient"    # 宽松验证：主要避免明显的负面关键词


class ContextConfig:
    """上下文配置类"""

    def __init__(self, validation_level: ValidationLevel,
                 positive_keywords: List[str],
                 negative_keywords: List[str],
                 window_size: int = 25,
                 min_confidence: float = 0.85,
                 require_exact_match: bool = True):
        """
        初始化上下文配置

        Args:
            validation_level: 验证级别
            positive_keywords: 正面关键词列表
            negative_keywords: 负面关键词列表
            window_size: 上下文窗口大小
            min_confidence: 最小置信度要求
            require_exact_match: 是否要求精确匹配
        """
        self.validation_level = validation_level
        self.positive_keywords = positive_keywords
        self.negative_keywords = negative_keywords
        self.window_size = window_size
        self.min_confidence = min_confidence
        self.require_exact_match = require_exact_match


class RecognizerConfig:
    """统一的识别器配置类"""

    def __init__(self,
                 regex: Union[str, List[str]] = None,
                 score: float = None,
                 description: str = "",
                 enabled: bool = True,
                 context_config: Optional[ContextConfig] = None,
                 is_nlp_based: bool = False):
        """
        初始化识别器配置

        Args:
            regex: 正则表达式模式（字符串或字符串列表，NLP识别器可为None）
            score: 识别置信度分数
            description: 识别器描述
            enabled: 是否启用该识别器（默认True）
            context_config: 上下文配置（可选，用于上下文识别器）
            is_nlp_based: 是否为NLP识别器（默认False）
        """
        self.regex = regex
        self.score = score
        self.description = description
        self.enabled = enabled
        self.context_config = context_config
        self.is_nlp_based = is_nlp_based

    @property
    def is_context_based(self) -> bool:
        """判断是否为上下文识别器"""
        return self.context_config is not None and not self.is_nlp_based

    @property
    def is_rule_based(self) -> bool:
        """判断是否为规则识别器"""
        return self.context_config is None and not self.is_nlp_based


# ============================================================================
# 统一识别器配置字典
# ============================================================================

# 统一管理所有识别器的配置（规则识别器 + 上下文识别器）
UNIFIED_RECOGNIZER_CONFIGS: Dict[str, RecognizerConfig] = {

    # ========================================================================
    # 规则识别器配置（不需要上下文验证）
    # 配置键名使用识别器类名，便于直接映射
    # ========================================================================

    "ChineseIDRecognizer": RecognizerConfig(
        regex=r'(?<![0-9])[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx](?![0-9])',
        score=0.98,
        description="中国身份证号码模式",
        enabled=True
    ),

    "LicensePlateRecognizer": RecognizerConfig(
        regex=r'\b[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]\b',
        score=0.9,
        description="中国车牌号模式",
        enabled=True
    ),

    "EducationLevelRecognizer": RecognizerConfig(
        regex=r'(?<![a-zA-Z\u4e00-\u9fff])(博士|硕士|学士|本科|大专|高中|中专|初中|研究生|大学|专科|博士学位|硕士学位|学士学位|本科学历|专科学历|大学生|专科生)(?![a-zA-Z\u4e00-\u9fff])',
        score=0.90,
        description="学历学位模式（纯规则匹配）",
        enabled=True
    ),

    "PrivacyInfoRecognizer": RecognizerConfig(
        regex=r'(?<![a-zA-Z\u4e00-\u9fff])(异性恋|同性恋|双性恋|佛教|基督教|伊斯兰教|天主教|道教|已婚|未婚|离异|单身|丧偶|再婚|分居|党员|预备党员|共青团员|民主党派|无党派)(?![a-zA-Z\u4e00-\u9fff])',
        score=0.90,
        description="隐私敏感信息模式（基础隐私信息）",
        enabled=True
    ),

    "EthnicityRecognizer": RecognizerConfig(
        regex=r'汉族|壮族|回族|满族|维吾尔族|苗族|彝族|土家族|藏族|蒙古族|侗族|布依族|瑶族|白族|朝鲜族|哈尼族|哈萨克族|黎族|傣族|畲族|傈僳族|仡佬族|东乡族|高山族|拉祜族|水族|佤族|纳西族|羌族|土族|仫佬族|锡伯族|柯尔克孜族|达斡尔族|景颇族|毛南族|撒拉族|布朗族|塔吉克族|阿昌族|普米族|鄂温克族|怒族|京族|基诺族|德昂族|保安族|俄罗斯族|裕固族|乌孜别克族|门巴族|鄂伦春族|独龙族|塔塔尔族|赫哲族|珞巴族',
        score=0.95,
        description="民族信息模式（纯规则匹配）",
        enabled=True
    ),

    "CompleteAddressRecognizer": RecognizerConfig(
        regex=r'[\u4e00-\u9fff]{2,4}[省市][\u4e00-\u9fff]{2,6}[市区县][\u4e00-\u9fff]{2,20}(?:[路街道巷弄号栋楼室]\d*号?|[园区镇乡村])',
        score=0.95,
        description="完整地址模式（支持多种格式）",
        enabled=True
    ),

    "URLRecognizer": RecognizerConfig(
        regex=r'',
        score=0.90,
        description="时间日期模式（支持中文和数字格式）",
        enabled=True
    ),

    "SponsorRecognizer": RecognizerConfig(
        regex=[
            "南京海光应用化学研究所",
            "南京海美生物医药",
            "南京瑞天医药",
            "南京生命能"
        ],
        score=1.0,
        description="申办方名称列表",
        enabled=True
    ),

    "StructuredFieldRecognizer": RecognizerConfig(
        regex=r'',
        score=0.85,
        description="本体定义",
        enabled=True
    ),

    # ========================================================================
    # 上下文识别器配置（需要上下文验证）
    # 配置键名使用识别器类名，便于直接映射
    # ========================================================================

    "WeChatRecognizer": RecognizerConfig(
        regex=r'(?<![a-zA-Z0-9_-])[a-zA-Z][a-zA-Z0-9_-]{5,19}(?![a-zA-Z0-9_-])',
        score=0.85,  # 降低置信度，让其他识别器优先
        description="微信号模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "微信", "WeChat", "微信号", "微信账号", "wechat", "wx", "WX",
                "微信联系", "加微信", "微信好友", "微信群", "微信名",
                "WeChat ID", "wechat id", "WECHAT ID", "联系微信"
            ],
            negative_keywords=[
                "编号", "代码",
                "系统", "数据库", "序列", "批次",
                # 医学术语相关
                "检测", "检验", "分析", "试剂", "抗体", "抗原",
                "蛋白", "基因", "分子", "细胞", "标记", "染色",
                "流式", "细胞术", "免疫", "组化", "病理",
                # 设备和软件名称
                "仪器", "设备", "软件", "系统", "平台", "程序",
                "Expert", "Cyt", "HLA", "CD", "DR"
            ],
            window_size=20,
            min_confidence=0.85,
            require_exact_match=False  # 微信号可以大小写不敏感
        )
    ),

    "MobilePhoneRecognizer": RecognizerConfig(
        regex=r'(?<![0-9])1[3-9]\d{9}(?![0-9])',
        score=0.98,
        description="手机号码模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "手机", "手机号", "电话", "联系电话", "移动电话", "手机号码",
                "联系方式", "电话号码", "手机联系", "移动号码"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号", "床位号",
                "身份证", "银行卡", "医保卡", "工号", "学号"
            ],
            window_size=15,
            min_confidence=0.98,
            require_exact_match=False
        )
    ),

    "LandlinePhoneRecognizer": RecognizerConfig(
        regex=r'(?<![0-9a-zA-Z])(?:0\d{2,3}-?\d{7,8}|\d{8})(?![0-9a-zA-Z])',
        score=0.85,
        description="中国固定电话模式（支持带区号和不带区号格式）",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "电话", "座机", "固话", "联系电话", "办公电话", "家庭电话",
                "固定电话", "电话号码", "联系方式", "办公室电话"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号",
                "手机", "移动电话", "身份证", "银行卡"
            ],
            window_size=30,  # 增加窗口大小以覆盖更远的上下文关键词
            min_confidence=0.85,
            require_exact_match=False
        )
    ),

    "MedicalInsuranceRecognizer": RecognizerConfig(
        regex=r'(?<![0-9])[0-9]{15,18}(?![0-9])',
        score=0.95,
        description="医保社保卡号模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "医保卡", "社保卡", "医保号", "社保号", "医疗保险",
                "社会保险", "医保账户", "社保账户", "保险卡号", "医疗卡"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号",
                "手机", "电话", "身份证", "银行卡", "工号"
            ],
            window_size=20,
            min_confidence=0.95,
            require_exact_match=False
        )
    ),

    "BankCardRecognizer": RecognizerConfig(
        regex=r'(?<![0-9])[1-9]\d{15,18}(?![0-9])',
        score=0.95,
        description="银行卡号模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "银行卡", "卡号", "账户", "银行账户", "储蓄卡", "信用卡",
                "银行卡号", "账户号", "银行账号", "卡片号码", "银行卡账户"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号",
                "手机", "电话", "身份证", "医保卡", "工号", "学号"
            ],
            window_size=20,
            min_confidence=0.95,
            require_exact_match=False
        )
    ),

    "CertificateRecognizer": RecognizerConfig(
        regex=r'[A-Za-z0-9]{6,20}',
        score=0.90,
        description="各类证件号码模式（军官证、护照、驾驶证、工作证、居住证等）",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                # 护照相关
                "护照", "护照号", "护照号码", "passport", "Passport", "PASSPORT",
                "护照信息", "护照证件", "护照编号", "出入境证件", "国际证件",
                # 军官证相关
                "军官证", "军官证号", "军官证号码", "军人证", "军人证件", "部队证件",
                "军籍号", "军人身份证", "现役军人证", "军官身份证",
                # 驾驶证相关
                "驾驶证", "驾驶证号", "驾驶证号码", "驾照", "驾照号", "驾照号码",
                "机动车驾驶证", "驾驶执照", "驾驶证件", "驾驶证编号",
                # 工作证相关
                "工作证", "工作证号", "工作证号码", "员工证", "员工证号", "员工卡",
                "工牌", "工号", "职工证", "工作证件", "员工证件", "工作卡",
                # 居住证相关
                "居住证", "居住证号", "居住证号码", "暂住证", "暂住证号", "居住证件",
                "居住登记", "居住证编号", "临时居住证", "居住许可证",
                # 其他证件
                "证件", "证件号", "证件号码", "证书", "证书号", "执照", "执照号",
                "许可证", "许可证号", "资格证", "资格证号", "从业证", "从业证号"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号", "床位号",
                "手机", "电话", "身份证", "银行卡", "医保卡",
                "设备", "仪器", "机器", "系统", "软件", "程序",
                "医学", "医疗", "临床", "检测", "化验", "诊断"
            ],
            window_size=25,
            min_confidence=0.90,
            require_exact_match=False
        )
    ),

    "QQNumberRecognizer": RecognizerConfig(
        regex=r'(?<![0-9])[1-9]\d{4,10}(?![0-9])',
        score=0.8,
        description="QQ号模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "QQ", "QQ号", "qq", "QQ号码", "QQ账号", "qq号", "qq号码",
                "联系QQ", "加QQ", "QQ联系", "我的QQ", "QQ号是", "qq号是"
            ],
            negative_keywords=[
                "编号", "序列号", "病案号", "检验号", "患者号", "床位号",
                "电话", "手机", "身份证", "银行卡", "医保卡"
            ],
            window_size=15,
            min_confidence=0.8,
            require_exact_match=False
        )
    ),

    "GPSCoordinateRecognizer": RecognizerConfig(
        regex=r'(?<![0-9.])-?[0-9]{1,3}\.[0-9]{4,6},-?[0-9]{1,3}\.[0-9]{4,6}(?![0-9.])',
        score=0.95,
        description="GPS坐标模式（支持负数）",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                "坐标", "经纬度", "GPS", "位置", "地理位置", "定位",
                "经度", "纬度", "地标", "导航", "地图", "位置信息",
                "GPS坐标", "地理坐标", "坐标点", "位置坐标"
            ],
            negative_keywords=[
                "数字", "编号", "代码", "序列", "价格", "金额",
                "电话", "手机", "身份证", "银行卡", "时间", "日期",
                "版本", "型号", "规格", "参数", "配置"
            ],
            window_size=25,
            min_confidence=0.95,
            require_exact_match=False
        )
    ),

    "MedicalNumberRecognizer": RecognizerConfig(
        # 在本体定义
        regex=r'',
        score=0.95,
        description="病案号模式",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                # 病案相关
                "病案号", "病历号", "医疗记录", "病案", "病历", "门诊号", "住院号",
                "就诊号", "挂号", "预约号", "诊疗号",
                # 检验相关
                "标本编号", "检验编号", "化验编号", "样本编号", "标本号", "检验号",
                "化验号", "样本号", "送检编号", "报告编号", "实验编号", "测试编号",
                # 卡号相关
                "卡号", "医保卡", "就诊卡", "健康卡", "医疗卡", "诊疗卡",
                # 其他医疗编号（排除设备相关）
                "处方号", "药品编号", "床位号", "科室编号", "医师编号",
                "申请编号", "条码号", "追踪号", "流水号", "登记号", "受理号",
                "编号", "号码", "代码", "ID", "识别码", "标识符",
                "设备编号", "序列号", "设备号", "机器编号", "仪器编号", "设备序列号",
                "医疗设备", "检测设备", "监护设备", "治疗设备", "诊断设备",
            ],
            negative_keywords=[
                # 非医疗编号
                "电话", "手机", "身份证", "银行卡", "QQ", "微信",
                "车牌", "驾照", "护照", "工号", "学号", "会员号",
                # 时间和日期
                "年", "月", "日", "时", "分", "秒", "点",
                # 价格和金额
                "元", "角", "分", "价格", "金额", "费用", "收费",
                # 地址和邮编
                "省", "市", "区", "县", "街道", "路", "号", "邮编",
                # 其他数字
                "版本", "型号", "规格", "参数", "配置", "数量", "重量"
            ],
            window_size=30,
            min_confidence=0.8,
            require_exact_match=False
        )
    ),

    "OrganizationRecognizer": RecognizerConfig(
        regex=r'(?:[\u4e00-\u9fff\w]*[（\(][^）\)]*[）\)])?[\u4e00-\u9fff\w]{1,20}(?:[（\(][^）\)]*[）\)])?(?:医疗中心|医药研究所|生物医药公司|医药科技公司|集团|医院|诊所|卫生院|研究院|检验所|大学|学院|公司|企业|制药公司|股份有限公司|有限公司|有限责任公司|申办方|人民医院|中医院|妇幼保健院|专科医院|综合医院|医学院|药科大学|医科大学|协和医院|同仁医院|实验室|检测中心|体检中心|康复中心|保健院|代表处|研究中心|开发中心|医药科技|药业|制药|生物技术|CRO|Ltd\.|Inc\.|Corp\.|Co\.|Group)(?![a-zA-Z\u4e00-\u9fff])',
        score=0.85,
        description="医疗机构名称模式（整合33个机构类型）",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.LENIENT,
            positive_keywords=None,
            negative_keywords=[
                "的医生", "的护士", "的专家", "的主任", "的科室","药前"
            ],
            window_size=25,  # 适中的窗口大小
            min_confidence=0.85,
            require_exact_match=False
        )
    ),

    "GenderRecognizer": RecognizerConfig(
        regex=r'',  # 在识别器类中定义
        score=0.85,
        description="性别识别器",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=[
                '性别','患者','病员','病人'
            ],
            negative_keywords=[
            ],
            window_size=20,
            min_confidence=0.85,
            require_exact_match=False
        )
    ),

    "AgeRecognizer": RecognizerConfig(
        regex=r'',  # 在识别器类中定义
        score=0.90,
        description="年龄识别器",
        enabled=True,
        context_config=ContextConfig(
            validation_level=ValidationLevel.LENIENT,
            positive_keywords=None,
            negative_keywords=[
                # 非医疗年龄
                "工龄", "教龄", "司龄", "党龄", "军龄", "学龄",
                "使用年限", "保质期", "有效期", "存储期", "保修期",
                # 时间概念
                "历史", "年代", "时期", "时代", "世纪", "年份",
                "建立", "成立", "创建", "开业", "营业", "运营",
                # 其他数字
                "价格", "金额", "费用", "收费", "成本", "预算",
                "数量", "重量", "长度", "高度", "面积", "体积"
            ],
            window_size=25,
            min_confidence=0.90,
            require_exact_match=False
        )
    ),

    # ========================================================================
    # NLP识别器配置（spaCy内置实体识别器）
    # 配置键名使用实体类型名称，便于直接映射
    # ========================================================================

    "PERSON": RecognizerConfig(
        description="spaCy人名识别器",
        enabled=True,
        is_nlp_based=True
    ),



    "LOCATION": RecognizerConfig(
        description="spaCy位置识别器",
        enabled=True,
        is_nlp_based=True
    ),

    "NRP": RecognizerConfig(
        description="spaCy民族/国籍识别器",
        enabled=True,
        is_nlp_based=True
    ),

    "DATE_TIME": RecognizerConfig(
        description="spaCy日期时间识别器",
        enabled=True,
        is_nlp_based=True
    ),

    # ========================================================================
    # Presidio内置识别器配置
    # 只包含不会被 _remove_conflicting_recognizers 移除的识别器
    # ========================================================================

    "EMAIL_ADDRESS": RecognizerConfig(
        description="Presidio邮箱地址识别器",
        score=0.90,
        enabled=True,
        is_nlp_based=True
    ),

    "IP_ADDRESS": RecognizerConfig(
        description="Presidio IP地址识别器",
        score=0.95,
        enabled=True,
        is_nlp_based=True
    ),

    "CREDIT_CARD": RecognizerConfig(
        description="Presidio信用卡号识别器",
        score=0.95,
        enabled=True,
        is_nlp_based=True
    ),

    "IBAN_CODE": RecognizerConfig(
        description="Presidio国际银行账号识别器",
        score=0.95,
        enabled=True,
        is_nlp_based=True
    )

    # 注意：以下识别器会被 _remove_conflicting_recognizers 移除，因此不包含：
    # - URL (UrlRecognizer) - 内置URL识别器存在误识别问题
    # - PHONE_NUMBER (PhoneRecognizer) - 与自定义手机号识别器冲突
    # - MEDICAL_LICENSE (MedicalLicenseRecognizer) - 与自定义病案号识别器冲突
}


# ============================================================================
# 兼容性支持 - 保持原有接口可用
# ============================================================================

CONTEXT_CONFIGS: Dict[str, ContextConfig] = {
    class_name: config.context_config
    for class_name, config in UNIFIED_RECOGNIZER_CONFIGS.items()
    if config.is_context_based
}

MEDICAL_PATTERNS: Dict[str, Dict[str, Any]] = {
    class_name: {
        "regex": config.regex,
        "score": config.score,
        "description": config.description
    }
    for class_name, config in UNIFIED_RECOGNIZER_CONFIGS.items()
}
