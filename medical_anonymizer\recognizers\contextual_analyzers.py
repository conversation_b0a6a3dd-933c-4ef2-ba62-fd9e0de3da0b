"""
上下文判断识别器模块

本模块包含需要严格上下文验证的识别器，确保只在明确上下文中识别敏感信息。

主要特性：
1. 严格的上下文验证机制
2. 支持多级验证策略（STRICT/MODERATE/LENIENT）
3. 防止误识别，提高准确性
4. 统一的识别器框架
"""

from presidio_analyzer import Pattern, PatternRecognizer, RecognizerResult
from typing import List, Dict, Any
import time
import logging
from medical_anonymizer.recognizers.patterns import (
    MEDICAL_PATTERNS,
    CONTEXT_CONFIGS,
    ValidationLevel,
    ContextConfig
)

# 配置日志
logger = logging.getLogger(__name__)

# ============================================================================
# 上下文验证框架
# ============================================================================

# ValidationLevel 和 ContextConfig 现在从 patterns.py 导入


class EnhancedMedicalContextRecognizer(PatternRecognizer):
    """增强的医疗上下文识别器基类

    解决Presidio原生context参数无效的问题，通过重写analyze方法
    实现真正的上下文验证和结果过滤。
    """

    def __init__(self, entity_type: str, patterns: List[Pattern],
                 context_config: ContextConfig, name: str = None):
        """
        初始化增强的医疗上下文识别器

        Args:
            entity_type: 实体类型
            patterns: 模式列表
            context_config: 上下文配置
            name: 识别器名称
        """
        super().__init__(
            supported_entity=entity_type,
            patterns=patterns,
            name=name or f"Enhanced{entity_type}Recognizer",
            supported_language="zh"
        )
        self.context_config = context_config
        self.validation_level = context_config.validation_level

        # 性能监控
        self.analysis_count = 0
        self.total_time = 0.0

        logger.info(f"初始化 {self.name}，验证级别: {self.validation_level.value}")

    def analyze(self, text: str, entities: List[str],
                nlp_artifacts=None) -> List[RecognizerResult]:
        """
        重写analyze方法，实现严格的上下文验证

        这是核心方法，解决了Presidio原生context参数无法过滤结果的问题。

        Args:
            text: 待分析文本
            entities: 实体类型列表
            nlp_artifacts: NLP分析结果（可选）

        Returns:
            验证通过的识别结果列表
        """
        start_time = time.time()

        try:
            # 第1步：使用Presidio基础模式匹配
            base_results = super().analyze(text, entities, nlp_artifacts)
            if not base_results:
                return []

            logger.debug(f"{self.name} 基础匹配找到 {len(base_results)} 个结果")

            # 第2步：严格的上下文验证
            validated_results = []
            for result in base_results:
                if self._validate_context(text, result):
                    # 第3步：额外验证（子类可重写）
                    if self._additional_validation(text, result, nlp_artifacts):
                        validated_results.append(result)
                        logger.debug(f"验证通过: '{text[result.start:result.end]}'")
                    else:
                        logger.debug(f"额外验证失败: '{text[result.start:result.end]}'")
                else:
                    logger.debug(f"上下文验证失败: '{text[result.start:result.end]}'")

            logger.info(f"{self.name} 最终返回 {len(validated_results)} 个验证通过的结果")
            return validated_results

        except Exception as e:
            logger.error(f"{self.name} 分析过程中出现错误: {e}")
            return []
        finally:
            # 性能统计
            elapsed_time = time.time() - start_time
            self.analysis_count += 1
            self.total_time += elapsed_time

    def _validate_context(self, text: str, result: RecognizerResult) -> bool:
        """
        核心上下文验证逻辑

        这是解决Presidio context参数无效问题的关键方法。

        Args:
            text: 原始文本
            result: 识别结果

        Returns:
            是否通过上下文验证
        """
        # 获取上下文窗口
        context_window = self._get_context_window(text, result)

        # 正面关键词检查
        has_positive = self._check_positive_keywords(context_window)

        # 负面关键词检查
        has_negative = self._check_negative_keywords(context_window)

        # 根据验证级别决定验证策略
        if self.validation_level == ValidationLevel.STRICT:
            # 严格验证：必须有正面关键词且无负面关键词
            result = has_positive and not has_negative
            logger.debug(f"严格验证 - 正面: {has_positive}, 负面: {has_negative}, 结果: {result}")
            return result
        elif self.validation_level == ValidationLevel.MODERATE:
            # 中等验证：有正面关键词或无负面关键词
            result = has_positive or not has_negative
            logger.debug(f"中等验证 - 正面: {has_positive}, 负面: {has_negative}, 结果: {result}")
            return result
        else:  # LENIENT
            # 宽松验证：主要排除明显的负面关键词
            result = not has_negative
            logger.debug(f"宽松验证 - 负面: {has_negative}, 结果: {result}")
            return result

    def _check_positive_keywords(self, context_window: str) -> bool:
        """检查正面关键词"""
        positive_keywords = self.context_config.positive_keywords

        # 处理 positive_keywords 为 None 的情况
        if positive_keywords is None:
            logger.debug("正面关键词为None，跳过正面关键词检查")
            return False

        if self.context_config.require_exact_match:
            # 精确匹配
            found_keywords = [kw for kw in positive_keywords if kw in context_window]
        else:
            # 大小写不敏感匹配
            context_lower = context_window.lower()
            found_keywords = [kw for kw in positive_keywords
                            if kw.lower() in context_lower]

        if found_keywords:
            logger.debug(f"找到正面关键词: {found_keywords}")
            return True
        return False

    def _check_negative_keywords(self, context_window: str) -> bool:
        """检查负面关键词"""
        negative_keywords = self.context_config.negative_keywords

        # 处理 negative_keywords 为 None 的情况
        if negative_keywords is None:
            logger.debug("负面关键词为None，跳过负面关键词检查")
            return False

        found_keywords = [kw for kw in negative_keywords if kw in context_window]

        if found_keywords:
            logger.debug(f"找到负面关键词: {found_keywords}")
            return True
        return False

    def _get_context_window(self, text: str, result: RecognizerResult) -> str:
        """获取上下文窗口"""
        window_size = self.context_config.window_size
        start = max(0, result.start - window_size)
        end = min(len(text), result.end + window_size)
        context = text[start:end]
        logger.debug(f"上下文窗口: '{context}'")
        return context

    def _additional_validation(self, text: str, result: RecognizerResult,
                              nlp_artifacts=None) -> bool:
        """
        额外验证逻辑（子类可重写）

        Args:
            text: 原始文本
            result: 识别结果
            nlp_artifacts: NLP分析结果

        Returns:
            是否通过额外验证
        """
        # 基础置信度检查
        min_confidence = self.context_config.min_confidence
        if result.score < min_confidence:
            logger.debug(f"置信度不足: {result.score} < {min_confidence}")
            return False

        return True

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if self.analysis_count == 0:
            return {"analysis_count": 0, "avg_time_ms": 0}

        avg_time_ms = (self.total_time / self.analysis_count) * 1000
        return {
            "analysis_count": self.analysis_count,
            "total_time_seconds": self.total_time,
            "avg_time_ms": avg_time_ms
        }

# ============================================================================
# 核心识别器实现
# ============================================================================

class WeChatRecognizer(EnhancedMedicalContextRecognizer):
    """
    微信号识别器 - 严格上下文验证

    只有在明确出现微信相关关键词时才识别微信号，避免误识别。
    """

    def __init__(self):
        """初始化增强的微信号识别器"""

        pattern_config = MEDICAL_PATTERNS["WeChatRecognizer"]
        patterns = [Pattern(
            name="wechat_enhanced_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["WeChatRecognizer"]

        super().__init__(
            entity_type="WeChatRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="WeChatRecognizer"  # 保持原有名称以确保兼容性
        )

    def _additional_validation(self, text: str, result: RecognizerResult,
                              nlp_artifacts=None) -> bool:
        """微信号的额外验证逻辑"""

        if not super()._additional_validation(text, result, nlp_artifacts):
            return False

        matched_text = text[result.start:result.end]

        # 微信号格式验证
        # 长度检查：6-20个字符
        if len(matched_text) < 6 or len(matched_text) > 20:
            return False

        # 不能全是数字（避免与其他编号混淆）
        if matched_text.isdigit():
            return False

        # 必须以字母开头
        if not matched_text[0].isalpha():
            return False

        return True

# ============================================================================
# 升级的上下文识别器（从Pattern Matchers转移）
# ============================================================================

class MobilePhoneRecognizer(EnhancedMedicalContextRecognizer):
    """手机号码识别器 - 严格上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["MobilePhoneRecognizer"]
        patterns = [Pattern(
            name="mobile_phone_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["MobilePhoneRecognizer"]

        super().__init__(
            entity_type="MobilePhoneRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="MobilePhoneRecognizer"
        )


class LandlinePhoneRecognizer(EnhancedMedicalContextRecognizer):
    """座机电话识别器 - 严格上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["LandlinePhoneRecognizer"]
        patterns = [Pattern(
            name="landline_phone_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["LandlinePhoneRecognizer"]

        super().__init__(
            entity_type="LandlinePhoneRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="LandlinePhoneRecognizer"
        )


class MedicalInsuranceRecognizer(EnhancedMedicalContextRecognizer):
    """医保社保卡号识别器 - 严格上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["MedicalInsuranceRecognizer"]
        patterns = [Pattern(
            name="medical_insurance_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["MedicalInsuranceRecognizer"]

        super().__init__(
            entity_type="MedicalInsuranceRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="MedicalInsuranceRecognizer"
        )


class BankCardRecognizer(EnhancedMedicalContextRecognizer):
    """银行卡号识别器 - 严格上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["BankCardRecognizer"]
        patterns = [Pattern(
            name="bank_card_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["BankCardRecognizer"]

        super().__init__(
            entity_type="BankCardRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="BankCardRecognizer"
        )


# ============================================================================
# 其他上下文识别器
# ============================================================================

class CertificateRecognizer(EnhancedMedicalContextRecognizer):
    """各类证件识别器 - 支持军官证、护照、驾驶证、工作证、居住证等"""

    def __init__(self):
        # 使用通用的数字字母组合模式来匹配各种证件号码
        pattern_config = MEDICAL_PATTERNS["CertificateRecognizer"]
        patterns = [Pattern(
            name="certificate_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["CertificateRecognizer"]

        super().__init__(
            entity_type="CERTIFICATE",
            patterns=patterns,
            context_config=context_config,
            name="CertificateRecognizer"
        )


class QQNumberRecognizer(EnhancedMedicalContextRecognizer):
    """QQ号码识别器 - 严格上下文判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["QQNumberRecognizer"]
        patterns = [Pattern(
            name="qq_number_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["QQNumberRecognizer"]

        super().__init__(
            entity_type="QQNumberRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="QQNumberRecognizer"
        )


class GPSCoordinateRecognizer(EnhancedMedicalContextRecognizer):
    """GPS坐标识别器 - 中等上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["GPSCoordinateRecognizer"]
        patterns = [Pattern(
            name="gps_coordinate_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["GPSCoordinateRecognizer"]

        super().__init__(
            entity_type="GPSCoordinateRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="GPSCoordinateRecognizer"
        )


# class CommunicationContentRecognizer(EnhancedMedicalContextRecognizer):
#     """通信内容识别器 - 严格上下文验证"""
#
#     def __init__(self):
#         pattern_config = MEDICAL_PATTERNS["COMMUNICATION_CONTENT"]
#         patterns = [Pattern(
#             name="communication_content_pattern",
#             regex=pattern_config["regex"],
#             score=pattern_config["score"]
#         )]
#
#         # 配置严格的上下文验证
#         context_config = ContextConfig(
#             validation_level=ValidationLevel.STRICT,
#             positive_keywords=[
#                 "短信", "彩信", "通讯录", "群组", "邮件", "邮件列表",
#                 "通信内容", "消息", "聊天记录", "通话记录", "联系人",
#                 "微信", "QQ", "电话", "手机", "通信", "联系方式"
#             ],
#             negative_keywords=[
#                 "病历", "诊断", "治疗", "药物", "检查", "化验",
#                 "手术", "医嘱", "护理", "康复", "随访", "复查"
#             ],
#             window_size=30,
#             min_confidence=0.80,
#             require_exact_match=False
#         )
#
#         super().__init__(
#             entity_type="COMMUNICATION_CONTENT",
#             patterns=patterns,
#             context_config=context_config,
#             name="CommunicationContentRecognizer"
#         )


class MedicalNumberRecognizer(EnhancedMedicalContextRecognizer):
    """增强的医疗编号识别器 - 严格上下文验证"""

    def __init__(self):
        # 定义多个医疗编号模式
        patterns = [
            # 纯数字医疗编号模式
            Pattern(
                name="numeric_medical_number",
                regex=r'(?<![0-9])\d{6,15}(?![0-9])',
                score=0.8
            ),
            # 字母+数字医疗编号模式
            Pattern(
                name="alphanumeric_medical_number",
                regex=r'(?<![a-zA-Z0-9])[A-Z]{1,5}\d{6,15}(?![a-zA-Z0-9])',
                score=0.9
            ),
            # 数字+字母医疗编号模式
            Pattern(
                name="numeric_alpha_medical_number",
                regex=r'(?<![a-zA-Z0-9])\d{6,15}[A-Z]{1,3}(?![a-zA-Z0-9])',
                score=0.9
            ),
            # 标准医疗编号格式
            Pattern(
                name="standard_medical_number",
                regex=r'(?<![a-zA-Z0-9])[A-Z]{3}\d{9}(?![a-zA-Z0-9])',
                score=0.95
            )
        ]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["MedicalNumberRecognizer"]

        super().__init__(
            entity_type="MedicalNumberRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="MedicalNumberRecognizer"
        )


class OrganizationRecognizer(EnhancedMedicalContextRecognizer):
    """医疗机构名称识别器 - 严格上下文验证"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["OrganizationRecognizer"]
        patterns = [Pattern(
            name="medical_organization_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["OrganizationRecognizer"]

        super().__init__(
            entity_type="OrganizationRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="OrganizationRecognizer"
        )


class GenderRecognizer(EnhancedMedicalContextRecognizer):
    """性别识别器 - 严格上下文验证

    识别医疗文本中的性别信息，包括：
    - 基本性别：男、女、男性、女性
    - 医疗记录中的性别表述
    - 避免与其他词汇的误识别

    特点：
    - 结合医疗上下文关键词提高准确性
    - 支持多种性别表述方式
    - 避免在非医疗语境中的误识别
    """

    def __init__(self):
        # 性别识别的正则表达式模式
        patterns = [
            # 基本性别词汇
            Pattern(
                name="basic_gender",
                regex=r'\b(男|女)\b',
                score=0.85
            ),
            # 性别 + 性字
            Pattern(
                name="gender_with_suffix",
                regex=r'\b(男性|女性)\b',
                score=0.90
            ),
        ]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["GenderRecognizer"]

        super().__init__(
            entity_type="GenderRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="GenderRecognizer"
        )


class AgeRecognizer(EnhancedMedicalContextRecognizer):
    """年龄识别器 - 严格上下文验证

    识别医疗文本中的年龄信息，包括：
    - 标准年龄：35岁、45周岁
    - 婴幼儿年龄：3个月、2岁半
    - 医疗记录中的年龄表述

    特点：
    - 结合医疗上下文关键词提高准确性
    - 支持多种年龄表述方式
    - 避免与其他数字的误识别
    """

    def __init__(self):
        # 年龄识别的正则表达式模式
        patterns = [
            # 标准年龄格式：数字 + 岁
            Pattern(
                name="standard_age",
                regex=r'\b(\d{1,3}岁)\b',
                score=0.90
            ),
            # 周岁格式
            Pattern(
                name="full_age",
                regex=r'\b(\d{1,3}周岁)\b',
                score=0.95
            ),
            # 年龄范围
            Pattern(
                name="age_range",
                regex=r'\b(\d{1,3}-\d{1,3}岁)\b',
                score=0.85
            ),
            # 婴幼儿年龄：月龄
            Pattern(
                name="month_age",
                regex=r'\b(\d{1,2}个?月龄?)\b',
                score=0.90
            ),
            # 婴幼儿年龄：天数（需要强上下文）
            # Pattern(
            #     name="day_age",
            #     regex=r'\b(\d{1,3}天)\b',
            #     score=0.80
            # ),
            # 小数年龄：2.5岁、3岁半
            Pattern(
                name="decimal_age",
                regex=r'\b(\d{1,2}[\.．]\d岁|\d{1,2}岁半)\b',
                score=0.95
            ),
            # 年龄描述：年龄为、年龄是
            Pattern(
                name="age_with_label",
                regex=r'(?:年龄|年龄为|年龄是)[:：\s]*(\d{1,3}岁|\d{1,3}周岁|\d{1,2}个?月)\b',
                score=0.95
            ),
            # 患者年龄描述
            Pattern(
                name="patient_age",
                regex=r'(?:患者|病人|病员).*?(\d{1,3}岁|\d{1,3}周岁|\d{1,2}个?月)\b',
                score=0.90
            ),
            # 性别年龄组合中的年龄
            Pattern(
                name="gender_age_combo",
                regex=r'\b(?:男|女|男性|女性)[，,]?\s*(\d{1,3}岁|\d{1,3}周岁)\b',
                score=0.95
            ),
            # 年龄性别组合中的年龄
            Pattern(
                name="age_gender_combo",
                regex=r'\b(\d{1,3}岁|\d{1,3}周岁)\s*[，,]?\s*(?:男|女|男性|女性)\b',
                score=0.95
            )
        ]

        # 使用集中配置的上下文验证
        context_config = CONTEXT_CONFIGS["AgeRecognizer"]

        super().__init__(
            entity_type="AgeRecognizer",
            patterns=patterns,
            context_config=context_config,
            name="AgeRecognizer"
        )








# ============================================================================
# 统一导出接口
# ============================================================================

# 上下文判断识别器列表
CONTEXT_BASED_RECOGNIZERS = [
    # 升级的上下文识别器（从Pattern Matchers转移）
    MobilePhoneRecognizer,
    LandlinePhoneRecognizer,
    MedicalInsuranceRecognizer,
    BankCardRecognizer,
    # 原有的上下文识别器
    WeChatRecognizer,
    CertificateRecognizer,
    QQNumberRecognizer,
    # FamilyRelationshipRecognizer,
    # MedicalPositionRecognizer,
    OrganizationRecognizer,
    # 从advanced_detectors迁移的识别器
    GPSCoordinateRecognizer,
    # CommunicationContentRecognizer,
    MedicalNumberRecognizer,
    # 新增的上下文识别器
    GenderRecognizer,
    AgeRecognizer,
]


def get_context_based_recognizers():
    """
    获取所有启用的上下文判断识别器实例

    只返回在统一配置中标记为 enabled=True 的识别器实例。
    使用识别器类名直接作为配置键，无需维护映射关系。
    """
    from .patterns import UNIFIED_RECOGNIZER_CONFIGS

    enabled_recognizers = []

    for recognizer_class in CONTEXT_BASED_RECOGNIZERS:
        class_name = recognizer_class.__name__

        # 直接使用类名作为配置键查找
        if class_name in UNIFIED_RECOGNIZER_CONFIGS:
            config = UNIFIED_RECOGNIZER_CONFIGS[class_name]
            if config.enabled:
                enabled_recognizers.append(recognizer_class())
        else:
            # 如果没有找到对应配置，默认启用（向后兼容）
            enabled_recognizers.append(recognizer_class())

    return enabled_recognizers


def get_context_based_recognizer_names():
    """
    获取所有启用的上下文判断识别器的名称

    只返回启用的识别器名称，与 get_context_based_recognizers() 保持一致。
    使用识别器类名直接作为配置键，无需维护映射关系。
    """
    from .patterns import UNIFIED_RECOGNIZER_CONFIGS

    enabled_names = []

    for recognizer_class in CONTEXT_BASED_RECOGNIZERS:
        class_name = recognizer_class.__name__

        # 直接使用类名作为配置键查找
        if class_name in UNIFIED_RECOGNIZER_CONFIGS:
            config = UNIFIED_RECOGNIZER_CONFIGS[class_name]
            if config.enabled:
                enabled_names.append(class_name)
        else:
            # 如果没有找到对应配置，默认启用（向后兼容）
            enabled_names.append(class_name)

    return enabled_names


# 动态生成 __all__ 列表，减少维护负担
__all__ = (
    # 识别器类名（从 CONTEXT_BASED_RECOGNIZERS 动态生成）
    [recognizer_class.__name__ for recognizer_class in CONTEXT_BASED_RECOGNIZERS] +

    # 框架组件
    [
        'ValidationLevel',
        'ContextConfig',
        'EnhancedMedicalContextRecognizer',
    ] +

    # 统一接口
    [
        'get_context_based_recognizers',
        'get_context_based_recognizer_names',
        'CONTEXT_BASED_RECOGNIZERS'
    ]
)
