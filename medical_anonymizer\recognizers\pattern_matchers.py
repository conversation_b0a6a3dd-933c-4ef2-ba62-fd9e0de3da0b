"""
模式匹配器模块

本模块包含基于正则表达式模式的高精度识别器，
这些匹配器使用精确的模式规则进行识别，无需额外的上下文验证。

技术特征：
- 基于正则表达式的精确模式匹配
- 高置信度识别（通常0.95-0.98）
- 快速响应，低计算开销
- 识别精度高，误报率极低
- 适用于格式固定的敏感信息
"""

from presidio_analyzer import Pattern, PatternRecognizer
from medical_anonymizer.recognizers.patterns import MEDICAL_PATTERNS


class ChineseIDRecognizer(PatternRecognizer):
    """中国身份证号码识别器 - 纯规则判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["ChineseIDRecognizer"]
        patterns = [Pattern(
            name="chinese_id_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]  # 0.98 高置信度
        )]

        super().__init__(
            supported_entity="ChineseIDRecognizer",
            patterns=patterns,
            name="ChineseIDRecognizer",
            supported_language="zh"
        )


class LicensePlateRecognizer(PatternRecognizer):
    """车牌号识别器 - 纯规则判断"""
    
    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["LicensePlateRecognizer"]
        patterns = [Pattern(
            name="license_plate_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]  # 0.9 高置信度
        )]
        
        super().__init__(
            supported_entity="LicensePlateRecognizer",
            patterns=patterns,
            name="LicensePlateRecognizer",
            supported_language="zh"
        )



class EthnicityRecognizer(PatternRecognizer):
    """民族信息识别器 - 纯规则判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["EthnicityRecognizer"]
        patterns = [Pattern(
            name="ethnicity_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        super().__init__(
            supported_entity="EthnicityRecognizer",
            patterns=patterns,
            name="EthnicityRecognizer",
            supported_language="zh"
        )


class EducationLevelRecognizer(PatternRecognizer):
    """学历学位识别器 - 纯规则判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["EducationLevelRecognizer"]
        patterns = [Pattern(
            name="education_level_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        super().__init__(
            supported_entity="EducationLevelRecognizer",
            patterns=patterns,
            name="EducationLevelRecognizer",
            supported_language="zh"
        )


class PrivacyInfoRecognizer(PatternRecognizer):
    """隐私敏感信息识别器 - 纯规则判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["PrivacyInfoRecognizer"]
        patterns = [Pattern(
            name="privacy_info_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        super().__init__(
            supported_entity="PrivacyInfoRecognizer",
            patterns=patterns,
            name="PrivacyInfoRecognizer",
            supported_language="zh"
        )


class CompleteAddressRecognizer(PatternRecognizer):
    """完整地址识别器 - 纯规则判断"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["CompleteAddressRecognizer"]
        patterns = [Pattern(
            name="complete_address_pattern",
            regex=pattern_config["regex"],
            score=pattern_config["score"]
        )]

        super().__init__(
            supported_entity="CompleteAddressRecognizer",
            patterns=patterns,
            name="CompleteAddressRecognizer",
            supported_language="zh"
        )


class URLRecognizer(PatternRecognizer):
    """严格的URL识别器 - 纯规则判断"""

    def __init__(self):
        patterns = [
            # HTTP/HTTPS URL模式
            Pattern(
                name="http_https_url",
                regex=r'https?://[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*(?:/[^\s]*)?',
                score=0.95
            ),
            # FTP URL模式
            Pattern(
                name="ftp_url",
                regex=r'ftp://[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*(?:/[^\s]*)?',
                score=0.95
            ),
            # 邮件协议URL
            Pattern(
                name="mailto_url",
                regex=r'mailto:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                score=0.90
            ),
            # WWW开头的URL（严格模式）
            Pattern(
                name="www_url_strict",
                regex=r'www\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z]{2,})+(?:/[^\s]*)?(?=\s|$|[,.;!?])',
                score=0.85
            )
        ]

        super().__init__(
            supported_entity="URLRecognizer",
            patterns=patterns,
            name="URLRecognizer",
            supported_language="zh"
        )


class StructuredFieldRecognizer(PatternRecognizer):
    """结构化字段识别器 - 专门识别"字段：值"格式的敏感信息（纯规则匹配）"""

    def __init__(self):
        # 提供一个虚拟模式以满足Presidio要求
        dummy_pattern = Pattern(
            name="dummy_pattern",
            regex=r'__DUMMY_PATTERN_NEVER_MATCH__',
            score=0.1
        )

        super().__init__(
            patterns=[dummy_pattern],
            supported_entity="StructuredFieldRecognizer",
            name="StructuredFieldRecognizer",
            supported_language="zh"
        )

    def analyze(self, text, entities, nlp_artifacts=None):
        """重写analyze方法以只提取冒号后的内容"""
        import re
        from presidio_analyzer import RecognizerResult

        results = []

        # 定义所有字段模式和对应的置信度
        field_patterns = [
            # 基本信息
            (r'姓名[：:]\s*([\u4e00-\u9fff]{2,4})', 0.95),
            # (r'年龄[：:]\s*(\d{1,3})(?:岁)?', 0.85),
            # (r'性别[：:]\s*([男女])', 0.85),

            # 职业相关
            (r'职业[：:]\s*([\u4e00-\u9fff\w\s]{2,20}?)(?=\s*[•\n\r，。；]|$)', 0.85),
            (r'职位[：:]\s*([\u4e00-\u9fff\w\s]{2,20}?)(?=\s*[•\n\r，。；]|$)', 0.85),
            (r'工作单位[：:]\s*([\u4e00-\u9fff\w\s]{2,30}?)(?=\s*[•\n\r，。；]|$)', 0.85),

            # 联系方式
            (r'(?:电话|手机|联系电话)[：:]\s*(1[3-9]\d{9})', 0.95),
            (r'(?:电话|手机|联系电话)[：:]\s*(0\d{2,3}-?\d{7,8})', 0.95),
            (r'(?:邮箱|电子邮件|Email)[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 0.95),
            (r'(?:地址|住址|居住地|通讯地址)[：:]\s*([\u4e00-\u9fff0-9\s#()-]{5,50}?)(?=\s*[•\n\r，。；]|$)', 0.85),
            (r'微信[号]*[：:]\s*([\w\-]{6,20})', 0.90),
            (r'QQ[号]*[：:]\s*(\d{5,11})', 0.90),

            # 身份证件
            (r'身份证[号码]*[：:]\s*(\d{17}[\dxX])', 0.95),

            # 生物特征
            (r'身高[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)?', 0.80),
            (r'体重[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:kg|公斤)?', 0.80),
            (r'血型[：:]\s*([ABO]{1,2}[+-](?:型?|RH[+-])?)', 0.85),

            # 民族和国籍
            (r'民族[：:]\s*([\u4e00-\u9fff]{1,8}族)', 0.85),
            (r'国籍[：:]\s*([\u4e00-\u9fff]{2,})', 0.85),

            # 银行卡信息
            (r'银行卡[号码]*[：:]\s*(\d{16,19})', 0.95),

            # 家庭关系
            (r'配偶[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),
            (r'父亲[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),
            (r'母亲[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),

            # 人名
            (r'检验者[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),
            (r'核对者[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),



            # 医疗相关字段
            (r'病案号[：:]\s*(\d{4,})', 0.95),
            (r'床号[：:]\s*(\d{1,4})', 0.85),
            (r'门诊号[：:]\s*(\d{4,})', 0.95),
            (r'住院号[：:]\s*(\d{4,})', 0.95),
            (r'病历号[：:]\s*(\d{4,})', 0.95),
            (r'条形码号[：:]\s*(\d{4,})', 0.95),


            # 隐私信息
            (r'犯罪记录[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),
            (r'婚姻状况[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),
            (r'性取向[：:]\s*([\u4e00-\u9fff]{2,4})', 0.90),

            # 标本编号
            (r'标本号[：:]\s*(\d{2,})', 0.95),
            # 流水号
            (r'流水号[：:]\s*(\d{2,})', 0.95),
            # 条形码
            (r'条形码[：:]\s*(\d{2,})', 0.95),
            # 条形码号
            (r'条形码号[：:]\s*(\d{4,})', 0.95),
            # 条形码号
            (r'条形码号[：:]\s*(\d{4,})', 0.95),
        ]

        for pattern, score in field_patterns:
            for match in re.finditer(pattern, text):
                # 只提取捕获组中的内容（冒号后的值）
                if match.group(1):
                    value_start = match.start(1)
                    value_end = match.end(1)

                    result = RecognizerResult(
                        entity_type="StructuredFieldRecognizer",
                        start=value_start,
                        end=value_end,
                        score=score
                    )
                    results.append(result)

        return results


# class DateTimeRecognizer(PatternRecognizer):
#     """优化的时间日期识别器 - 收紧识别规则，避免医疗数值误识别"""
#
#     def __init__(self):
#         patterns = [Pattern(
#             name="datetime_pattern",
#             regex='',
#             score=0.90
#         )]
#
#         super().__init__(
#             supported_entity="DATE_TIME",
#             patterns=patterns,
#             name="DateTimeRecognizer",
#             supported_language="zh"
#         )

    # def analyze(self, text, entities, nlp_artifacts=None):
    #     """重写analyze方法以提供更精确的时间日期识别，避免医疗数值误识别"""
    #     import re
    #     from presidio_analyzer import RecognizerResult
    #
    #     results = []
    #
    #     # 定义严格的时间日期模式 - 只识别明确的时间日期格式
    #     datetime_patterns = [
    #         # 完整日期格式（高置信度）
    #         # (r'\d{4}年\d{1,2}月\d{1,2}[日号]', 0.95, "完整中文日期"),
    #         # (r'\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}', 0.95, "数字日期格式"),
    #
    #         # 带时段的完整时间（高置信度）
    #         # (r'(?:上午|下午|早上|晚上|中午|凌晨)\s*\d{1,2}[：:]\d{1,2}', 0.95, "带时段的时间"),
    #
    #         # 相对时间（明确的时间词汇）
    #         # (r'(?:今天|昨天|明天|前天|后天|今日|昨日|明日)', 0.90, "相对日期"),
    #         # (r'(?:星期|周)[一二三四五六七天日1-7]', 0.90, "星期格式"),
    #
    #         # 节假日（明确的时间标识）
    #         # (r'(?:元旦|春节|清明|劳动节|端午|中秋|国庆|圣诞)节?', 0.85, "节假日"),
    #     ]
    #
    #
    #     for pattern, score, description in datetime_patterns:
    #         for match in re.finditer(pattern, text):
    #             matched_text = match.group()
    #             start_pos = match.start()
    #             end_pos = match.end()
    #
    #             result = RecognizerResult(
    #                 entity_type="DATE_TIME",
    #                 start=start_pos,
    #                 end=end_pos,
    #                 score=score
    #             )
    #             results.append(result)
    #
    #     return results



class SponsorRecognizer(PatternRecognizer):
    """申办方识别器 - 纯规则匹配特定申办方名称"""

    def __init__(self):
        pattern_config = MEDICAL_PATTERNS["SponsorRecognizer"]

        # 提供一个虚拟模式以满足Presidio要求
        dummy_pattern = Pattern(
            name="sponsor_pattern",
            regex=r'__DUMMY_PATTERN_NEVER_MATCH__',
            score=pattern_config["score"]
        )

        super().__init__(
            patterns=[dummy_pattern],
            supported_entity="SponsorRecognizer",
            name="SponsorRecognizer",
            supported_language="zh"
        )

    def analyze(self, text, entities, nlp_artifacts=None):
        """重写analyze方法以直接匹配申办方名称"""
        import re
        from presidio_analyzer import RecognizerResult

        results = []

        # 从MEDICAL_PATTERNS获取申办方名称列表和置信度
        pattern_config = MEDICAL_PATTERNS["SponsorRecognizer"]
        # 为了匹配其他配置，这里的名称也用regex
        sponsor_names = pattern_config["regex"]
        score = pattern_config["score"]

        # 直接匹配申办方名称
        for sponsor_name in sponsor_names:
            # 使用正则表达式进行精确匹配
            pattern = re.escape(sponsor_name)  # 转义特殊字符

            for match in re.finditer(pattern, text):
                result = RecognizerResult(
                    entity_type="SponsorRecognizer",
                    start=match.start(),
                    end=match.end(),
                    score=score
                )
                results.append(result)

        return results


# 纯规则判断识别器列表
RULE_BASED_RECOGNIZERS = [
    ChineseIDRecognizer,
    LicensePlateRecognizer,
    EthnicityRecognizer,
    EducationLevelRecognizer,
    PrivacyInfoRecognizer,
    CompleteAddressRecognizer,
    URLRecognizer,
    StructuredFieldRecognizer,
    # DateTimeRecognizer,
    SponsorRecognizer,
]


def get_rule_based_recognizers():
    """
    获取所有启用的纯规则判断识别器实例

    只返回在统一配置中标记为 enabled=True 的识别器实例。
    使用识别器类名直接作为配置键，无需维护映射关系。
    """
    from .patterns import UNIFIED_RECOGNIZER_CONFIGS

    enabled_recognizers = []

    for recognizer_class in RULE_BASED_RECOGNIZERS:
        class_name = recognizer_class.__name__

        # 直接使用类名作为配置键查找
        if class_name in UNIFIED_RECOGNIZER_CONFIGS:
            config = UNIFIED_RECOGNIZER_CONFIGS[class_name]
            if config.enabled:
                enabled_recognizers.append(recognizer_class())
        else:
            # 如果没有找到对应配置，默认启用（向后兼容）
            enabled_recognizers.append(recognizer_class())

    return enabled_recognizers


def get_rule_based_recognizer_names():
    """
    获取所有启用的纯规则判断识别器的名称

    只返回启用的识别器名称，与 get_rule_based_recognizers() 保持一致。
    使用识别器类名直接作为配置键，无需维护映射关系。
    """
    from .patterns import UNIFIED_RECOGNIZER_CONFIGS

    enabled_names = []

    for recognizer_class in RULE_BASED_RECOGNIZERS:
        class_name = recognizer_class.__name__

        # 直接使用类名作为配置键查找
        if class_name in UNIFIED_RECOGNIZER_CONFIGS:
            config = UNIFIED_RECOGNIZER_CONFIGS[class_name]
            if config.enabled:
                enabled_names.append(class_name)
        else:
            # 如果没有找到对应配置，默认启用（向后兼容）
            enabled_names.append(class_name)

    return enabled_names
