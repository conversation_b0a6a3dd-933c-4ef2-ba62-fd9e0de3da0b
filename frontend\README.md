# HIPAA医疗数据脱敏系统 - 前端演示页面

## 📋 项目简介

这是HIPAA医疗数据脱敏系统的专业前端演示页面，专为向领导展示系统功能而设计。页面提供了直观的用户界面，支持文本输入和图片上传两种方式进行医疗数据脱敏演示。

## ✨ 主要功能

### 1. 文本输入脱敏
- **直接输入**：支持在文本框中直接输入待脱敏的医疗文本
- **示例数据**：提供一键加载示例数据功能，方便快速演示
- **实时处理**：点击"开始脱敏"按钮即可获得脱敏结果

### 2. 图片上传OCR
- **多种上传方式**：支持拖拽上传和点击选择文件
- **格式支持**：支持JPG、PNG、GIF格式图片，最大10MB
- **OCR文本提取**：自动调用OCR服务提取图片中的文本内容
- **一键处理**：提取文本后自动进行脱敏处理

### 3. 脱敏结果展示（全新优化布局）
- **快速导航**：结果区域顶部的快速导航目录，一键跳转到各个区域
- **敏感信息标注**：优先展示高亮标注的原文，最直观易懂
- **处理统计**：统计信息（处理时间、敏感信息数量）显示在标注卡片头部
- **脱敏文本**：可折叠的脱敏结果卡片，默认折叠节省空间
- **高亮切换**：支持开启/关闭敏感信息高亮显示，修复OCR场景bug

### 4. 敏感信息详情（全新分组展示）
- **智能分组**：按7大类别自动分组显示（个人信息、联系方式、医疗信息等）
- **紧凑布局**：网格布局提高空间利用率，支持折叠/展开操作
- **中文分类**：每个敏感信息显示中文类别名称和颜色标识
- **置信度**：显示检测的置信度百分比
- **全局控制**：一键展开/折叠所有分组
- **导出功能**：支持将结果导出为JSON格式

## 🚀 快速开始

### 1. 启动后端服务

首先确保HIPAA脱敏API服务正在运行：

```bash
# 进入项目根目录
cd /path/to/hipaa-deidentify

# 启动脱敏服务
cd api
python start_service.py

# 或者使用Docker
./run_docker.sh
```

服务启动后，API将在 `http://127.0.0.1:50505` 上运行。

### 2. 配置OCR服务

确保OCR服务在 `http://*************:8011` 上运行，或者修改 `script.js` 中的OCR_API配置：

```javascript
const CONFIG = {
    DEIDENTIFY_API: 'http://127.0.0.1:50505/deidentify',
    OCR_API: 'http://*************:8011/ocr/single',  // 修改为实际的OCR服务地址
    // ...
};
```

### 3. 启动前端页面

由于浏览器的CORS限制，建议使用本地HTTP服务器运行前端页面：

```bash
# 进入前端目录
cd frontend

# 使用Python启动简单HTTP服务器
python -m http.server 8080

# 或者使用Node.js的http-server
npx http-server -p 8080

# 或者使用PHP
php -S localhost:8080
```

然后在浏览器中访问：`http://localhost:8080`

## 🎯 使用指南

### 文本脱敏演示

1. **加载示例数据**：
   - 点击"加载示例"按钮，自动填入包含多种敏感信息的示例文本
   - 示例包含：姓名、身份证、电话、地址、医疗信息等

2. **开始脱敏**：
   - 点击"开始脱敏"按钮
   - 系统将自动检测并脱敏文本中的敏感信息

3. **查看结果**：
   - 脱敏结果：查看处理后的安全文本
   - 敏感信息标注：查看原文中的敏感信息高亮显示
   - 详情列表：查看所有检测到的敏感信息及其类别

### 图片OCR脱敏演示

1. **上传图片**：
   - 切换到"图片上传"标签页
   - 拖拽图片到上传区域或点击选择文件

2. **提取并脱敏**：
   - 点击"提取文本并脱敏"按钮
   - 系统将先调用OCR提取文本，然后进行脱敏处理

3. **查看结果**：
   - 与文本脱敏相同的结果展示方式

## 🔧 配置说明

### API配置

在 `script.js` 文件中可以修改以下配置：

```javascript
const CONFIG = {
    DEIDENTIFY_API: 'http://127.0.0.1:50505/deidentify',  // 脱敏API地址
    OCR_API: 'http://*************:8011/ocr/single',      // OCR API地址
    MAX_FILE_SIZE: 10 * 1024 * 1024,                      // 最大文件大小(10MB)
    SUPPORTED_FORMATS: ['image/jpeg', 'image/png', 'image/gif']  // 支持的图片格式
};
```

### 敏感信息类型映射

系统支持以下敏感信息类型的中文显示：

- 身份证号、手机号码、病案号、微信号
- 银行卡号、护照号、医保卡号
- 人名、机构名称、地名、地址
- 邮箱地址、网址、IP地址
- 民族、学历学位、医疗职位
- 生物特征、隐私信息等

## 🎨 界面特色

- **专业设计**：简洁现代的界面设计，适合向领导演示
- **响应式布局**：支持不同屏幕尺寸的设备
- **智能分组**：敏感信息按类型分组，提高查看效率
- **紧凑布局**：网格展示节省空间，支持折叠/展开操作
- **直观操作**：清晰的操作流程和用户反馈
- **实时状态**：加载状态提示和错误处理
- **数据导出**：支持将脱敏结果导出为JSON格式

## 🔍 支持的敏感信息类型

系统可以检测和脱敏以下类型的敏感信息：

### 个人身份信息
- 姓名、身份证号、手机号、微信号
- 邮箱地址、护照号、银行卡号

### 医疗相关信息
- 病案号、医保卡号、医疗职位
- 医疗机构名称、医疗执照

### 地理位置信息
- 完整地址、地名、GPS坐标

### 其他敏感信息
- 民族、学历学位、隐私信息
- 车辆信息、设备序列号、通信内容

## 📱 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🚨 注意事项

1. **网络连接**：确保能够访问脱敏API和OCR服务
2. **CORS设置**：如果遇到跨域问题，请配置API服务的CORS设置
3. **文件大小**：图片文件大小限制为10MB
4. **数据安全**：演示数据仅用于展示，请勿使用真实敏感数据

## 🔧 故障排除

### 常见问题

1. **无法连接到脱敏服务**
   - 检查API服务是否正常运行
   - 确认API地址配置是否正确

2. **OCR服务连接失败**
   - 检查OCR服务是否可访问
   - 确认网络连接和防火墙设置

3. **图片上传失败**
   - 检查文件格式和大小是否符合要求
   - 确认浏览器支持FileReader API

4. **页面显示异常**
   - 使用现代浏览器访问
   - 检查JavaScript是否被禁用

如有其他问题，请检查浏览器控制台的错误信息。
