/**
 * 事件处理模块
 * 处理所有用户界面交互和事件监听器绑定
 */

// 事件处理模块 - 避免变量重复声明，直接使用模块方法

// 示例数据
const EXAMPLE_TEXT = `患者基本信息：
姓名：张三
性别：男
年龄：45岁
身份证号：110101197801011234
联系电话：13800138000
家庭住址：北京市朝阳区建国路88号

就诊信息：
就诊日期：2024年1月15日
科室：心内科
主治医师：李医生
病历号：BL20240115001

检查结果：
血压：140/90 mmHg
心率：78次/分
血红蛋白：120 g/L
白细胞计数：6.5×10^9/L
血小板计数：250×10^9/L

诊断：高血压病2级
治疗方案：口服降压药物，定期复查`;

// UI工具函数
const UIUtils = {
    showError: function(message) {
        const elements = window.DOMUtils.elements;
        if (elements.errorMessage) {
            elements.errorText.textContent = message;
            elements.errorMessage.style.display = 'block';
            setTimeout(() => {
                elements.errorMessage.style.display = 'none';
            }, 5000);
        }
        console.error('错误:', message);
    },

    showLoading: function(message = '处理中...') {
        const elements = window.DOMUtils.elements;
        if (elements.loading) {
            if (elements.loadingText) {
                elements.loadingText.textContent = message;
            }
            elements.loading.style.display = 'flex';
        }
    },

    hideLoading: function() {
        const elements = window.DOMUtils.elements;
        if (elements.loading) {
            elements.loading.style.display = 'none';
        }
    }
};

// 将UIUtils暴露给全局
window.UIUtils = UIUtils;

// 标签页切换
function switchTab(tabName) {
    console.log(`切换到标签页: ${tabName}`);

    const elements = window.DOMUtils.elements;

    // 更新标签按钮状态
    if (elements.tabBtns) {
        elements.tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
    }

    // 更新标签内容显示
    if (elements.tabContents) {
        elements.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });
    }
}

// 文件选择处理
function handleFileSelect(e) {
    console.log('handleFileSelect 被调用，文件数量:', e.target.files.length);
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        console.log('开始处理批量文件上传');
        // 支持批量上传
        window.BatchImageProcessing.handleBatchFileUpload(files);
    } else {
        console.log('没有选择文件');
    }
}

// 清空文本
function clearText() {
    console.log('清空文本按钮被点击');
    const elements = window.DOMUtils.elements;
    if (elements.textInput) {
        elements.textInput.value = '';
        console.log('✓ 文本已清空');
    } else {
        console.error('❌ 文本输入框不存在');
    }
    window.ResultDisplay.hideResults();
}

// 处理文本脱敏
async function processText() {
    console.log('开始脱敏按钮被点击');

    const elements = window.DOMUtils.elements;
    if (!elements.textInput) {
        console.error('❌ 文本输入框不存在');
        UIUtils.showError('文本输入框不存在');
        return;
    }

    const text = elements.textInput.value.trim();
    if (!text) {
        console.log('⚠️ 文本为空');
        UIUtils.showError('请输入待脱敏的文本');
        return;
    }
    
    console.log('✓ 开始处理文本脱敏，文本长度:', text.length);
    
    UIUtils.showLoading('正在进行脱敏处理...');
    
    try {
        const startTime = Date.now();
        const results = await window.APIClient.callDeidentifyAPI(text);
        const processingTime = (Date.now() - startTime) / 1000;
        
        console.log('脱敏处理完成，耗时:', processingTime, '秒');
        
        // 添加处理时间到结果中
        results.processing_time = processingTime;
        
        // 显示结果
        window.ResultDisplay.showResults(results, text);
        
    } catch (error) {
        console.error('脱敏处理失败:', error);
        UIUtils.showError(`脱敏处理失败: ${error.message}`);
    } finally {
        UIUtils.hideLoading();
    }
}

// 加载示例数据
function loadExampleData() {
    console.log('加载示例按钮被点击');

    const elements = window.DOMUtils.elements;
    if (!elements.textInput) {
        console.error('❌ 文本输入框不存在');
        UIUtils.showError('文本输入框不存在');
        return;
    }

    elements.textInput.value = EXAMPLE_TEXT;
    console.log('✓ 示例数据已加载');

    // 切换到文本输入标签页
    switchTab('text');

    // 滚动到文本输入区域
    elements.textInput.scrollIntoView({ behavior: 'smooth' });
    elements.textInput.focus();
}

// 初始化拖拽上传
function initializeDragAndDrop() {
    const elements = window.DOMUtils.elements;
    if (!elements.uploadArea) {
        console.warn('⚠️ 未找到上传区域，跳过拖拽上传初始化');
        return;
    }
    
    elements.uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    elements.uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    elements.uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            // 支持批量拖拽上传
            window.BatchImageProcessing.handleBatchFileUpload(files);
        }
    });

    elements.uploadArea.addEventListener('click', function(e) {
        // 只有当点击的不是按钮时才触发文件选择
        if (e.target.tagName !== 'BUTTON' && !e.target.closest('button')) {
            if (elements.fileInput) {
                elements.fileInput.click();
            }
        }
    });
    
    console.log('✓ 拖拽上传功能已初始化');
}

// 初始化所有事件监听器
function initializeEventListeners() {
    console.log('开始初始化事件监听器...');

    const elements = window.DOMUtils.elements;

    // 标签页切换
    if (elements.tabBtns && elements.tabBtns.length > 0) {
        elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', () => switchTab(btn.dataset.tab));
        });
        console.log(`✓ 标签页切换事件已绑定 (${elements.tabBtns.length}个标签)`);
    } else {
        console.warn('⚠️ 未找到标签页按钮 .tab-btn');
    }

    // 按钮事件 - 添加null检查
    const clearTextBtn = document.getElementById('clear-text-btn');
    if (clearTextBtn) {
        clearTextBtn.addEventListener('click', clearText);
        console.log('✓ 清空按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到清空按钮 #clear-text-btn');
    }
    
    const processTextBtn = document.getElementById('process-text-btn');
    if (processTextBtn) {
        processTextBtn.addEventListener('click', processText);
        console.log('✓ 开始脱敏按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到开始脱敏按钮 #process-text-btn');
    }
    
    const toggleHighlightBtn = document.getElementById('toggle-highlight');
    if (toggleHighlightBtn) {
        toggleHighlightBtn.addEventListener('click', window.ResultDisplay.toggleHighlight);
        console.log('✓ 切换高亮按钮事件已绑定');
    }
    
    const exportResultsBtn = document.getElementById('export-results');
    if (exportResultsBtn) {
        exportResultsBtn.addEventListener('click', window.ResultDisplay.exportResults);
        console.log('✓ 导出结果按钮事件已绑定');
    }

    // 添加示例数据按钮事件
    const loadExampleBtn = document.getElementById('load-example-btn');
    if (loadExampleBtn) {
        loadExampleBtn.addEventListener('click', loadExampleData);
        console.log('✓ 加载示例按钮事件已绑定');
    } else {
        console.warn('⚠️ 未找到加载示例按钮 #load-example-btn');
    }

    // 文件选择
    if (elements.fileInput) {
        console.log('找到文件输入元素，绑定change事件');
        elements.fileInput.addEventListener('change', handleFileSelect);
    } else {
        console.error('未找到文件输入元素 #file-input');
    }
    
    // 选择文件按钮点击事件
    const selectFilesBtn = document.getElementById('select-files-btn');
    if (selectFilesBtn) {
        console.log('找到选择文件按钮，绑定点击事件');
        selectFilesBtn.addEventListener('click', function(e) {
            console.log('选择文件按钮被点击');
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 阻止默认行为
            elements.fileInput.click();
        });
    } else {
        console.error('未找到选择文件按钮 #select-files-btn');
    }
    
    // 批量图片相关事件
    if (elements.clearAllImagesBtn) {
        elements.clearAllImagesBtn.addEventListener('click', window.BatchImageProcessing.clearAllImages);
        console.log('✓ 清空所有图片按钮事件已绑定');
    }
    
    if (elements.processAllImagesBtn) {
        elements.processAllImagesBtn.addEventListener('click', window.BatchImageProcessing.processAllImages);
        console.log('✓ 批量处理按钮事件已绑定');
    }

    if (elements.reprocessAllImagesBtn) {
        elements.reprocessAllImagesBtn.addEventListener('click', window.BatchImageProcessing.reprocessAllImages);
        console.log('✓ 重新批量推理按钮事件已绑定');
    }

    // 结果标签页切换
    if (elements.resultTabBtns && elements.resultTabBtns.length > 0) {
        elements.resultTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                window.ResultDisplay.switchResultTab(e.target.dataset.tab);
            });
        });
        console.log(`✓ 结果标签页切换事件已绑定 (${elements.resultTabBtns.length}个标签)`);
    } else {
        console.warn('⚠️ 未找到结果标签页按钮 .result-tab-btn');
    }
    
    // 图片网格事件委托
    if (elements.imagesGrid) {
        elements.imagesGrid.addEventListener('click', window.BatchImageProcessing.handleImageGridClick);
        console.log('✓ 图片网格事件委托已绑定');
    }

    // 回车键快捷处理
    if (elements.textInput) {
        elements.textInput.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                processText();
            }
        });
        console.log('✓ 文本输入快捷键已绑定');
    }

    // 初始化卡片折叠功能
    initializeCardToggle();
    console.log('✓ 卡片折叠功能已初始化');

    // 初始化图片导航系统
    if (window.ImageNavigation) {
        window.ImageNavigation.initializeImageNavigation();
        console.log('✓ 图片导航系统已初始化');
    }

    // 初始化侧边栏导航
    if (window.SidebarNavigation) {
        window.SidebarNavigation.initializeSidebarNavigation();
        console.log('✓ 侧边栏导航已初始化');
    }

    console.log('🎉 所有事件监听器初始化完成');
}

// 初始化应用
function initializeApp() {
    console.log('开始初始化应用...');
    
    // 初始化API客户端
    window.APIClient.initializeAPIClient();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化拖拽上传
    initializeDragAndDrop();
    
    console.log('✅ 应用初始化完成');
}

// 初始化卡片折叠功能
function initializeCardToggle() {
    document.addEventListener('click', function(e) {
        if (e.target.closest('.clickable-header')) {
            const header = e.target.closest('.clickable-header');
            const targetId = header.dataset.target;
            const content = document.getElementById(targetId);
            const toggle = header.querySelector('.card-toggle i');

            if (content && toggle) {
                if (content.classList.contains('collapsed')) {
                    content.classList.remove('collapsed');
                    toggle.classList.remove('fa-chevron-right');
                    toggle.classList.add('fa-chevron-down');
                    console.log(`✓ 展开卡片: ${targetId}`);
                } else {
                    content.classList.add('collapsed');
                    toggle.classList.remove('fa-chevron-down');
                    toggle.classList.add('fa-chevron-right');
                    console.log(`✓ 折叠卡片: ${targetId}`);
                }
            }
        }
    });
}

// 导出事件处理功能
window.EventHandlers = {
    switchTab,
    handleFileSelect,
    clearText,
    processText,
    loadExampleData,
    initializeDragAndDrop,
    initializeEventListeners,
    initializeApp
};

// DOM加载完成后初始化
window.DOMUtils.onDOMReady(initializeApp);

console.log('✓ 事件处理模块已加载');
