#!/usr/bin/env python3
"""
HIPAA医疗数据脱敏服务启动脚本

提供便捷的服务启动方式，支持不同的运行模式。
"""

import os
import sys
import argparse
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

import uvicorn
from config import config


def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    log_level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    level = log_level_map.get(log_level.upper(), logging.INFO)

    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HIPAA医疗数据脱敏服务")
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="服务监听地址 (默认: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=config.PORT,
        help=f"服务监听端口 (默认: {config.PORT})"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        default=config.DEBUG,
        help="启用自动重载 (开发模式)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=config.LOG_LEVEL,
        help=f"日志级别 (默认: {config.LOG_LEVEL})"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,  # 🔧 设为1个进程避免内存爆炸
        help="工作进程数量 (默认: 1，避免内存问题)"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info(f"启动 {config.APP_NAME} v{config.APP_VERSION}")
    logger.info("=" * 60)
    logger.info(f"监听地址: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")
    logger.info(f"日志级别: {args.log_level}")
    logger.info(f"自动重载: {args.reload}")
    logger.info(f"工作进程: {args.workers}")

    # 检查性能优化依赖
    uvloop_available = False
    httptools_available = False

    try:
        import uvloop
        uvloop_available = True
        logger.info(f"✓ uvloop {uvloop.__version__} 可用")
    except ImportError:
        logger.warning("⚠️ uvloop 未安装，建议安装以提升性能")

    try:
        import httptools
        httptools_available = True
        logger.info(f"✓ httptools {httptools.__version__} 可用")
    except ImportError:
        logger.warning("⚠️ httptools 未安装，建议安装以提升性能")

    logger.info("=" * 60)

    try:
        # 启动服务
        uvicorn_config = {
            "app": "main:app",
            "host": args.host,
            "port": args.port,
            "reload": args.reload,
            "log_level": args.log_level.lower(),
            "workers": args.workers if not args.reload else 1,  # reload模式下只能使用1个worker
            "access_log": True,
            "app_dir": current_dir
        }

        # 生产环境性能优化配置
        if not args.reload:
            optimization_enabled = []

            # 只有在可用时才启用uvloop
            if uvloop_available:
                uvicorn_config["loop"] = "uvloop"
                optimization_enabled.append("uvloop")

            # 只有在可用时才启用httptools
            if httptools_available:
                uvicorn_config["http"] = "httptools"
                optimization_enabled.append("httptools")

            # 其他生产环境配置
            uvicorn_config.update({
                "proxy_headers": True,      # 支持代理头
                "forwarded_allow_ips": "*", # 允许所有代理IP
                "timeout_keep_alive": 5,    # Keep-Alive超时时间
                "timeout_graceful_shutdown": 30,  # 优雅关闭超时时间
            })

            if optimization_enabled:
                logger.info(f"✓ 启用性能优化: {' + '.join(optimization_enabled)}")
            else:
                logger.info("⚠️ 未启用性能优化（缺少依赖包）")
        else:
            logger.info("⚠️ 开发模式: 未启用性能优化")

        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
