# Medical Data Anonymization System Dependencies
# 针对Python 3.11优化的依赖版本

# Core Presidio dependencies for PII detection
presidio-analyzer==2.2.354
presidio-anonymizer==2.2.354

# spaCy for Chinese NLP processing
spacy==3.7.2

# FastAPI and related dependencies for web service (兼容Python 3.11)
fastapi==0.109.2
pydantic==2.5.3
uvicorn[standard]==0.27.1
python-multipart==0.0.9

# 性能优化依赖
uvloop>=0.19.0          # 高性能异步事件循环
httptools>=0.6.0        # 高性能HTTP解析器

# Additional dependencies for enhanced functionality
typing-extensions>=4.9.0

# System monitoring dependencies
psutil>=5.9.0

# HTTP client for testing and utils
requests>=2.31.0
aiohttp>=3.9.0          # 异步HTTP客户端（用于utils工具）

# Additional dependencies that may be required for spaCy 3.7
thinc>=8.1.0,<8.3.0
catalogue>=2.0.6,<2.1.0
srsly>=2.4.3,<3.0.0
wasabi>=0.9.1,<1.2.0

# NumPy (兼容Python 3.11)
numpy>=1.24.0,<2.0.0

# Development and testing dependencies (optional)
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0

# Documentation dependencies (optional)
markdown>=3.5.0
