# HIPAA医疗数据脱敏服务 - 简化版

基于FastAPI的医疗文本敏感信息检测与脱敏RESTful API服务。

## 功能特点

- **简化架构**: 最小化文件数量，集中核心功能
- **高性能**: 基于FastAPI异步框架，支持并发处理
- **易于使用**: 直接JSON输入输出，无复杂数据模型
- **线程安全**: 内置请求计数器和统计功能
- **自动文档**: FastAPI自动生成API文档

## 文件结构

```
api/
├── main.py              # 主应用文件（包含所有路由和逻辑）
├── simple_config.py     # 基本配置文件
├── start_service.py     # 服务启动脚本
└── README.md           # 说明文档
```

## 快速开始

### 1. 安装依赖

```bash
# 安装FastAPI相关依赖
pip install fastapi uvicorn[standard] python-multipart psutil

# 或者安装完整依赖
pip install -r ../requirements.txt
```

### 2. 启动服务

```bash
# 方式1: 直接运行主文件
cd api
python main.py

# 方式2: 使用启动脚本（推荐）
python start_service.py

# 方式3: 使用uvicorn命令
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问服务

- **服务地址**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## API接口

### 1. 核心脱敏接口

**POST /deidentify**

对单个医疗文本进行敏感信息检测和脱敏处理。

**请求示例:**
```json
{
    "text": "患者张三，男，35岁，身份证号110101199001011234，联系电话13812345678"
}
```

**响应示例:**
```json
{
    "sensitive_words": [
        {
            "text": "张三",
            "entity_type": "PERSON",
            "start": 2,
            "end": 4,
            "confidence": 0.95
        },
        {
            "text": "110101199001011234",
            "entity_type": "CHINESE_ID",
            "start": 15,
            "end": 33,
            "confidence": 0.99
        }
    ],
    "deidentified_text": "患者**，男，35岁，身份证号**********，联系电话**********",
    "processing_time": 0.15,
    "total_entities": 3,
    "request_id": "req_20240101_123456_abc"
}
```

### 2. 批量脱敏接口

**POST /deidentify/batch**

对多个医疗文本进行批量脱敏处理。

**请求示例:**
```json
{
    "texts": [
        "患者张三，身份证110101199001011234",
        "患者李四，联系电话13912345678"
    ],
    "max_workers": 4
}
```

### 3. 系统统计接口

**GET /stats**

获取系统使用统计信息。

**响应示例:**
```json
{
    "total_requests": 1234,
    "uptime_seconds": 86400.5,
    "version": "1.0.0"
}
```

### 4. 健康检查接口

**GET /health**

检查服务健康状态。

**响应示例:**
```json
{
    "status": "healthy",
    "version": "1.0.0",
    "uptime_seconds": 3600.5,
    "checks": {
        "service": "healthy",
        "anonymizer": "healthy"
    }
}
```

## 脱敏策略

固定使用mask脱敏策略：

- **mask**: 替换为 `****` 或 `**********`

## 支持的敏感信息类型

- **个人身份**: 姓名、身份证号、手机号、微信号
- **医疗信息**: 病案号、医保卡号、医疗职位
- **联系方式**: 邮箱、网址、IP地址
- **地理位置**: 地名、GPS坐标、完整地址
- **其他**: 银行卡、护照、车辆信息等

## 配置选项

在 `simple_config.py` 中可以配置：

```python
# 服务器配置
HOST = "0.0.0.0"
PORT = 50505
DEBUG = False

# 请求限制
MAX_TEXT_LENGTH = 50000
```

**固定配置（不可修改）：**
- 使用所有识别器：`USE_ALL_RECOGNIZERS = True`
- 启用医学术语过滤器：`ENABLE_MEDICAL_FILTER = True`
- 脱敏策略：`mask`
- 请求计数器最大值：`1000000`（达到后自动归零）
