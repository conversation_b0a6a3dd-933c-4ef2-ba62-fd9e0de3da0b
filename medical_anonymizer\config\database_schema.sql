-- 医疗脱敏系统配置数据库表结构
-- 支持多项目、JSON配置存储和配置继承

CREATE TABLE `hipaa_deidentify_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码，master为默认模板项目',
  `parent_project` varchar(50) DEFAULT 'master' COMMENT '父项目编码，用于配置继承',
  `config_json` json NOT NULL COMMENT '完整的JSON配置内容',
  `config_type` varchar(20) DEFAULT 'full' COMMENT '配置类型：full-完整配置，diff-差异配置',
  `version` int(11) DEFAULT 1 COMMENT '配置版本号',
  `description` varchar(1000) DEFAULT NULL COMMENT '配置描述',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `updated_by` varchar(100) DEFAULT 'system' COMMENT '修改人',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_version` (`project_code`, `version`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_parent_project` (`parent_project`),
  KEY `idx_updated_time` (`updated_time`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医疗脱敏系统项目配置表';

-- 配置变更历史表（独立审计表）
CREATE TABLE `hipaa_deidentify_config_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置表ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：CREATE, UPDATE, DELETE, ROLLBACK',
  `describe` json DEFAULT NULL COMMENT '修改描述，不需要存储完整的json',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `created_by` varchar(100) DEFAULT 'system' COMMENT '变更人',

  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_time` (`created_time`),
  FOREIGN KEY (`config_id`) REFERENCES `hipaa_deidentify_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医疗脱敏系统配置变更历史表';


