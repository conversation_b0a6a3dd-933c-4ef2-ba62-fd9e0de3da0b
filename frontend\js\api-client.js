/**
 * API客户端模块
 * 处理所有与后端API的通信，包括OCR、脱敏、健康检查等
 */


function getApiBaseUrl() {
    // 优先级：
    // 1. 固定配置的IP地址
    // 2. 当前页面的主机地址
    // 3. 本地地址

    // const fixedIP = '***************'; // 您指定的固定IP
    const fixedIP = '127.0.0.1';
    const currentHost = window.location.hostname;

    // 如果当前访问的是固定IP，直接使用
    if (currentHost === fixedIP) {
        return `http://${fixedIP}:50505`;
    }

    // 如果当前是localhost或127.0.0.1，尝试固定IP
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        return `http://${fixedIP}:50505`;
    }

    // 否则使用当前主机地址
    return `http://${currentHost}:50505`;
}


const CONFIG = {
    DEIDENTIFY_API: `${getApiBaseUrl()}/deidentify`,
    OCR_API: 'http://*************:8011/ocr/single',
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_FORMATS: ['image/jpeg', 'image/png', 'image/gif']
};

console.log('当前API配置:', CONFIG.DEIDENTIFY_API);

// 文件转base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            // 移除data:image/...;base64,前缀
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}

// 调用OCR API
async function callOCRAPI(base64Image) {
    const response = await fetch(CONFIG.OCR_API, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            image: base64Image
        })
    });

    if (!response.ok) {
        throw new Error(`OCR服务请求失败: ${response.status}`);
    }

    const data = await response.json();
    
    // 根据API文档，提取路径为 result.markdown_result
    if (data.result && data.result.markdown_result) {
        return data.result.markdown_result;
    } else {
        throw new Error('OCR服务返回数据格式错误');
    }
}

// 批量处理专用的OCR函数
async function performOCR(file) {
    try {
        console.log(`开始OCR处理: ${file.name}`);
        
        // 转换文件为base64
        const base64 = await fileToBase64(file);
        
        // 调用OCR API
        const text = await callOCRAPI(base64);
        
        console.log(`OCR处理完成: ${file.name}, 提取文本长度: ${text.length}`);
        
        return {
            success: true,
            text: text
        };
    } catch (error) {
        console.error(`OCR处理失败: ${file.name}`, error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 测试API连接
async function testApiConnection(apiUrl) {
    try {
        const healthUrl = apiUrl.replace('/deidentify', '/health');
        const response = await fetch(healthUrl, {
            method: 'GET',
            timeout: 5000
        });
        return response.ok;
    } catch (error) {
        return false;
    }
}


// 自动检测最佳API地址
async function detectBestApiUrl() {
    const candidates = [
        'http://127.0.0.1:5050',        // 本地地址
        'http://***************:50505',  // 固定IP
        // 'http://localhost:50505',        // 本地主机
        `http://${window.location.hostname}:50505`  // 当前主机
    ];

    console.log('正在检测最佳API地址...');

    for (const baseUrl of candidates) {
        console.log(`测试: ${baseUrl}`);
        if (await testApiConnection(baseUrl)) {
            const apiUrl = `${baseUrl}/deidentify`;
            console.log(`✓ 找到可用API: ${apiUrl}`);
            CONFIG.DEIDENTIFY_API = apiUrl;

            // 更新页面显示
            updateApiStatus(apiUrl, true);
            return apiUrl;
        }
    }

    console.log('❌ 未找到可用的API服务');
    updateApiStatus(CONFIG.DEIDENTIFY_API, false);
    return null;
}

// 更新API状态显示
function updateApiStatus(apiUrl, isConnected) {
    // 在页面上显示API状态
    let statusElement = document.getElementById('api-status');
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'api-status';
        statusElement.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 13px;
            z-index: 1000;
            max-width: 350px;
            word-break: break-all;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        `;
        document.body.appendChild(statusElement);

        // 点击隐藏状态元素
        statusElement.addEventListener('click', () => {
            statusElement.style.opacity = '0';
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 300);
        });
    }

    if (isConnected) {
        statusElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        statusElement.style.color = 'white';
        statusElement.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-check-circle"></i>
                <div>
                    <div style="font-weight: 600;">API连接成功</div>
                    <div style="font-size: 11px; opacity: 0.9;">${apiUrl}</div>
                </div>
            </div>
        `;
    } else {
        statusElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
        statusElement.style.color = 'white';
        statusElement.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <div style="font-weight: 600;">API连接失败</div>
                    <div style="font-size: 11px; opacity: 0.9;">${apiUrl}</div>
                </div>
            </div>
        `;
    }

    // 自动隐藏成功状态
    if (isConnected) {
        setTimeout(() => {
            if (statusElement && statusElement.parentNode) {
                statusElement.style.opacity = '0';
                setTimeout(() => {
                    if (statusElement.parentNode) {
                        statusElement.parentNode.removeChild(statusElement);
                    }
                }, 300);
            }
        }, 3000);
    }
}


// 调用脱敏API
async function callDeidentifyAPI(text) {
    // 如果当前API不可用，尝试重新检测
    if (!(await testApiConnection(CONFIG.DEIDENTIFY_API.replace('/deidentify', '')))) {
        console.log('当前API不可用，尝试重新检测...');
        const newApiUrl = await detectBestApiUrl();
        if (!newApiUrl) {
            throw new Error('无法连接到脱敏API服务，请检查服务是否正在运行');
        }
    }

    const response = await fetch(CONFIG.DEIDENTIFY_API, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: text
        })
    });

    if (!response.ok) {
        throw new Error(`脱敏API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data;
}

// 批量处理专用的脱敏函数
async function performDeidentification(text) {
    try {
        console.log(`开始脱敏处理，文本长度: ${text.length}`);
        
        // 调用脱敏API
        const results = await callDeidentifyAPI(text);
        
        console.log(`脱敏处理完成，检测到 ${results.sensitive_words.length} 个敏感信息`);
        
        return {
            success: true,
            data: results
        };
    } catch (error) {
        console.error('脱敏处理失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 健康检查
async function healthCheck() {
    try {
        const healthUrl = CONFIG.DEIDENTIFY_API.replace('/deidentify', '/health');
        const response = await fetch(healthUrl);
        return {
            success: response.ok,
            status: response.status,
            url: healthUrl
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            url: CONFIG.DEIDENTIFY_API.replace('/deidentify', '/health')
        };
    }
}

// 初始化API客户端
async function initializeAPIClient() {
    console.log('初始化API客户端...');
    
    // 检测最佳API URL
    await detectBestApiUrl();
    
    // 执行健康检查
    const health = await healthCheck();
    if (health.success) {
        console.log('✓ API服务连接正常');
    } else {
        console.warn('⚠️ API服务连接异常:', health.error);
    }
    
    return health.success;
}

// 导出所有API功能
window.APIClient = {
    CONFIG,
    fileToBase64,
    callOCRAPI,
    performOCR,
    callDeidentifyAPI,
    performDeidentification,
    testApiConnection,
    detectBestApiUrl,
    healthCheck,
    initializeAPIClient
};

console.log('✓ API客户端模块已加载');
