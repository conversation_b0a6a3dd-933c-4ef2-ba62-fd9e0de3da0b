<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIPAA医疗数据脱敏系统 - 演示平台</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <i class="fas fa-shield-alt"></i>
                    <h1>HIPAA医疗数据脱敏系统</h1>
<!--                    <p class="subtitle">智能识别与保护医疗敏感信息</p>-->
                </div>
                <div class="header-right">
                    <button id="refresh-api-btn" class="btn btn-small btn-outline" title="重新检测API连接">
                        <i class="fas fa-sync-alt"></i>
                        检测API
                    </button>
                </div>
            </div>
        </header>

        <!-- 输入区域 -->
        <section class="input-section">
            <div class="input-tabs">
                <button class="tab-btn active" data-tab="text">
                    <i class="fas fa-keyboard"></i>
                    文本输入
                </button>
                <button class="tab-btn" data-tab="image">
                    <i class="fas fa-image"></i>
                    图片上传
                </button>
            </div>

            <!-- 文本输入标签页 -->
            <div class="tab-content active" id="text-tab">
                <div class="input-group">
                    <label for="text-input">请输入待脱敏的医疗文本：</label>
                    <textarea 
                        id="text-input" 
                        placeholder="例如：患者张三，男，35岁，身份证号110101199001011234，联系电话13812345678，住址北京市朝阳区..."
                        rows="8"
                    ></textarea>
                    <div class="input-actions">
                        <button id="load-example-btn" class="btn btn-outline">
                            <i class="fas fa-file-alt"></i>
                            加载示例
                        </button>
                        <button id="clear-text-btn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i>
                            清空
                        </button>
                        <button id="process-text-btn" class="btn btn-primary">
                            <i class="fas fa-shield-alt"></i>
                            开始脱敏
                        </button>
                    </div>
                </div>
            </div>

            <!-- 图片上传标签页 -->
            <div class="tab-content" id="image-tab">
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>拖拽图片到此处或点击上传</h3>
                        <p>支持 JPG、PNG、GIF 格式，最大 10MB，支持批量上传</p>
                        <input type="file" id="file-input" accept="image/*" multiple hidden>
                        <button class="btn btn-outline" id="select-files-btn">
                            选择文件
                        </button>
                    </div>
                </div>

                <!-- 批量图片预览和处理区域 -->
                <div class="batch-images-container" id="batch-images-container" style="display: none;">
                    <div class="batch-header">
                        <h3>
                            <i class="fas fa-images"></i>
                            已选择图片 (<span id="image-count">0</span>)
                        </h3>
                        <div class="batch-actions">
                            <button id="clear-all-images-btn" class="btn btn-secondary">
                                <i class="fas fa-trash"></i>
                                清空所有
                            </button>
                            <button id="process-all-images-btn" class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                批量处理
                            </button>
                            <button id="reprocess-all-images-btn" class="btn btn-warning" style="display: none;">
                                <i class="fas fa-redo"></i>
                                重新批量推理
                            </button>
                        </div>
                    </div>
                    <div class="images-grid" id="images-grid">
                        <!-- 动态生成的图片项 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 规则配置区域 -->
        <section class="rules-config-section">
            <div class="config-header">
                <h2>
                    <i class="fas fa-cogs"></i>
                    敏感信息检测规则配置
                </h2>
                <div class="development-notice">
                    <i class="fas fa-info-circle"></i>
                    此功能正在开发中
                </div>
            </div>

            <div class="config-content" id="rules-config">
                <!-- 规则配置将通过JavaScript动态生成 -->
                <div class="loading-rules">正在加载规则配置...</div>
            </div>
        </section>

        <!-- 结果展示区域 -->
        <section class="results-section" id="results-section" style="display: none;">

            <!-- 原文高亮显示 -->
            <div class="result-card" id="highlighted-section">
                <div class="card-header">
                    <div class="header-left">
                        <h3><i class="fas fa-search"></i> 敏感信息标注</h3>
                        <div class="result-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                处理时间: <span id="processing-time">-</span>ms
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                敏感信息: <span id="entity-count">0</span>项
                            </span>
                        </div>
                    </div>
                    <button class="btn btn-small" id="toggle-highlight">
                        <i class="fas fa-eye"></i>
                        切换高亮
                    </button>
                </div>
                <div class="card-content">
                    <div class="text-display" id="highlighted-text"></div>
                </div>
            </div>

            <!-- 脱敏结果 -->
            <div class="result-card collapsible-card" id="deidentified-section">
                <div class="card-header clickable-header" data-target="deidentified-content">
                    <div class="header-left">
                        <h3><i class="fas fa-shield-alt"></i> 脱敏结果</h3>
                    </div>
                    <button class="card-toggle" aria-label="展开/折叠">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="card-content collapsed" id="deidentified-content">
                    <div class="text-display" id="deidentified-text"></div>
                </div>
            </div>

            <!-- 敏感信息详情 -->
            <div class="result-card" id="details-section">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> 敏感信息详情</h3>
                    <div class="header-actions">
                        <button class="btn btn-small" id="toggle-all-groups">
                            <i class="fas fa-expand-alt"></i>
                            全部展开
                        </button>
                        <button class="btn btn-small" id="export-results">
                            <i class="fas fa-download"></i>
                            导出结果
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <!-- 结果分类标签页 -->
                    <div class="result-tabs">
                        <button class="result-tab-btn active" data-tab="detected">
                            <i class="fas fa-exclamation-triangle"></i>
                            检测到的敏感信息 (<span id="detected-count">0</span>)
                        </button>
                        <button class="result-tab-btn" data-tab="filtered">
                            <i class="fas fa-filter"></i>
                            被过滤的词汇 (<span id="filtered-count">0</span>)
                        </button>
                    </div>

                    <!-- 检测到的敏感信息 -->
                    <div class="result-tab-content active" id="detected-tab">
                        <div class="sensitive-info-list" id="sensitive-info-list"></div>
                    </div>

                    <!-- 被过滤的词汇 -->
                    <div class="result-tab-content" id="filtered-tab">
                        <div class="filtered-info-list" id="filtered-info-list">
                            <div class="empty-state">
                                <i class="fas fa-filter"></i>
                                <p>暂无被过滤的词汇</p>
                                <small>过滤器会自动识别并过滤掉误报的医学术语</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 加载状态 -->
        <div class="loading" id="loading" style="display: none;">
            <div class="loading-spinner"></div>
            <p id="loading-text">正在处理中...</p>
        </div>

        <!-- 右侧悬浮导航栏 -->
        <nav class="floating-nav" id="floating-nav" style="display: none;">
            <div class="nav-header">
                <div class="nav-title">
                    <i class="fas fa-bars"></i>
                    <span>导航</span>
                </div>
                <button class="nav-toggle" id="nav-toggle" aria-label="收起/展开导航">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="nav-content">
                <div class="nav-menu">
                    <a href="#highlighted-section" class="nav-item" data-section="highlighted">
                        <i class="fas fa-highlighter"></i>
                        <span class="nav-text">标注</span>
                    </a>
                    <a href="#deidentified-section" class="nav-item" data-section="deidentified">
                        <i class="fas fa-shield-alt"></i>
                        <span class="nav-text">脱敏</span>
                    </a>
                    <a href="#details-section" class="nav-item" data-section="details">
                        <i class="fas fa-list-ul"></i>
                        <span class="nav-text">详情</span>
                    </a>
                </div>
                <div class="nav-footer">
                    <button class="nav-back-top" id="nav-back-top" title="返回顶部">
                        <i class="fas fa-chevron-up"></i>
                        <span class="nav-text">顶部</span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 错误提示 -->
        <div class="error-message" id="error-message" style="display: none;">
            <i class="fas fa-exclamation-circle"></i>
            <span id="error-text"></span>
            <button class="close-btn" onclick="hideError()">×</button>
        </div>
    </div>

    <!-- 模块化JavaScript文件 - 按依赖顺序加载 -->

    <!-- 1. 基础工具模块 -->
    <script src="js/dom-utils.js"></script>
    <script src="js/data-formatters.js"></script>
    <script src="js/detector-mapping.js"></script>

    <!-- 2. API通信模块 -->
    <script src="js/api-client.js"></script>

    <!-- 3. 模板系统模块 -->
    <script src="js/template-loader.js"></script>
    <script src="js/rules-config-manager.js"></script>

    <!-- 4. 功能模块 -->
    <script src="js/batch-image-processing.js"></script>
    <script src="js/image-navigation.js"></script>
    <script src="js/sidebar-navigation.js"></script>
    <script src="js/result-display.js"></script>

    <!-- 5. 事件处理模块 -->
    <script src="js/event-handlers.js"></script>

    <!-- 6. 主应用入口 -->
    <script src="js/main.js"></script>

    <!-- 7. 其他工具脚本 -->
    <script src="check_spacing.js"></script>
</body>
</html>
