"""
医疗数据脱敏系统过滤器模块
重构后的两层过滤器架构
"""

# 新的两层过滤器架构
from .two_layer_filter import TwoLayerFilter, TwoLayerFilterConfig, create_two_layer_filter, create_medical_filter
from .exact_match_filter import Exact<PERSON>atchFilter, create_exact_match_filter
from .specialized_rules import get_entity_rules, ENTITY_RULES_REGISTRY


__all__ = [
    # 新架构主要接口
    'TwoLayerFilter',
    'TwoLayerFilterConfig',
    'create_two_layer_filter',
    'create_medical_filter',

    # 分层接口
    'ExactMatchFilter',
    'create_exact_match_filter',
    'get_entity_rules',
    'ENTITY_RULES_REGISTRY',


]