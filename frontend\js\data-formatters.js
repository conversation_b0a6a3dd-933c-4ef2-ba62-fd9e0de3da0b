/**
 * 数据格式化模块
 * 处理数据转换、格式化和实体类型映射
 */

// 实体类型中文映射（完整版 - 支持医疗脱敏系统所有敏感信息类型）
const ENTITY_TYPE_MAPPING = {
    // === 规则识别器类型（使用识别器类名） ===
    'ChineseIDRecognizer': '身份证号',
    'LicensePlateRecognizer': '车牌号',
    'EthnicityRecognizer': '民族',
    'EducationLevelRecognizer': '学历学位',
    'PrivacyInfoRecognizer': '隐私信息',
    'CompleteAddressRecognizer': '完整地址',
    'URLRecognizer': '网址链接',
    'StructuredFieldRecognizer': '结构化字段',
    'SponsorRecognizer': '申办方名称',

    // === 上下文识别器类型（使用识别器类名） ===
    'MobilePhoneRecognizer': '手机号',
    'LandlinePhoneRecognizer': '固定电话',
    'MedicalInsuranceRecognizer': '医保卡号',
    'BankCardRecognizer': '银行卡号',
    'WeChatRecognizer': '微信号',
    'CertificateRecognizer': '各类证件',
    'QQNumberRecognizer': 'QQ号',
    'OrganizationRecognizer': '医疗机构',
    'GPSCoordinateRecognizer': 'GPS坐标',
    'MedicalNumberRecognizer': '医疗编号',
    'GenderRecognizer': '性别信息',
    'AgeRecognizer': '年龄信息',

    // === spaCy和Presidio实体类型 ===
    'PERSON': '人名',
    'LOCATION': '位置信息',
    'NRP': '民族/国籍',
    'DATE_TIME': '日期时间',
    'EMAIL_ADDRESS': '邮箱地址',
    'IP_ADDRESS': 'IP地址',
    'CREDIT_CARD': '信用卡号',
    'IBAN_CODE': '国际银行账号',

    // === 向后兼容的旧实体类型映射 ===
    'CHINESE_ID': '身份证号',
    'LICENSE_PLATE': '车牌号',
    'ETHNICITY': '民族',
    'EDUCATION_LEVEL': '学历学位',
    'PRIVACY_INFO': '隐私信息',
    'COMPLETE_ADDRESS': '完整地址',
    'URL': '网址链接',
    'STRUCTURED_FIELD': '结构化字段',
    'SPONSOR': '申办方名称',
    'MOBILE_PHONE': '手机号',
    'LANDLINE_PHONE': '固定电话',
    'MEDICAL_INSURANCE': '医保卡号',
    'BANK_CARD': '银行卡号',
    'WECHAT_ID': '微信号',
    'CERTIFICATE': '各类证件',
    'QQ_NUMBER': 'QQ号',
    'MEDICAL_ORGANIZATION': '医疗机构',
    'GPS_COORDINATE': 'GPS坐标',
    'MEDICAL_NUMBER': '医疗编号',
    'GENDER': '性别信息',
    'AGE': '年龄信息',

    // === 其他通用类型 ===
    'PII': '个人身份信息',
    'FINANCIAL_DATA': '金融数据',
    'HEALTH_DATA': '健康数据',

    // === 金融信息类 ===
    'BANK_CARD': '银行卡号',
    'CREDIT_CARD': '信用卡号',
    'BANK_ACCOUNT': '银行账号',
    'ALIPAY': '支付宝账号',
    'FINANCIAL_ACCOUNT': '金融账户',
    'FINANCIAL_DATA': '金融数据',

    // === 医疗信息类 ===
    'MEDICAL_RECORD_ID': '病案号',
    'MEDICAL_RECORD_NUMBER': '病历号',
    'MEDICAL_LICENSE': '医师执业证号',
    'MEDICAL_INSURANCE': '医保卡号',
    'HEALTH_CARD': '医保卡号',
    'PRESCRIPTION_NUMBER': '处方号',
    'LAB_RESULT': '检验结果',
    'DIAGNOSIS': '诊断信息',
    'TREATMENT': '治疗信息',
    'MEDICATION': '药物信息',
    'MEDICAL_POSITION': '医疗职位',
    'MEDICAL_ORGANIZATION': '医疗机构',
    'MEDICAL_NUMBER': '医疗编号',
    'HEALTH_DATA': '健康数据',
    
    // === 个人特征类 ===
    'ETHNICITY': '民族',
    'FAMILY_RELATIONSHIP': '家庭关系',
    'EDUCATION_LEVEL': '学历学位',
    'NRP': '民族/国籍',
    'BIOMETRIC_FEATURE': '生物特征',
    'FINGERPRINT': '指纹',
    'DNA': 'DNA信息',
    'BLOOD_TYPE': '血型',
    'GENETIC_INFO': '基因信息',
    'PRIVACY_INFO': '隐私信息',

    // === 设备信息类 ===
    'DEVICE_SERIAL': '设备序列号',
    'MAC_ADDRESS': 'MAC地址',
    'IP_ADDRESS': 'IP地址',
    'IMEI': 'IMEI号',
    'LICENSE_PLATE': '车牌号',
    'VEHICLE_INFO': '车辆信息',

    // === 组织机构类 ===
    'ORGANIZATION': '组织机构',
    'ORG': '机构名',
    'COMPANY': '公司名称',
    'HOSPITAL': '医院名称',
    'SCHOOL': '学校名称',
    'GOVERNMENT_AGENCY': '政府机构',
    'SPONSOR': '申办方名称',

    // === 地理位置类 ===
    'LOCATION': '位置信息',
    'GPE': '地名',
    'COMPLETE_ADDRESS': '完整地址',
    'GPS_COORDINATE': 'GPS坐标',

    // === 技术信息类 ===
    'URL': '网址链接',
    'USERNAME': '用户名',
    'PASSWORD': '密码',
    'TOKEN': '令牌',
    'API_KEY': 'API密钥',
    'STRUCTURED_FIELD': '结构化字段',

    // === 时间信息类 ===
    'DATE_OF_BIRTH': '出生日期',
    'DATE_TIME': '日期时间',
    'AGE': '年龄',

    // === 其他类型 ===
    'CRYPTO': '加密货币',
    'IBAN_CODE': '国际银行账号'
};

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 格式化处理时间（毫秒）
function formatProcessingTime(seconds) {
    if (seconds < 1) {
        return Math.round(seconds * 1000) + 'ms';
    } else {
        return seconds.toFixed(2) + 's';
    }
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'error': '处理失败'
    };
    return statusMap[status] || '未知';
}

// 获取过滤器类型显示名称
function getFilterTypeDisplayName(filterType) {
    const typeMap = {
        'exact_match': '精确匹配过滤',
        'specialized_rules': '特殊实体规则过滤',
        'pattern_match': '模式匹配过滤',
        'context_filter': '上下文过滤',
        'length_filter': '长度过滤',
        'unknown': '未知过滤器'
    };
    
    return typeMap[filterType] || filterType;
}

// 生成唯一ID
function generateUniqueId(prefix = 'id') {
    return prefix + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 验证文件类型和大小
function validateFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const supportedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    
    if (!supportedTypes.includes(file.type)) {
        return {
            valid: false,
            error: `文件 ${file.name} 格式不支持，请选择 JPG、PNG 或 GIF 格式的图片`
        };
    }
    
    if (file.size > maxSize) {
        return {
            valid: false,
            error: `文件 ${file.name} 过大，请选择小于 10MB 的图片`
        };
    }
    
    return { valid: true };
}

// 格式化敏感信息实体
function formatSensitiveEntity(entity) {
    return {
        text: entity.text,
        type: ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type,
        originalType: entity.entity_type,
        start: entity.start,
        end: entity.end,
        confidence: entity.confidence || entity.score || 0
    };
}

// 格式化过滤实体
function formatFilteredEntity(entity) {
    return {
        text: entity.text,
        type: ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type,
        originalType: entity.entity_type,
        filterType: getFilterTypeDisplayName(entity.filter_type),
        originalFilterType: entity.filter_type,
        context: entity.context || ''
    };
}

// 按过滤器类型分组
function groupFilteredEntitiesByType(filteredEntities) {
    const groups = {};
    
    filteredEntities.forEach(entity => {
        const filterType = entity.filter_type || 'unknown';
        if (!groups[filterType]) {
            groups[filterType] = [];
        }
        groups[filterType].push(entity);
    });
    
    return groups;
}

// 统一结果数据格式
function normalizeResultData(result) {
    // 确保数据格式统一
    const normalized = {
        sensitive_words: result.sensitive_words || result.entities || [],
        filtered_entities: result.filtered_entities || [],
        deidentified_text: result.deidentified_text || result.anonymized_text || '',
        processing_time: result.processing_time || 0,
        total_entities: result.total_entities || (result.sensitive_words || result.entities || []).length,
        timestamp: result.timestamp || new Date().toISOString()
    };
    
    // 如果有原文，也保留
    if (result.originalText) {
        normalized.originalText = result.originalText;
    }
    
    // 如果有图片ID，也保留
    if (result.imageId) {
        normalized.imageId = result.imageId;
    }
    
    return normalized;
}

// 创建结果摘要
function createResultSummary(result) {
    const normalized = normalizeResultData(result);
    
    return {
        entityCount: normalized.sensitive_words.length,
        filteredCount: normalized.filtered_entities.length,
        processingTime: formatProcessingTime(normalized.processing_time),
        hasText: Boolean(normalized.deidentified_text),
        timestamp: formatTimestamp(normalized.timestamp)
    };
}

// 高亮文本中的敏感信息
function highlightSensitiveText(text, entities) {
    if (!text || !entities || entities.length === 0) {
        return text;
    }
    
    // 按位置排序，从后往前处理避免位置偏移
    const sortedEntities = [...entities].sort((a, b) => b.start - a.start);
    
    let highlightedText = text;
    
    sortedEntities.forEach(entity => {
        const before = highlightedText.substring(0, entity.start);
        const highlighted = highlightedText.substring(entity.start, entity.end);
        const after = highlightedText.substring(entity.end);
        
        const entityType = ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type;
        highlightedText = before +
            `<span class="sensitive-highlight" title="${entityType}">${highlighted}</span>` +
            after;
    });
    
    return highlightedText;
}

// 导出所有格式化功能
window.DataFormatters = {
    ENTITY_TYPE_MAPPING,
    formatFileSize,
    formatTimestamp,
    formatProcessingTime,
    getStatusText,
    getFilterTypeDisplayName,
    generateUniqueId,
    validateFile,
    formatSensitiveEntity,
    formatFilteredEntity,
    groupFilteredEntitiesByType,
    normalizeResultData,
    createResultSummary,
    highlightSensitiveText
};

console.log('✓ 数据格式化模块已加载');
