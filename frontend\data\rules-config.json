{"categories": {"identity": {"id": "identity", "title": "核心身份识别器组", "icon": "fa-id-card", "enabled": true, "collapsed": false, "rules": [{"id": "chinese_id", "name": "身份证号识别器", "type": "ChineseIDRecognizer", "description": "基于正则表达式识别18位身份证号码", "enabled": true}, {"id": "passport", "name": "护照号识别器", "type": "PassportRecognizer", "description": "识别中国护照号码格式", "enabled": true}, {"id": "person_name", "name": "人名识别器", "type": "PersonRecognizer", "description": "基于NLP模型识别人名", "enabled": true}, {"id": "mobile_phone", "name": "手机号识别器", "type": "MobilePhoneRecognizer", "description": "识别11位手机号码", "enabled": true}, {"id": "email", "name": "邮箱识别器", "type": "EmailRecognizer", "description": "识别电子邮箱地址", "enabled": true}, {"id": "landline_phone", "name": "固话识别器", "type": "LandlinePhoneRecognizer", "description": "识别固定电话号码", "enabled": true}, {"id": "biometric", "name": "生物特征识别器", "type": "BiometricRecognizer", "description": "识别生物特征相关信息", "enabled": true, "keywords": ["指纹", "声纹", "面部特征", "虹膜", "掌纹", "DNA", "基因", "生物特征", "人脸识别", "指纹识别", "声纹识别", "虹膜识别", "面部识别"]}]}, "medical": {"id": "medical", "title": "医疗专业识别器组", "icon": "fa-heartbeat", "enabled": true, "collapsed": true, "rules": [{"id": "medical_record", "name": "病历号识别器", "type": "MedicalRecordRecognizer", "description": "识别医院病历号码", "enabled": true}, {"id": "medical_license", "name": "医师执业证识别器", "type": "MedicalLicenseRecognizer", "description": "识别医师执业证号", "enabled": true}, {"id": "health_card", "name": "医保卡识别器", "type": "HealthCardRecognizer", "description": "识别医保卡号码", "enabled": true}, {"id": "prescription", "name": "处方号识别器", "type": "PrescriptionRecognizer", "description": "识别处方单号码", "enabled": true}, {"id": "medical_terms", "name": "医学术语识别器", "type": "MedicalTermsRecognizer", "description": "识别医学专业术语", "enabled": true, "keywords": ["血压", "心率", "体温", "血糖", "胆固醇", "血红蛋白", "白细胞", "血小板", "肝功能", "肾功能", "心电图", "B超", "CT", "MRI", "X光", "病理", "化验", "检查", "诊断", "治疗", "手术", "药物", "处方", "病史"]}]}, "privacy": {"id": "privacy", "title": "隐私敏感信息识别器组", "icon": "fa-shield-alt", "enabled": true, "collapsed": true, "rules": [{"id": "bank_card", "name": "银行卡识别器", "type": "BankCardRecognizer", "description": "识别银行卡号码", "enabled": true}, {"id": "address", "name": "地址识别器", "type": "AddressRecognizer", "description": "识别详细地址信息", "enabled": true}, {"id": "organization", "name": "组织机构识别器", "type": "OrganizationRecognizer", "description": "识别公司、医院等机构名称", "enabled": true}, {"id": "date_time", "name": "日期时间识别器", "type": "DateTimeRecognizer", "description": "识别敏感的日期时间信息", "enabled": true}, {"id": "financial_account", "name": "金融账户识别器", "type": "FinancialAccountRecognizer", "description": "识别各类金融账户信息", "enabled": true, "keywords": ["支付宝", "微信支付", "银行账户", "信用卡", "储蓄卡", "借记卡", "账户余额", "交易记录", "转账", "汇款", "理财", "基金", "股票", "保险", "贷款"]}, {"id": "device_info", "name": "设备信息识别器", "type": "DeviceInfoRecognizer", "description": "识别设备相关敏感信息", "enabled": true, "keywords": ["IMEI", "MAC地址", "IP地址", "设备序列号", "UUID", "设备ID", "硬件信息", "系统版本", "浏览器信息", "位置信息", "GPS坐标"]}]}, "nlp": {"id": "nlp", "title": "NLP智能识别器组", "icon": "fa-brain", "enabled": true, "collapsed": true, "rules": [{"id": "context_person", "name": "上下文人名识别器", "type": "ContextPersonRecognizer", "description": "基于上下文智能识别人名", "enabled": true}, {"id": "context_location", "name": "上下文地点识别器", "type": "ContextLocationRecognizer", "description": "基于上下文智能识别地点", "enabled": true}, {"id": "semantic_analysis", "name": "语义分析识别器", "type": "SemanticAnalysisRecognizer", "description": "基于语义分析识别敏感信息", "enabled": true}, {"id": "pattern_learning", "name": "模式学习识别器", "type": "PatternLearningRecognizer", "description": "基于机器学习的模式识别", "enabled": true, "keywords": ["机器学习", "深度学习", "神经网络", "自然语言处理", "文本分析", "语义理解", "实体识别", "关系抽取", "情感分析", "意图识别"]}]}}}