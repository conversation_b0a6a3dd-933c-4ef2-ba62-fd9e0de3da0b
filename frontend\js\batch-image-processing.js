/**
 * 批量图片处理模块
 * 处理批量图片上传、处理状态管理和界面更新
 */

// 批量图片管理
let batchImages = [];

// 并发处理配置
const CONCURRENT_CONFIG = {
    maxConcurrency: 4,        // 最大并发数
    minDelayMs: 100,          // 最小请求间隔（毫秒）
    maxRetries: 2,            // 最大重试次数
    retryDelayMs: 1000        // 重试延迟（毫秒）
};

// 批量图片处理模块 - 避免变量重复声明，直接使用模块方法

/**
 * 管理批量处理按钮状态
 * @param {string} state - 按钮状态: 'idle', 'processing', 'progress'
 * @param {Object} options - 选项参数
 */
function updateBatchProcessingButtonState(state, options = {}) {
    const btn = document.getElementById('process-all-images-btn');
    const reprocessBtn = document.getElementById('reprocess-all-images-btn');

    if (!btn) {
        console.warn('⚠️ 批量处理按钮未找到');
        return;
    }

    switch (state) {
        case 'idle':
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-play"></i> 批量处理';
            // 检查是否有已处理的图片，如果有则保持重新处理按钮显示
            if (reprocessBtn) {
                const batchImages = window.BatchImageProcessing?.getBatchImages?.() || [];
                const hasProcessedImages = batchImages.some(img =>
                    img.status === 'completed' || img.status === 'error'
                );

                if (hasProcessedImages) {
                    reprocessBtn.style.display = 'inline-block';
                } else {
                    reprocessBtn.style.display = 'none';
                }
            }
            console.log('✓ 批量处理按钮状态: 空闲');
            break;

        case 'processing':
            btn.disabled = true;
            const concurrencyText = options.concurrency;
            if (concurrencyText === '正在完成') {
                btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在完成...`;
                console.log('✓ 批量处理按钮状态: 正在完成');
            } else {
                btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${concurrencyText || 4}并发处理中...`;
                console.log('✓ 批量处理按钮状态: 处理中');
            }
            // 隐藏重新处理按钮
            if (reprocessBtn) {
                reprocessBtn.style.display = 'none';
            }
            break;

        case 'progress':
            btn.disabled = true;
            const { current = 0, total = 0, percentage = 0 } = options;
            btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 处理中... ${current}/${total} (${percentage}%)`;
            // 隐藏重新处理按钮
            if (reprocessBtn) {
                reprocessBtn.style.display = 'none';
            }
            break;

        case 'completed':
            btn.disabled = true;
            const { successCount = 0, failureCount = 0, totalCount = 0 } = options;
            btn.innerHTML = `<i class="fas fa-check-circle"></i> 处理完成 ${successCount}/${totalCount} (成功: ${successCount}, 失败: ${failureCount})`;
            // 显示重新处理按钮
            if (reprocessBtn) {
                reprocessBtn.style.display = 'inline-block';
            }
            console.log('✓ 批量处理按钮状态: 处理完成');
            break;

        default:
            console.warn(`⚠️ 未知的按钮状态: ${state}`);
    }
}

// 批量文件上传处理
function handleBatchFileUpload(files) {
    const validFiles = files.filter(file => {
        const validation = window.DataFormatters.validateFile(file);
        if (!validation.valid) {
            console.error(validation.error);
            return false;
        }
        return true;
    });
    
    if (validFiles.length === 0) {
        window.UIUtils.showError('没有有效的图片文件');
        return;
    }
    
    console.log(`添加 ${validFiles.length} 个有效文件到批量处理队列`);
    
    // 添加到批量图片列表
    validFiles.forEach(file => {
        const imageId = window.DataFormatters.generateUniqueId('img');
        const imageItem = {
            id: imageId,
            file: file,
            status: 'pending', // pending, processing, completed, error
            result: null,
            error: null
        };
        batchImages.push(imageItem);
        console.log(`添加图片: ${file.name} (${window.DataFormatters.formatFileSize(file.size)})`);
    });
    
    // 更新界面
    updateBatchImagesDisplay();
    showBatchImagesContainer();
}

// 显示批量图片容器
function showBatchImagesContainer() {
    const elements = window.DOMUtils.elements;
    if (elements.uploadArea) {
        elements.uploadArea.style.display = 'none';
    }
    if (elements.batchImagesContainer) {
        elements.batchImagesContainer.style.display = 'block';
    }
}

// 隐藏批量图片容器
function hideBatchImagesContainer() {
    const elements = window.DOMUtils.elements;
    if (elements.uploadArea) {
        elements.uploadArea.style.display = 'block';
    }
    if (elements.batchImagesContainer) {
        elements.batchImagesContainer.style.display = 'none';
    }
}

// 更新批量图片显示
function updateBatchImagesDisplay() {
    const elements = window.DOMUtils.elements;
    if (elements.imageCount) {
        elements.imageCount.textContent = batchImages.length;
    }

    if (elements.imagesGrid) {
        elements.imagesGrid.innerHTML = '';

        batchImages.forEach(imageItem => {
            const imageElement = createImageItemElement(imageItem);
            elements.imagesGrid.appendChild(imageElement);
        });
    }

    // 更新批量处理按钮状态
    if (elements.processAllImagesBtn) {
        elements.processAllImagesBtn.disabled = batchImages.length === 0;
    }
}

// 创建图片项元素
function createImageItemElement(imageItem) {
    const div = document.createElement('div');
    div.className = `image-item ${imageItem.status}`;
    div.dataset.imageId = imageItem.id;

    const fileSize = window.DataFormatters.formatFileSize(imageItem.file.size);
    const statusText = window.DataFormatters.getStatusText(imageItem.status);
    
    div.innerHTML = `
        <div class="image-preview-container">
            <img class="image-preview-img" src="${URL.createObjectURL(imageItem.file)}" alt="预览图片">
            <div class="image-status ${imageItem.status}">${statusText}</div>
        </div>
        <div class="image-info">
            <div class="image-filename">${imageItem.file.name}</div>
            <div class="image-size">${fileSize}</div>
        </div>
        ${imageItem.status === 'processing' ? '<div class="image-progress"><div class="image-progress-bar"></div></div>' : ''}
        <div class="image-actions">
            <button class="btn btn-small btn-secondary remove-single-image" data-image-id="${imageItem.id}">
                <i class="fas fa-trash"></i>
                移除
            </button>
            <button class="btn btn-small btn-primary process-single-image" data-image-id="${imageItem.id}" 
                    ${imageItem.status === 'processing' ? 'disabled' : ''}>
                <i class="fas fa-play"></i>
                ${imageItem.status === 'completed' ? '重新处理' : '处理'}
            </button>
        </div>
        ${imageItem.result ? createResultPreview(imageItem.result) : ''}
        ${imageItem.error ? `<div class="error-info"><i class="fas fa-exclamation-triangle"></i> ${imageItem.error}</div>` : ''}
    `;
    
    return div;
}

// 创建结果预览
function createResultPreview(result) {
    if (!result) return '';
    
    // 兼容不同的数据格式
    const entities = result.sensitive_words || result.entities || [];
    const entityCount = entities.length;
    const filteredCount = result.filtered_entities ? result.filtered_entities.length : 0;
    
    return `
        <div class="result-preview">
            <div class="result-summary">
                <span class="result-stat sensitive-stat">
                    <i class="fas fa-exclamation-triangle"></i>
                    检测到敏感信息 ${entityCount} 个
                </span>
                <span class="result-stat filtered-stat">
                    <i class="fas fa-filter"></i>
                    过滤词汇 ${filteredCount} 个
                </span>
            </div>
            <button class="btn btn-small btn-outline view-detail" data-image-id="${result.imageId}">
                <i class="fas fa-eye"></i>
                查看详情
            </button>
        </div>
    `;
}

// 处理单张图片
async function processSingleImage(imageId) {
    const imageItem = batchImages.find(img => img.id === imageId);
    if (!imageItem) {
        console.error(`未找到图片项: ${imageId}`);
        return;
    }

    console.log(`开始处理图片: ${imageItem.file.name}`);

    // 清除旧的处理结果，确保重新处理时不会有缓存问题
    imageItem.result = null;
    imageItem.error = null;

    // 更新状态为处理中
    imageItem.status = 'processing';
    updateImageItemDisplay(imageItem);
    
    try {
        // 先进行OCR识别
        const ocrResult = await window.APIClient.performOCR(imageItem.file);

        if (!ocrResult.success) {
            throw new Error(ocrResult.error || 'OCR识别失败');
        }

        const extractedText = ocrResult.text;

        if (!extractedText || extractedText.trim() === '') {
            throw new Error('未能从图片中提取到文本');
        }

        // 进行脱敏处理
        const deidentifyResult = await window.APIClient.performDeidentification(extractedText);
        
        if (!deidentifyResult.success) {
            throw new Error(deidentifyResult.error || '脱敏处理失败');
        }
        
        // 保存结果 - 使用深拷贝确保数据独立性，避免缓存问题
        imageItem.result = JSON.parse(JSON.stringify({
            ...deidentifyResult.data,
            imageId: imageId,
            originalText: extractedText,
            // 确保字段名称与showResults函数期望的一致
            sensitive_words: deidentifyResult.data.entities || deidentifyResult.data.sensitive_words || [],
            total_entities: deidentifyResult.data.total_entities || (deidentifyResult.data.entities || deidentifyResult.data.sensitive_words || []).length,
            // 添加时间戳确保数据新鲜度
            processedAt: new Date().toISOString()
        }));
        imageItem.status = 'completed';

        console.log(`✅ 图片 ${imageItem.file.name} 处理完成，结果已保存:`, imageItem.result);
        
        
        // 如果是单独处理（非批量处理），自动显示结果
        const elements = window.DOMUtils.elements;
        const isInBatchProcessing = elements.processAllImagesBtn && elements.processAllImagesBtn.disabled;
        if (!isInBatchProcessing) {
            console.log('单独处理完成，自动显示结果');
            setTimeout(() => {
                window.ResultDisplay.viewImageDetail(imageId);
                // 移除自动滚动行为 - 保持用户当前的滚动位置
                // if (elements.resultsSection && elements.resultsSection.style.display === 'block') {
                //     elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
                // }
            }, 300);
        }

    } catch (error) {
        console.error('处理图片失败:', error);
        imageItem.status = 'error';
        imageItem.error = error.message;
    }
    
    // 更新显示
    updateImageItemDisplay(imageItem);
}

// 更新单个图片项显示
function updateImageItemDisplay(imageItem) {
    const imageElement = document.querySelector(`[data-image-id="${imageItem.id}"]`);
    if (imageElement) {
        const newElement = createImageItemElement(imageItem);
        imageElement.parentNode.replaceChild(newElement, imageElement);
    }
}

// 批量处理所有图片（四并发）
async function processAllImages() {
    const pendingImages = batchImages.filter(img => img.status === 'pending' || img.status === 'error');

    if (pendingImages.length === 0) {
        window.UIUtils.showError('没有待处理的图片');
        // 确保按钮状态正确
        updateBatchProcessingButtonState('idle');
        return;
    }

    console.log(`🚀 开始 ${CONCURRENT_CONFIG.maxConcurrency} 并发批量处理 ${pendingImages.length} 张图片`);

    // 记录开始时间
    const startTime = Date.now();

    // 设置批量处理按钮为处理中状态
    updateBatchProcessingButtonState('processing', { concurrency: CONCURRENT_CONFIG.maxConcurrency });

    let lastCompletedImage = null;
    let processedCount = 0;
    const totalCount = pendingImages.length;
    let isCompleting = false; // 添加状态锁，防止重复触发完成状态

    try {
        // 使用配置的并发数量处理
        await processConcurrentImages(pendingImages, CONCURRENT_CONFIG.maxConcurrency, (completedImage) => {
            processedCount++;

            // 更新按钮显示进度
            const progress = Math.round((processedCount / totalCount) * 100);

            // 只有在未达到100%时才显示进度状态
            if (processedCount < totalCount) {
                updateBatchProcessingButtonState('progress', {
                    current: processedCount,
                    total: totalCount,
                    percentage: progress
                });
            } else if (!isCompleting) {
                // 当达到100%时，显示"正在完成..."状态（只执行一次）
                isCompleting = true;
                updateBatchProcessingButtonState('processing', {
                    concurrency: '正在完成'
                });
            }

            // 记录最后一个成功处理的图片
            if (completedImage && completedImage.status === 'completed' && completedImage.result) {
                lastCompletedImage = completedImage;
            }
        });
        
        // 批量处理完成后，自动显示最后一个成功处理的图片结果
        if (lastCompletedImage) {
            console.log(`批量处理完成，自动显示最后处理的图片结果: ${lastCompletedImage.file.name}`);
            window.ResultDisplay.viewImageDetail(lastCompletedImage.id);

            // 移除自动滚动行为 - 保持用户当前的滚动位置
            // setTimeout(() => {
            //     const elements = window.DOMUtils.elements;
            //     if (elements.resultsSection && elements.resultsSection.style.display === 'block') {
            //         elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
            //     }
            // }, 300);
        }

        // 触发批量处理完成事件（用于更新悬浮导航栏）
        const successCount = batchImages.filter(img => img.status === 'completed').length;
        const failureCount = batchImages.filter(img => img.status === 'error').length;

        document.dispatchEvent(new CustomEvent('batchProcessingComplete', {
            detail: {
                successCount,
                failureCount,
                totalImages: batchImages.length
            }
        }));

        // 计算处理时间和性能统计
        const endTime = Date.now();
        const totalTime = (endTime - startTime) / 1000; // 转换为秒
        const avgTimePerImage = totalTime / totalCount;
        const throughput = totalCount / totalTime; // 图片/秒

        console.log(`🎉 ${CONCURRENT_CONFIG.maxConcurrency}并发批量处理完成!`);
        console.log(`📊 处理统计:`);
        console.log(`  - 成功: ${successCount} 张`);
        console.log(`  - 失败: ${failureCount} 张`);
        console.log(`  - 总时间: ${totalTime.toFixed(2)} 秒`);
        console.log(`  - 平均每张: ${avgTimePerImage.toFixed(2)} 秒`);
        console.log(`  - 处理速度: ${throughput.toFixed(2)} 图片/秒`);
        console.log(`  - 并发效率: ${(throughput * CONCURRENT_CONFIG.maxConcurrency).toFixed(2)}x 理论单线程速度`);
        console.log(`  - 并发配置: ${CONCURRENT_CONFIG.maxConcurrency} 线程, 最小间隔 ${CONCURRENT_CONFIG.minDelayMs}ms`);

        console.log(`✅ ${CONCURRENT_CONFIG.maxConcurrency}并发批量处理全部完成`);

        // 短暂延迟后显示处理完成状态，让用户看到100%进度
        setTimeout(() => {
            updateBatchProcessingButtonState('completed', {
                successCount,
                failureCount,
                totalCount
            });

            // 延迟恢复到idle状态，让用户看到完成状态
            setTimeout(() => {
                updateBatchProcessingButtonState('idle');
            }, 5000); // 5秒后恢复到idle状态
        }, 800); // 800ms延迟显示完成状态

    } catch (error) {
        console.error('❌ 批量处理过程中发生错误:', error);
        window.UIUtils.showError(`批量处理失败: ${error.message}`);

        // 错误情况下也要恢复按钮状态
        updateBatchProcessingButtonState('idle');
    }
}

/**
 * 并发处理图片队列
 * @param {Array} imageQueue - 待处理的图片队列
 * @param {number} concurrency - 并发数量
 * @param {Function} onProgress - 进度回调函数
 */
async function processConcurrentImages(imageQueue, concurrency = 4, onProgress = null) {
    console.log(`📊 开始 ${concurrency} 并发处理 ${imageQueue.length} 张图片`);

    // 创建并发处理池
    const processingPool = [];
    let queueIndex = 0;

    // 处理单个图片的包装函数
    const processImageWrapper = async (imageItem) => {
        const startTime = Date.now();
        try {
            console.log(`🔄 [并发] 开始处理图片: ${imageItem.file.name}`);
            await processSingleImage(imageItem.id);

            const processingTime = Date.now() - startTime;
            console.log(`✅ [并发] 完成处理图片: ${imageItem.file.name} (状态: ${imageItem.status}, 耗时: ${processingTime}ms)`);

            if (onProgress) {
                onProgress(imageItem);
            }

            return imageItem;
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error(`❌ [并发] 处理图片失败: ${imageItem.file.name} (耗时: ${processingTime}ms)`, error);

            // 确保错误状态正确设置
            imageItem.status = 'error';
            imageItem.error = error.message || '处理失败';

            // 更新UI显示
            updateImageItemDisplay(imageItem);

            if (onProgress) {
                onProgress(imageItem);
            }

            return imageItem;
        }
    };

    // 启动初始并发任务
    for (let i = 0; i < Math.min(concurrency, imageQueue.length); i++) {
        if (queueIndex < imageQueue.length) {
            const imageItem = imageQueue[queueIndex++];
            const promise = processImageWrapper(imageItem);
            processingPool.push(promise);
        }
    }

    // 处理剩余队列
    while (processingPool.length > 0) {
        // 等待任意一个任务完成
        const completedPromise = await Promise.race(processingPool);

        // 从处理池中移除已完成的任务
        const completedIndex = processingPool.indexOf(completedPromise);
        processingPool.splice(completedIndex, 1);

        // 如果还有待处理的图片，添加新任务到处理池
        if (queueIndex < imageQueue.length) {
            const nextImageItem = imageQueue[queueIndex++];
            const nextPromise = processImageWrapper(nextImageItem);
            processingPool.push(nextPromise);
        }
    }

    console.log(`🎉 ${concurrency} 并发处理完成！`);
}

// 清空所有图片
function clearAllImages() {
    batchImages = [];
    updateBatchImagesDisplay();
    hideBatchImagesContainer();
    // 重置文件输入
    const elements = window.DOMUtils.elements;
    if (elements.fileInput) {
        elements.fileInput.value = '';
    }
    console.log('已清空所有图片');
}

// 移除单张图片
function removeSingleImage(imageId) {
    const index = batchImages.findIndex(img => img.id === imageId);
    if (index !== -1) {
        const fileName = batchImages[index].file.name;
        batchImages.splice(index, 1);
        updateBatchImagesDisplay();
        
        if (batchImages.length === 0) {
            hideBatchImagesContainer();
        }
        
        console.log(`移除图片: ${fileName}`);
    }
}

// 图片网格点击事件处理
function handleImageGridClick(event) {
    const target = event.target;
    const button = target.closest('button');
    
    if (!button) return;
    
    const imageId = button.dataset.imageId;
    
    if (button.classList.contains('remove-single-image')) {
        removeSingleImage(imageId);
    } else if (button.classList.contains('process-single-image')) {
        processSingleImage(imageId);
    } else if (button.classList.contains('view-detail')) {
        // 显示图片详情并跳转到敏感信息标注区域
        window.ResultDisplay.viewImageDetail(imageId);

        // 平滑滚动到敏感信息标注区域
        setTimeout(() => {
            const highlightedSection = document.getElementById('highlighted-section');
            if (highlightedSection) {
                highlightedSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                console.log('✓ 已跳转到敏感信息标注区域');
            }
        }, 300); // 等待结果显示完成后再滚动
    }
}

// 获取批量图片数据（供其他模块使用）
function getBatchImages() {
    return batchImages;
}

// 根据ID查找图片项
function findImageById(imageId) {
    return batchImages.find(img => img.id === imageId);
}

// 重新批量推理功能
function reprocessAllImages() {
    console.log('🔄 开始重新批量推理...');

    // 检查是否有图片
    if (batchImages.length === 0) {
        window.UIUtils.showError('没有可重新处理的图片');
        return;
    }

    // 重置所有图片状态为pending
    let resetCount = 0;
    batchImages.forEach(imageItem => {
        if (imageItem.status === 'completed' || imageItem.status === 'error') {
            imageItem.status = 'pending';
            imageItem.result = null;
            imageItem.error = null;
            resetCount++;

            // 更新UI显示
            updateImageItemDisplay(imageItem);
        }
    });

    console.log(`✓ 已重置 ${resetCount} 张图片状态为待处理`);

    // 自动触发批量处理
    setTimeout(() => {
        processAllImages();
    }, 100); // 短暂延迟确保UI更新完成
}

// 根据ID查找图片项
function findImageById(imageId) {
    return batchImages.find(img => img.id === imageId);
}

// 导出批量图片处理功能
window.BatchImageProcessing = {
    handleBatchFileUpload,
    showBatchImagesContainer,
    hideBatchImagesContainer,
    updateBatchImagesDisplay,
    processSingleImage,
    processAllImages,
    reprocessAllImages,
    clearAllImages,
    removeSingleImage,
    handleImageGridClick,
    getBatchImages,
    findImageById,
    updateBatchProcessingButtonState
};

console.log('✓ 批量图片处理模块已加载');
