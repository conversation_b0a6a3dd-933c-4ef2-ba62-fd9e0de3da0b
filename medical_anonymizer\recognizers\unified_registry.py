"""
统一识别器注册模块

本模块提供统一的识别器注册和管理功能，将不同类型的识别器
（模式匹配器、上下文分析器、高级检测器）整合到统一的接口中。

这个模块作为识别器管理的核心，确保所有识别器的统一访问和配置。
"""

from typing import List
from presidio_analyzer import PatternRecognizer

# 导入模式匹配器（原纯规则判断识别器）
from .pattern_matchers import (
    ChineseIDRecognizer,
    LicensePlateRecognizer,
    EthnicityRecognizer,
    EducationLevelRecognizer,
    PrivacyInfoRecognizer,
    CompleteAddressRecognizer,
    URLRecognizer,
    StructuredFieldRecognizer,
    SponsorRecognizer,
    get_rule_based_recognizers,
    get_rule_based_recognizer_names
)

# 导入上下文分析器（原上下文判断识别器）
from .contextual_analyzers import (
    # 升级的上下文识别器（从Pattern Matchers转移）
    MobilePhoneRecognizer,
    LandlinePhoneRecognizer,
    MedicalInsuranceRecognizer,
    BankCardRecognizer,
    # 原有的上下文识别器
    WeChatRecognizer,
    CertificateRecognizer,
    QQNumberRecognizer,
    # FamilyRelationshipRecognizer,
    # MedicalPositionRecognizer,
    OrganizationRecognizer,
    # 从advanced_detectors迁移的识别器
    GPSCoordinateRecognizer,
    # CommunicationContentRecognizer,
    MedicalNumberRecognizer,
    get_context_based_recognizers,
    get_context_based_recognizer_names
)


def get_all_recognizers() -> List[PatternRecognizer]:
    """
    获取所有可用的医疗识别器
    
    这个函数组合了纯规则判断识别器、上下文判断识别器和其他复杂识别器，
    保持与原有系统的完全兼容性。

    返回:
        List[PatternRecognizer]: 所有识别器实例的列表
    """
    recognizers = []
    
    # 添加纯规则判断识别器
    recognizers.extend(get_rule_based_recognizers())
    
    # 添加上下文判断识别器 (自动使用增强版本)
    # 注意：get_context_based_recognizers()现在返回增强版本的识别器
    # 包括StrictMedicalRecordRecognizer和EnhancedWeChatRecognizer
    recognizers.extend(get_context_based_recognizers())
    
    # 注意：原advanced_detectors中的识别器已经迁移到相应的架构层中
    # 它们现在通过get_rule_based_recognizers()和get_context_based_recognizers()自动包含
    
    return recognizers



def get_recognizers_by_type():
    """
    按类型获取识别器，用于分析和调试

    返回:
        dict: 包含不同类型识别器的字典
    """
    rule_based = get_rule_based_recognizers()
    context_based = get_context_based_recognizers()

    nlp_based_names = get_nlp_based_recognizer_names()

    return {
        'rule_based': rule_based,
        'context_based': context_based,
        'rule_based_names': get_rule_based_recognizer_names(),
        'context_based_names': get_context_based_recognizer_names(),
        'nlp_based_names': nlp_based_names,
        'rule_based_count': len(rule_based),
        'context_based_count': len(context_based),
        'nlp_based_count': len(nlp_based_names)
    }


def get_nlp_based_recognizer_names():
    """
    获取所有启用的NLP识别器实体类型名称

    只返回spaCy实体类型名称，因为只有这些需要动态控制。

    Returns:
        List[str]: 启用的spaCy实体类型名称列表
    """
    from .patterns import UNIFIED_RECOGNIZER_CONFIGS

    return [
        entity_name for entity_name, config in UNIFIED_RECOGNIZER_CONFIGS.items()
        if config.is_nlp_based and config.enabled
    ]


def get_recognizer_statistics():
    """
    获取识别器统计信息

    返回:
        dict: 识别器统计信息
    """
    rule_based = get_rule_based_recognizers()
    context_based = get_context_based_recognizers()
    nlp_based_names = get_nlp_based_recognizer_names()
    all_recognizers = get_all_recognizers()

    total_configured = len(rule_based) + len(context_based) + len(nlp_based_names)

    return {
        'total_recognizers': len(all_recognizers),
        'total_configured': total_configured,
        'rule_based_count': len(rule_based),
        'context_based_count': len(context_based),
        'nlp_based_count': len(nlp_based_names),
        'other_count': len(all_recognizers) - len(rule_based) - len(context_based),
        'rule_based_percentage': round(len(rule_based) / total_configured * 100, 1) if total_configured > 0 else 0,
        'context_based_percentage': round(len(context_based) / total_configured * 100, 1) if total_configured > 0 else 0,
        'nlp_based_percentage': round(len(nlp_based_names) / total_configured * 100, 1) if total_configured > 0 else 0
    }


# 为了保持完全兼容性，重新导出所有识别器类
__all__ = [
    # 纯规则判断识别器
    'ChineseIDRecognizer',
    'LicensePlateRecognizer',
    'EthnicityRecognizer',
    'EducationLevelRecognizer',
    'PrivacyInfoRecognizer',
    'CompleteAddressRecognizer',
    'URLRecognizer',
    'StructuredFieldRecognizer',
    'SponsorRecognizer',

    # 升级的上下文识别器（从Pattern Matchers转移）
    'MobilePhoneRecognizer',
    'LandlinePhoneRecognizer',
    'MedicalInsuranceRecognizer',
    'BankCardRecognizer',

    # 原有的上下文判断识别器
    'WeChatRecognizer',
    'CertificateRecognizer',
    'QQNumberRecognizer',
    # 'FamilyRelationshipRecognizer',
    # 'MedicalPositionRecognizer',
    'OrganizationRecognizer',

    # 从advanced_detectors迁移的识别器
    'GPSCoordinateRecognizer',
    # 'CommunicationContentRecognizer',
    'MedicalNumberRecognizer',

    # 函数
    'get_all_recognizers',
    'get_recognizers_by_type',
    'get_recognizer_statistics'
]
