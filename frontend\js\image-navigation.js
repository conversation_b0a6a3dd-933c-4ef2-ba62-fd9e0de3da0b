/**
 * 图片导航模块
 * 提供批量图片处理结果的智能导航功能
 */

// 当前导航状态（图片导航模块）
let imageNavCurrentIndex = 0;
let imageNavProcessedImages = [];
let imageNavEnabled = false;

/**
 * 初始化图片导航系统
 */
function initializeImageNavigation() {
    console.log('🧭 初始化图片导航系统');
    
    // 添加键盘快捷键支持
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    console.log('✓ 图片导航系统初始化完成');
}

/**
 * 更新可导航的图片列表
 */
function updateNavigableImages() {
    const batchImages = window.BatchImageProcessing.getBatchImages();
    imageNavProcessedImages = batchImages.filter(img => img.status === 'completed' && img.result);

    console.log(`📊 更新可导航图片列表: ${imageNavProcessedImages.length} 张已处理图片`);

    // 如果有多张图片，启用导航
    imageNavEnabled = imageNavProcessedImages.length > 1;

    // 更新所有导航控件的显示状态
    updateNavigationControls();

    return imageNavProcessedImages;
}

/**
 * 创建导航控件HTML
 */
function createNavigationControls() {
    if (!navigationEnabled || processedImages.length <= 1) {
        return '';
    }
    
    const currentImage = processedImages[currentImageIndex];
    const totalImages = processedImages.length;
    
    // 根据图片数量决定显示方式
    const showThumbnails = totalImages <= 10;
    
    return `
        <div class="image-navigation-controls">
            <!-- 导航按钮和位置指示器 -->
            <div class="nav-main-controls">
                <button class="nav-btn nav-prev" ${currentImageIndex === 0 ? 'disabled' : ''} 
                        onclick="window.ImageNavigation.navigateToImage(${currentImageIndex - 1})"
                        title="上一张 (←)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                <div class="nav-position-indicator">
                    <span class="current-image-info">
                        <i class="fas fa-image"></i>
                        ${currentImage ? currentImage.file.name : '未知图片'}
                    </span>
                    <span class="position-text">
                        第 ${currentImageIndex + 1} 张，共 ${totalImages} 张
                    </span>
                </div>
                
                <button class="nav-btn nav-next" ${currentImageIndex === totalImages - 1 ? 'disabled' : ''} 
                        onclick="window.ImageNavigation.navigateToImage(${currentImageIndex + 1})"
                        title="下一张 (→)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            
            ${showThumbnails ? createThumbnailNavigation() : ''}
        </div>
    `;
}

/**
 * 创建缩略图导航
 */
function createThumbnailNavigation() {
    if (processedImages.length <= 1) return '';
    
    const thumbnailsHtml = processedImages.map((img, index) => {
        const isActive = index === currentImageIndex;
        const statusIcon = getStatusIcon(img);
        
        return `
            <div class="nav-thumbnail ${isActive ? 'active' : ''}" 
                 onclick="window.ImageNavigation.navigateToImage(${index})"
                 title="${img.file.name}">
                <div class="thumbnail-image">
                    <img src="${createImagePreviewUrl(img.file)}" alt="${img.file.name}" />
                    <div class="thumbnail-overlay">
                        ${statusIcon}
                        <span class="thumbnail-index">${index + 1}</span>
                    </div>
                </div>
                <div class="thumbnail-info">
                    <div class="thumbnail-name">${truncateFileName(img.file.name, 12)}</div>
                    <div class="thumbnail-stats">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${img.result.sensitive_words ? img.result.sensitive_words.length : 0}
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    return `
        <div class="nav-thumbnails-container">
            <div class="nav-thumbnails-scroll">
                ${thumbnailsHtml}
            </div>
        </div>
    `;
}

/**
 * 获取状态图标
 */
function getStatusIcon(imageItem) {
    switch (imageItem.status) {
        case 'completed':
            return '<i class="fas fa-check-circle status-success"></i>';
        case 'error':
            return '<i class="fas fa-exclamation-circle status-error"></i>';
        case 'processing':
            return '<i class="fas fa-spinner fa-spin status-processing"></i>';
        default:
            return '<i class="fas fa-clock status-pending"></i>';
    }
}

/**
 * 截断文件名
 */
function truncateFileName(fileName, maxLength) {
    if (fileName.length <= maxLength) return fileName;
    
    const extension = fileName.split('.').pop();
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
    
    return truncatedName + '.' + extension;
}

/**
 * 更新导航控件显示（现在用于侧边栏导航）
 */
function updateNavigationControls() {
    // 通知侧边栏导航更新
    if (window.SidebarNavigation) {
        window.SidebarNavigation.updateSidebarContent();
    }

    console.log('✓ 导航控件更新请求已发送到侧边栏导航');
}

/**
 * 导航到指定图片
 */
function navigateToImage(index) {
    if (!navigationEnabled || index < 0 || index >= processedImages.length) {
        console.warn(`⚠️ 无效的图片索引: ${index}`);
        return;
    }
    
    const targetImage = processedImages[index];
    if (!targetImage || !targetImage.result) {
        console.warn(`⚠️ 图片 ${index} 没有处理结果`);
        return;
    }
    
    console.log(`🧭 导航到图片 ${index + 1}/${processedImages.length}: ${targetImage.file.name}`);

    // 更新当前索引
    currentImageIndex = index;

    // 显示该图片的结果（不触发自动滚动）
    window.ResultDisplay.displayResults(targetImage.result);

    // 更新导航控件
    updateNavigationControls();

    // 触发导航事件（用于更新悬浮导航栏）
    document.dispatchEvent(new CustomEvent('imageNavigated', {
        detail: {
            index: currentImageIndex,
            image: targetImage,
            total: processedImages.length
        }
    }));

    console.log('✓ 图片导航完成，保持当前滚动位置');
}

/**
 * 键盘导航处理
 */
function handleKeyboardNavigation(event) {
    if (!navigationEnabled) return;
    
    // 检查是否在输入框中
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        return;
    }
    
    switch (event.key) {
        case 'ArrowLeft':
            event.preventDefault();
            if (currentImageIndex > 0) {
                navigateToImage(currentImageIndex - 1);
            }
            break;
        case 'ArrowRight':
            event.preventDefault();
            if (currentImageIndex < processedImages.length - 1) {
                navigateToImage(currentImageIndex + 1);
            }
            break;
        case 'Home':
            event.preventDefault();
            navigateToImage(0);
            break;
        case 'End':
            event.preventDefault();
            navigateToImage(processedImages.length - 1);
            break;
    }
}

/**
 * 设置当前图片（用于外部调用）
 */
function setCurrentImage(imageId) {
    const index = processedImages.findIndex(img => img.id === imageId);
    if (index !== -1) {
        currentImageIndex = index;
        updateNavigationControls();
    }
}

/**
 * 创建图片预览URL
 */
function createImagePreviewUrl(file) {
    if (!file) return '';

    try {
        return URL.createObjectURL(file);
    } catch (error) {
        console.warn('创建图片预览URL失败:', error);
        return '';
    }
}

/**
 * 获取当前图片信息
 */
function getCurrentImageInfo() {
    if (!navigationEnabled || currentImageIndex >= processedImages.length) {
        return null;
    }

    return {
        index: currentImageIndex,
        image: processedImages[currentImageIndex],
        total: processedImages.length
    };
}

// 导出图片导航功能
window.ImageNavigation = {
    initializeImageNavigation,
    updateNavigableImages,
    createNavigationControls,
    updateNavigationControls,
    navigateToImage,
    setCurrentImage,
    getCurrentImageInfo
};

console.log('✓ 图片导航模块已加载');
