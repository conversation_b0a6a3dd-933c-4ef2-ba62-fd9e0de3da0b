/**
 * 侧边栏图片导航模块
 * 提供简洁的侧边栏图片导航功能
 */

let sidebarElement = null;
let isVisible = false;
let processedImages = [];
let currentImageIndex = 0;
let navigationEnabled = false;

/**
 * 初始化侧边栏导航
 */
function initializeSidebarNavigation() {
    console.log('🎯 初始化侧边栏图片导航');
    
    // 创建侧边栏元素
    createSidebarElement();
    
    // 设置事件监听器
    setupEventListeners();
    
    console.log('✓ 侧边栏导航初始化完成');
}

/**
 * 创建侧边栏元素
 */
function createSidebarElement() {
    // 如果已存在，先移除
    if (sidebarElement) {
        sidebarElement.remove();
    }
    
    sidebarElement = document.createElement('div');
    sidebarElement.id = 'image-sidebar-nav';
    sidebarElement.className = 'image-sidebar-navigation';
    sidebarElement.innerHTML = `
        <div class="sidebar-header">
            <div class="sidebar-title">
                <h4><i class="fas fa-images"></i> 图片导航</h4>
                <div class="sidebar-legend">
                    <span class="legend-item">
                        <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                        <small>脱敏</small>
                    </span>
                    <span class="legend-item">
                        <i class="fas fa-filter" style="color: #6c757d;"></i>
                        <small>过滤</small>
                    </span>
                </div>
            </div>
            <button class="sidebar-toggle" onclick="window.SidebarNavigation.toggleSidebar()" title="折叠/展开">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <div class="sidebar-controls">
                <button class="sidebar-btn sidebar-prev" onclick="window.SidebarNavigation.navigatePrevious()" title="上一张 (←)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="sidebar-position">
                    <span class="current-index">1</span>
                    <span class="separator">/</span>
                    <span class="total-count">1</span>
                </div>
                <button class="sidebar-btn sidebar-next" onclick="window.SidebarNavigation.navigateNext()" title="下一张 (→)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="sidebar-thumbnails">
                <!-- 缩略图将在这里动态生成 -->
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(sidebarElement);
    
    console.log('✓ 侧边栏元素已创建');
}

/**
 * 更新侧边栏内容
 */
function updateSidebarContent() {
    if (!sidebarElement) return;

    // 获取所有批量图片
    const batchImages = window.BatchImageProcessing ? window.BatchImageProcessing.getBatchImages() : [];
    // 获取已处理的图片用于导航
    processedImages = batchImages.filter(img => img.status === 'completed' && img.result);

    // 如果有任何图片（不管是否处理完成），都显示侧边栏
    const hasAnyImages = batchImages.length > 0;
    navigationEnabled = processedImages.length > 1;

    if (hasAnyImages) {
        // 显示侧边栏
        showSidebar();

        // 更新控件状态
        updateSidebarControls();

        // 更新缩略图（显示所有图片）
        updateSidebarThumbnails();
    } else {
        // 隐藏侧边栏
        hideSidebar();
    }

    console.log(`📊 侧边栏内容已更新: ${batchImages.length} 张图片 (${processedImages.length} 张已处理)`);
}

/**
 * 更新侧边栏控件状态
 */
function updateSidebarControls() {
    if (!sidebarElement || !navigationEnabled) return;
    
    const prevBtn = sidebarElement.querySelector('.sidebar-prev');
    const nextBtn = sidebarElement.querySelector('.sidebar-next');
    const currentIndexSpan = sidebarElement.querySelector('.current-index');
    const totalCountSpan = sidebarElement.querySelector('.total-count');
    
    if (prevBtn) prevBtn.disabled = currentImageIndex === 0;
    if (nextBtn) nextBtn.disabled = currentImageIndex === processedImages.length - 1;
    if (currentIndexSpan) currentIndexSpan.textContent = currentImageIndex + 1;
    if (totalCountSpan) totalCountSpan.textContent = processedImages.length;
}

/**
 * 更新侧边栏缩略图
 */
function updateSidebarThumbnails() {
    if (!sidebarElement) return;

    const thumbnailsContainer = sidebarElement.querySelector('.sidebar-thumbnails');
    if (!thumbnailsContainer) return;

    // 获取所有批量图片（包括已处理和未处理的）
    const batchImages = window.BatchImageProcessing ? window.BatchImageProcessing.getBatchImages() : [];

    if (batchImages.length === 0) {
        thumbnailsContainer.innerHTML = '<div class="no-images-message">暂无图片</div>';
        return;
    }

    const thumbnailsHtml = batchImages.map((img, index) => {
        const isActive = index === currentImageIndex && img.status === 'completed' && img.result;
        const statusIcon = getStatusIcon(img);
        const sensitiveCount = img.result && img.result.sensitive_words ? img.result.sensitive_words.length : 0;
        const filteredCount = img.result && img.result.filtered_entities ? img.result.filtered_entities.length : 0;

        // 判断是否显示重新处理按钮（只有已处理的图片才显示）
        const showReprocessButton = img.status === 'completed' || img.status === 'error';

        return `
            <div class="sidebar-thumbnail ${isActive ? 'active' : ''}"
                 data-image-id="${img.id}"
                 title="${img.file.name}">
                <div class="thumbnail-image" onclick="window.SidebarNavigation.handleThumbnailClick('${img.id}', ${index})">
                    <img src="${createImagePreviewUrl(img.file)}" alt="${img.file.name}" />
                    <div class="thumbnail-overlay">
                        ${statusIcon}
                        <span class="thumbnail-number">${index + 1}</span>
                    </div>
                </div>
                <div class="thumbnail-info">
                    <div class="thumbnail-name">${truncateFileName(img.file.name, 15)}</div>
                    <div class="thumbnail-stats">
                        <div class="stat-item sensitive" title="脱敏关键词数量: ${sensitiveCount}">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span class="stat-count">${sensitiveCount}</span>
                        </div>
                        <div class="stat-item filtered" title="过滤关键词数量: ${filteredCount}">
                            <i class="fas fa-filter"></i>
                            <span class="stat-count">${filteredCount}</span>
                        </div>
                        ${showReprocessButton ? `
                            <button class="reprocess-btn"
                                    onclick="window.SidebarNavigation.reprocessSingleImage('${img.id}', event)"
                                    title="重新处理此图片"
                                    ${img.status === 'processing' ? 'disabled' : ''}>
                                <i class="fas fa-sync"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');

    thumbnailsContainer.innerHTML = thumbnailsHtml;
}

/**
 * 获取状态图标
 */
function getStatusIcon(img) {
    if (img.status === 'completed') {
        return '<i class="fas fa-check-circle status-success"></i>';
    } else if (img.status === 'error') {
        return '<i class="fas fa-exclamation-circle status-error"></i>';
    } else {
        return '<i class="fas fa-clock status-processing"></i>';
    }
}

/**
 * 创建图片预览URL
 */
function createImagePreviewUrl(file) {
    return URL.createObjectURL(file);
}

/**
 * 截断文件名
 */
function truncateFileName(filename, maxLength) {
    if (filename.length <= maxLength) return filename;
    const ext = filename.split('.').pop();
    const name = filename.substring(0, filename.lastIndexOf('.'));
    const truncated = name.substring(0, maxLength - ext.length - 4) + '...';
    return truncated + '.' + ext;
}

/**
 * 显示侧边栏
 */
function showSidebar() {
    if (!sidebarElement) return;

    sidebarElement.classList.add('visible');
    isVisible = true;

    // 不再调整主内容区域 - 保持原始位置
    // adjustMainContentLayout(true);

    console.log('✓ 侧边栏已显示');
}

/**
 * 隐藏侧边栏
 */
function hideSidebar() {
    if (!sidebarElement) return;

    sidebarElement.classList.remove('visible');
    isVisible = false;

    // 不再调整主内容区域 - 保持原始位置
    // adjustMainContentLayout(false);

    console.log('✓ 侧边栏已隐藏');
}

/**
 * 切换侧边栏显示状态
 */
function toggleSidebar() {
    if (!navigationEnabled) return;
    
    if (isVisible) {
        hideSidebar();
    } else {
        showSidebar();
    }
}

/**
 * 调整主内容区域布局（已禁用 - 保持主内容区域位置不变）
 */
function adjustMainContentLayout(sidebarVisible) {
    // 不再调整主内容区域布局
    // 侧边栏将覆盖在主内容之上，而不是推动主内容
    return;

    // const mainContainer = document.querySelector('.container');
    // if (!mainContainer) return;
    //
    // if (sidebarVisible) {
    //     mainContainer.style.marginRight = '320px'; // 为侧边栏预留空间
    // } else {
    //     mainContainer.style.marginRight = '0';
    // }
}

/**
 * 导航到指定图片
 */
function navigateToImage(index) {
    if (!navigationEnabled || index < 0 || index >= processedImages.length) {
        console.warn(`⚠️ 无效的图片索引: ${index}`);
        return;
    }
    
    const targetImage = processedImages[index];
    if (!targetImage || !targetImage.result) {
        console.warn(`⚠️ 图片 ${index} 没有处理结果`);
        return;
    }
    
    console.log(`🎯 侧边栏导航到图片 ${index + 1}/${processedImages.length}: ${targetImage.file.name}`);
    
    // 更新当前索引
    currentImageIndex = index;
    
    // 显示该图片的结果
    if (window.ResultDisplay) {
        window.ResultDisplay.displayResults(targetImage.result);
    }
    
    // 更新侧边栏状态
    updateSidebarControls();
    updateSidebarThumbnails();
    
    // 触发导航事件
    document.dispatchEvent(new CustomEvent('imageNavigated', {
        detail: {
            index: currentImageIndex,
            image: targetImage,
            total: processedImages.length
        }
    }));
}

/**
 * 导航到上一张图片
 */
function navigatePrevious() {
    navigateToImage(currentImageIndex - 1);
}

/**
 * 导航到下一张图片
 */
function navigateNext() {
    navigateToImage(currentImageIndex + 1);
}

/**
 * 处理缩略图点击事件
 */
function handleThumbnailClick(imageId, index) {
    // 获取图片信息
    const batchImages = window.BatchImageProcessing ? window.BatchImageProcessing.getBatchImages() : [];
    const img = batchImages.find(item => item.id === imageId);

    if (!img) {
        console.warn(`⚠️ 未找到图片: ${imageId}`);
        return;
    }

    // 如果图片已处理完成，导航到该图片
    if (img.status === 'completed' && img.result) {
        navigateToImage(index);
    } else {
        console.log(`图片 ${img.file.name} 尚未处理完成，状态: ${img.status}`);
        // 可以选择显示提示信息
        if (window.UIUtils && window.UIUtils.showInfo) {
            window.UIUtils.showInfo(`图片 "${img.file.name}" 尚未处理完成`);
        }
    }
}

/**
 * 获取当前显示的图片ID
 */
function getCurrentDisplayedImageId() {
    // 尝试从图片导航系统获取当前图片ID
    if (window.ImageNavigation && window.ImageNavigation.getCurrentImageId) {
        return window.ImageNavigation.getCurrentImageId();
    }

    // 尝试从结果显示模块获取当前图片ID
    if (window.ResultDisplay && window.ResultDisplay.getCurrentImageId) {
        return window.ResultDisplay.getCurrentImageId();
    }

    // 如果有当前显示的图片，从processedImages中获取
    if (processedImages.length > 0 && currentImageIndex >= 0 && currentImageIndex < processedImages.length) {
        return processedImages[currentImageIndex].id;
    }

    return null;
}

/**
 * 清除图片缓存数据
 */
function clearImageCache(imageId) {
    console.log(`🗑️ 清除图片缓存: ${imageId}`);

    // 清除可能的浏览器缓存
    if (window.caches) {
        caches.keys().then(cacheNames => {
            cacheNames.forEach(cacheName => {
                caches.open(cacheName).then(cache => {
                    cache.keys().then(requests => {
                        requests.forEach(request => {
                            if (request.url.includes(imageId)) {
                                cache.delete(request);
                                console.log(`🗑️ 已删除缓存: ${request.url}`);
                            }
                        });
                    });
                });
            });
        });
    }
}

/**
 * 刷新当前图片显示
 */
async function refreshCurrentImageDisplay(imageId) {
    console.log(`🔄 刷新当前图片显示: ${imageId}`);

    try {
        // 获取最新的图片数据
        const imageItem = window.BatchImageProcessing ? window.BatchImageProcessing.findImageById(imageId) : null;

        if (!imageItem || !imageItem.result) {
            console.warn(`⚠️ 图片 ${imageId} 没有处理结果，无法刷新显示`);
            return;
        }

        // 强制刷新结果显示
        if (window.ResultDisplay && window.ResultDisplay.displayResults) {
            console.log(`🔄 强制刷新结果显示，新数据:`, imageItem.result);
            window.ResultDisplay.displayResults(imageItem.result);
        }

        // 更新图片导航系统
        if (window.ImageNavigation && window.ImageNavigation.updateNavigableImages) {
            window.ImageNavigation.updateNavigableImages();
        }

        console.log(`✅ 图片 ${imageId} 显示已刷新`);
    } catch (error) {
        console.error(`❌ 刷新图片显示失败: ${imageId}`, error);
    }
}

/**
 * 重新处理单张图片
 */
async function reprocessSingleImage(imageId, event) {
    // 阻止事件冒泡，避免触发缩略图点击
    if (event) {
        event.stopPropagation();
        event.preventDefault();
    }

    console.log(`🔄 开始重新处理图片: ${imageId}`);

    try {
        // 获取当前显示的图片ID，用于判断是否需要刷新主显示区域
        const currentDisplayedImageId = getCurrentDisplayedImageId();
        const isCurrentlyDisplayed = currentDisplayedImageId === imageId;

        // 调用批量图片处理模块的单张图片处理功能
        if (window.BatchImageProcessing && window.BatchImageProcessing.processSingleImage) {
            // 清除可能的缓存数据
            clearImageCache(imageId);

            await window.BatchImageProcessing.processSingleImage(imageId);

            // 处理完成后立即更新UI
            updateSidebarContent();

            // 如果当前正在显示这张图片，强制刷新主显示区域
            if (isCurrentlyDisplayed) {
                await refreshCurrentImageDisplay(imageId);
            }

            console.log(`✅ 图片 ${imageId} 重新处理完成，UI已更新`);
        } else {
            console.error('❌ 批量图片处理模块不可用');
            if (window.UIUtils && window.UIUtils.showError) {
                window.UIUtils.showError('图片处理功能不可用');
            }
        }
    } catch (error) {
        console.error(`❌ 重新处理图片失败: ${imageId}`, error);
        if (window.UIUtils && window.UIUtils.showError) {
            window.UIUtils.showError(`重新处理失败: ${error.message}`);
        }
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 监听图片导航事件
    document.addEventListener('imageNavigated', function(event) {
        console.log('🎯 检测到图片导航事件，更新侧边栏');
        updateSidebarContent();
    });
    
    // 监听批量处理完成事件
    document.addEventListener('batchProcessingComplete', function(event) {
        console.log('📊 检测到批量处理完成事件，刷新侧边栏');
        setTimeout(() => {
            updateSidebarContent();
        }, 500);
    });
    
    // 监听键盘快捷键
    document.addEventListener('keydown', function(event) {
        if (!navigationEnabled || !isVisible) return;
        
        switch (event.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                event.preventDefault();
                navigatePrevious();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                event.preventDefault();
                navigateNext();
                break;
            case 'Home':
                event.preventDefault();
                navigateToImage(0);
                break;
            case 'End':
                event.preventDefault();
                navigateToImage(processedImages.length - 1);
                break;
        }
    });
    
    console.log('✓ 侧边栏事件监听器已设置');
}

/**
 * 销毁侧边栏导航
 */
function destroySidebarNavigation() {
    if (sidebarElement) {
        sidebarElement.remove();
        sidebarElement = null;
    }

    // 不再调整主内容区域 - 保持原始位置
    // adjustMainContentLayout(false);

    // 重置状态
    isVisible = false;
    navigationEnabled = false;
    processedImages = [];
    currentImageIndex = 0;

    console.log('✓ 侧边栏导航已销毁');
}

// 导出侧边栏导航功能
window.SidebarNavigation = {
    initializeSidebarNavigation,
    updateSidebarContent,
    navigateToImage,
    navigatePrevious,
    navigateNext,
    toggleSidebar,
    handleThumbnailClick,
    reprocessSingleImage,
    destroySidebarNavigation
};

console.log('✓ 侧边栏导航模块已加载');
