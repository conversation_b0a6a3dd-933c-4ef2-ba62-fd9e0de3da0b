"""
通用精确匹配过滤器
第一层过滤：基于医学术语词典的O(1)复杂度快速匹配
实体类型无关的统一处理
支持动态配置
"""

from typing import List, Dict, Any, Optional
from .medical_dictionaries import is_medical_term, initialize_dynamic_dictionary
import logging

logger = logging.getLogger(__name__)


class ExactMatchFilter:
    """
    通用精确匹配过滤器

    特点：
    - 实体类型无关
    - O(1)复杂度查找
    - 基于完整医学术语词典
    - 统一处理所有类型的精确匹配需求
    - 支持动态配置
    """

    def __init__(self, config_manager=None):
        """
        初始化精确匹配过滤器

        Args:
            config_manager: 配置管理器，用于动态配置
        """
        self.config_manager = config_manager
        self._initialized = False
        self._initialize_dynamic_dictionary()

    def _initialize_dynamic_dictionary(self):
        """初始化动态词典"""
        if self.config_manager and not self._initialized:
            try:
                initialize_dynamic_dictionary(self.config_manager)
                self._initialized = True
                logger.info("精确匹配过滤器：动态词典初始化成功")
            except Exception as e:
                logger.warning(f"精确匹配过滤器：动态词典初始化失败，使用默认配置: {e}")
                self._initialized = False
    
    def filter_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对识别结果进行精确匹配过滤
        
        Args:
            results: 原始识别结果列表
            
        Returns:
            过滤后的识别结果列表
        """
        filtered_results = []
        
        for result in results:
            entity_text = result['text']
            entity_type = result['entity_type']
            
            # 特殊类型：完全保留，不进行任何过滤
            if self._should_preserve_completely(entity_type):
                filtered_results.append(result)
                continue
            
            # 精确匹配检查
            if not self._is_exact_match(entity_text):
                filtered_results.append(result)
            else:
                self._log_filtered_entity(entity_text, entity_type, "精确匹配医学术语")
        
        return filtered_results
    
    def _is_exact_match(self, entity_text: str) -> bool:
        """
        检查是否为医学术语的精确匹配
        
        Args:
            entity_text: 实体文本
            
        Returns:
            bool: True表示是医学术语，应该过滤
        """
        return is_medical_term(entity_text)
    
    def _should_preserve_completely(self, entity_type: str) -> bool:
        """
        判断是否为需要完全保留的实体类型
        
        Args:
            entity_type: 实体类型
            
        Returns:
            bool: True表示完全保留，不进行任何过滤
        """
        # 这些类型的实体通常都是真正的敏感信息，不应该被过滤
        preserve_types = {
            "DEVICE_SERIAL",        # 设备序列号
            "STRUCTURED_FIELD",     # 结构化字段
            # "MEDICAL_ORGANIZATION", # 医疗机构  公司xx 可能有误识别的情况 ，未来如果严重，需要新增一个医疗结构的过滤类型
            "CHINESE_ID",           # 身份证号
            "MOBILE_PHONE",         # 手机号
            "BANK_CARD",            # 银行卡
            # "WECHAT_ID",            # 微信号
            # "QQ_NUMBER",            # QQ号
            # "EMAIL_ADDRESS",        # 邮箱
            "IP_ADDRESS",           # IP地址
            "CREDIT_CARD",          # 信用卡
            "PASSPORT",             # 护照
            "MEDICAL_INSURANCE",    # 医保卡
            "LICENSE_PLATE",        # 车牌号
            "COMPLETE_ADDRESS",     # 完整地址
            "GPS_COORDINATE"        # GPS坐标
        }
        
        return entity_type in preserve_types
    
    def _log_filtered_entity(self, entity_text: str, entity_type: str, reason: str):
        """
        记录被过滤的实体（可选的日志功能）
        
        Args:
            entity_text: 实体文本
            entity_type: 实体类型
            reason: 过滤原因
        """
        # 可以根据需要启用或禁用日志
        print(f"[过滤层：精确匹配过滤] {entity_type}: '{entity_text}'")
        pass


def create_exact_match_filter(enable_stats: bool = False, config_manager=None) -> ExactMatchFilter:
    """
    创建精确匹配过滤器（强制不使用统计功能避免内存累积）

    Args:
        enable_stats: 忽略此参数，强制不使用统计功能
        config_manager: 配置管理器，用于动态配置

    Returns:
        精确匹配过滤器实例
    """
    # 强制返回基础过滤器，不使用任何统计功能，但支持动态配置
    return ExactMatchFilter(config_manager=config_manager)
