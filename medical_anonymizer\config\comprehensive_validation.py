"""
全面配置验证脚本

验证 recognizer_config.json 与系统原始配置的完全对接
"""

import json
import sys
from pathlib import Path
from typing import Dict, Set, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from medical_anonymizer.config import create_config_mapper
from medical_anonymizer.recognizers.patterns import UNIFIED_RECOGNIZER_CONFIGS


class ConfigurationValidator:
    """配置验证器"""
    
    def __init__(self):
        self.mapper = create_config_mapper()
        self.json_config = None
        self.mapped_configs = None
        self.validation_results = {
            "completeness": {"passed": False, "issues": []},
            "mapping": {"passed": False, "issues": []},
            "parameters": {"passed": False, "issues": []},
            "functionality": {"passed": False, "issues": []}
        }
    
    def load_configurations(self):
        """加载配置"""
        try:
            self.json_config = self.mapper.load_config()
            self.mapped_configs = self.mapper.map_all_recognizers()
            return True
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return False
    
    def validate_completeness(self):
        """1. 配置完整性验证"""
        print("🔍 1. 配置完整性验证")
        print("-" * 50)
        
        issues = []
        
        # 获取原始配置中的所有识别器
        original_recognizers = set(UNIFIED_RECOGNIZER_CONFIGS.keys())
        json_recognizers = set(self.mapped_configs.keys())
        
        print(f"📊 原始配置识别器数量: {len(original_recognizers)}")
        print(f"📊 JSON映射识别器数量: {len(json_recognizers)}")
        
        # 检查缺失的识别器
        missing_recognizers = original_recognizers - json_recognizers
        if missing_recognizers:
            issues.append(f"缺失识别器: {sorted(missing_recognizers)}")
            print(f"❌ 缺失识别器 ({len(missing_recognizers)}个):")
            for recognizer in sorted(missing_recognizers):
                print(f"   - {recognizer}")
        
        # 检查多余的识别器
        extra_recognizers = json_recognizers - original_recognizers
        if extra_recognizers:
            issues.append(f"多余识别器: {sorted(extra_recognizers)}")
            print(f"❌ 多余识别器 ({len(extra_recognizers)}个):")
            for recognizer in sorted(extra_recognizers):
                print(f"   - {recognizer}")
        
        # 检查总数
        if len(json_recognizers) != 29:
            issues.append(f"识别器总数不正确: {len(json_recognizers)} (应为29)")
            print(f"❌ 识别器总数: {len(json_recognizers)} (应为29)")
        
        if not issues:
            print("✅ 配置完整性验证通过")
            print(f"✅ 所有29个识别器都正确包含")
        
        self.validation_results["completeness"] = {
            "passed": len(issues) == 0,
            "issues": issues
        }
        
        return len(issues) == 0
    
    def validate_mapping_accuracy(self):
        """2. 配置映射准确性验证"""
        print("\n🔄 2. 配置映射准确性验证")
        print("-" * 50)
        
        issues = []
        
        # 检查每个配置项的sources映射
        recognizers = self.json_config.get("recognizers", {})
        all_sources = set()
        
        for category_name, category_config in recognizers.items():
            category_recognizers = category_config.get("recognizers", {})
            
            for config_name, config_data in category_recognizers.items():
                sources = config_data.get("sources", [])
                
                # 检查sources是否为空
                if not sources:
                    issues.append(f"{config_name}: sources数组为空")
                    continue
                
                # 检查每个source是否存在于原始配置中
                for source in sources:
                    all_sources.add(source)
                    if source not in UNIFIED_RECOGNIZER_CONFIGS:
                        issues.append(f"{config_name}: 无效的source '{source}'")
        
        # 检查是否所有原始识别器都被映射
        unmapped_recognizers = set(UNIFIED_RECOGNIZER_CONFIGS.keys()) - all_sources
        if unmapped_recognizers:
            issues.append(f"未映射的识别器: {sorted(unmapped_recognizers)}")
            print(f"❌ 未映射的识别器 ({len(unmapped_recognizers)}个):")
            for recognizer in sorted(unmapped_recognizers):
                print(f"   - {recognizer}")
        
        # 检查映射后的配置对象
        for recognizer_name, mapped_config in self.mapped_configs.items():
            if not hasattr(mapped_config, 'enabled'):
                issues.append(f"{recognizer_name}: 映射后缺少enabled属性")
            if not hasattr(mapped_config, 'description'):
                issues.append(f"{recognizer_name}: 映射后缺少description属性")
        
        if not issues:
            print("✅ 配置映射准确性验证通过")
            print(f"✅ 所有{len(all_sources)}个识别器正确映射")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        self.validation_results["mapping"] = {
            "passed": len(issues) == 0,
            "issues": issues
        }
        
        return len(issues) == 0
    
    def validate_parameter_consistency(self):
        """3. 参数对接验证"""
        print("\n⚙️ 3. 参数对接验证")
        print("-" * 50)
        
        issues = []
        
        # 检查每个识别器的参数一致性
        for recognizer_name, original_config in UNIFIED_RECOGNIZER_CONFIGS.items():
            if recognizer_name not in self.mapped_configs:
                continue
                
            mapped_config = self.mapped_configs[recognizer_name]
            
            # 检查enabled状态
            if mapped_config.enabled != original_config.enabled:
                issues.append(f"{recognizer_name}: enabled状态不一致 "
                            f"(JSON: {mapped_config.enabled}, 原始: {original_config.enabled})")
            
            # 检查上下文配置
            if original_config.context_config and mapped_config.context_config:
                orig_ctx = original_config.context_config
                mapped_ctx = mapped_config.context_config
                
                # 检查positive_keywords
                if orig_ctx.positive_keywords != mapped_ctx.positive_keywords:
                    issues.append(f"{recognizer_name}: positive_keywords不一致")
                
                # 检查negative_keywords
                if orig_ctx.negative_keywords != mapped_ctx.negative_keywords:
                    issues.append(f"{recognizer_name}: negative_keywords不一致")
            
            elif original_config.context_config != mapped_config.context_config:
                if original_config.context_config is None and mapped_config.context_config is not None:
                    issues.append(f"{recognizer_name}: 原始无上下文配置，但映射后有")
                elif original_config.context_config is not None and mapped_config.context_config is None:
                    issues.append(f"{recognizer_name}: 原始有上下文配置，但映射后无")
        
        # 特殊检查：OrganizationRecognizer和AgeRecognizer的positive_keywords
        special_cases = ["OrganizationRecognizer", "AgeRecognizer"]
        for recognizer_name in special_cases:
            if recognizer_name in self.mapped_configs:
                mapped_config = self.mapped_configs[recognizer_name]
                if (mapped_config.context_config and 
                    mapped_config.context_config.positive_keywords is not None):
                    issues.append(f"{recognizer_name}: positive_keywords应为None")
        
        if not issues:
            print("✅ 参数对接验证通过")
            print("✅ 所有参数与原始配置一致")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        self.validation_results["parameters"] = {
            "passed": len(issues) == 0,
            "issues": issues
        }
        
        return len(issues) == 0
    
    def validate_functionality(self):
        """4. 功能兼容性测试"""
        print("\n🧪 4. 功能兼容性测试")
        print("-" * 50)
        
        issues = []
        
        try:
            # 测试配置系统初始化
            from medical_anonymizer.config import initialize_config_system, get_config_manager
            
            if not initialize_config_system():
                issues.append("配置系统初始化失败")
            else:
                print("✅ 配置系统初始化成功")
            
            # 测试配置管理器
            manager = get_config_manager()
            
            # 测试获取识别器状态
            status = manager.get_recognizer_status()
            if not status:
                issues.append("无法获取识别器状态")
            else:
                print(f"✅ 获取识别器状态成功 ({len(status)}个配置项)")
            
            # 测试统计信息
            stats = manager.get_statistics()
            if not stats:
                issues.append("无法获取统计信息")
            else:
                print(f"✅ 获取统计信息成功")
                print(f"   - 总识别器: {stats.get('total_recognizers', 0)}")
                print(f"   - 启用识别器: {stats.get('enabled_recognizers', 0)}")
            
            # 测试动态控制功能
            test_recognizer = "chineseId"
            if manager.disable_recognizer(test_recognizer):
                if manager.enable_recognizer(test_recognizer):
                    print("✅ 动态启用/禁用功能正常")
                else:
                    issues.append("识别器启用失败")
            else:
                issues.append("识别器禁用失败")
            
        except Exception as e:
            issues.append(f"功能测试异常: {str(e)}")
        
        self.validation_results["functionality"] = {
            "passed": len(issues) == 0,
            "issues": issues
        }
        
        return len(issues) == 0
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("📋 配置验证报告")
        print("="*60)
        
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for result in self.validation_results.values() if result["passed"])
        
        print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
        
        for test_name, result in self.validation_results.items():
            status = "✅ 通过" if result["passed"] else "❌ 失败"
            test_display = {
                "completeness": "配置完整性",
                "mapping": "配置映射准确性", 
                "parameters": "参数对接",
                "functionality": "功能兼容性"
            }
            print(f"   {test_display[test_name]}: {status}")
            
            if not result["passed"] and result["issues"]:
                for issue in result["issues"]:
                    print(f"     - {issue}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有验证通过！")
            print("✅ JSON配置与系统原始配置完全对接")
            return True
        else:
            print(f"\n⚠️ {total_tests - passed_tests} 项验证失败")
            print("❌ 需要修复配置问题")
            return False


def main():
    """主验证函数"""
    print("🚀 医疗数据脱敏系统配置验证")
    print("="*60)
    
    validator = ConfigurationValidator()
    
    # 加载配置
    if not validator.load_configurations():
        return False
    
    # 执行验证
    results = []
    results.append(validator.validate_completeness())
    results.append(validator.validate_mapping_accuracy())
    results.append(validator.validate_parameter_consistency())
    results.append(validator.validate_functionality())
    
    # 生成报告
    success = validator.generate_report()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
