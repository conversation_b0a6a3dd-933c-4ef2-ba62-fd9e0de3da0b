# 医疗数据脱敏系统动态配置模块

## 📋 概述

本模块为医疗数据脱敏系统提供了完整的动态配置管理功能，实现了前端配置与后端实现的解耦，支持识别器的动态控制和配置热更新。系统支持JSON文件配置和MySQL数据库配置两种存储方式。

## 🏗️ 架构设计

### 核心组件

1. **配置存储层**
   - **JSON文件存储**: `recognizer_config.json` - 本地文件配置
   - **MySQL数据库存储**: `hipaa_deidentify_config` - 集中化数据库配置
   - 支持多项目配置管理和配置继承

2. **配置映射器** (`config_mapper.py`)
   - 将JSON配置转换为内部`RecognizerConfig`格式
   - 支持多源识别器映射
   - 提供配置验证和错误处理

3. **配置管理器** (`config_manager.py`)
   - 高级配置管理功能
   - 支持配置热更新和变更监听
   - 提供备份恢复和统计功能
   - 支持数据库和文件两种存储后端

4. **数据库配置系统**
   - MySQL JSON字段存储完整配置
   - 多项目支持和配置继承
   - 配置历史记录和审计跟踪

## 📊 配置结构

### 识别器配置格式

```json
{
  "recognizers": {
    "大类名称": {
      "name": "英文标识",
      "displayName": "中文名称",
      "recognizers": {
        "识别器英文名称": {
          "name": "识别器英文名称",
          "displayName": "识别器中文名称",
          "description": "识别器介绍说明",
          "category": "nlp_recognizer|contextual",
          "enabled": true,
          "positiveKeywords": ["正面关键词数组"],
          "negativeKeywords": ["负面关键词数组"],
          "sources": ["所属具体识别器数组"]
        }
      }
    }
  }
}
```

### 过滤器配置格式

```json
{
  "filters": {
    "过滤字典类型英文": {
      "name": "过滤字典类型英文",
      "displayName": "过滤字典类型中文",
      "description": "过滤字典介绍",
      "values": ["词典数组"]
    }
  }
}
```

## 🎯 识别器分类

### 1. 身份证件类 (identityDocuments)
- **各类证件**: 护照、驾照、军官证等
- **银行卡信息**: 银行卡号、信用卡号
- **医保社保卡**: 医保卡、社保卡号
- **国际银行账号**: IBAN码

### 2. 通信联系类 (communication)
- **联系电话**: 手机号、固定电话
- **微信号**: 微信账号识别
- **QQ号**: QQ号码识别
- **邮箱地址**: 电子邮箱

### 3. 位置时间类 (locationTime)
- **GPS坐标**: 经纬度坐标
- **地理位置**: 地名、地址
- **IP地址**: 网络地址
- **日期时间**: 时间信息

### 4. 医疗信息类 (medicalInformation)
- **医疗编号**: 病案号、检验号等
- **医疗机构**: 医院、CRO等机构名称

### 5. 隐私个人类 (privacyPersonal)
- **人名**: 中文人名
- **性别**: 性别信息
- **年龄**: 年龄信息
- **民族信息**: 民族、国籍
