"""
动态配置映射器

本模块负责将JSON配置格式转换为系统内部使用的RecognizerConfig格式，
实现前端配置与后端实现的解耦。

主要功能：
1. JSON配置加载和验证
2. 配置格式转换和映射
3. 动态配置更新支持
4. 向后兼容性保证
"""

import json
from typing import Dict, Any, Optional
from pathlib import Path
import logging

# 导入现有的配置类
from medical_anonymizer.recognizers.patterns import (
    RecognizerConfig, 
    ContextConfig, 
    ValidationLevel,
    UNIFIED_RECOGNIZER_CONFIGS
)

logger = logging.getLogger(__name__)


class ConfigMapper:
    """配置映射器类"""
    
    def __init__(self, config_file_path: Optional[str] = None):
        """
        初始化配置映射器
        
        Args:
            config_file_path: JSON配置文件路径，默认使用内置配置文件
        """
        if config_file_path is None:
            # 使用默认配置文件路径
            current_dir = Path(__file__).parent
            config_file_path = current_dir / "recognizer_config.json"
        
        self.config_file_path = Path(config_file_path)
        self.json_config = None
        self.mapped_configs = {}
        
        # 验证级别映射
        self.validation_level_mapping = {
            "STRICT": ValidationLevel.STRICT,
            "MODERATE": ValidationLevel.MODERATE,
            "LENIENT": ValidationLevel.LENIENT
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载JSON配置文件
        
        Returns:
            加载的配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            json.JSONDecodeError: JSON格式错误
        """
        try:
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                self.json_config = json.load(f)
            
            logger.info(f"成功加载配置文件: {self.config_file_path}")
            return self.json_config
            
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {self.config_file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON配置文件格式错误: {e}")
            raise
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置文件格式

        Args:
            config: 配置字典

        Returns:
            验证是否通过
        """
        required_keys = ["recognizers"]

        for key in required_keys:
            if key not in config:
                logger.error(f"配置文件缺少必需字段: {key}")
                return False

        # 验证识别器配置结构
        recognizers = config.get("recognizers", {})
        if not isinstance(recognizers, dict):
            logger.error("recognizers字段必须是字典类型")
            return False

        # 验证每个类别的识别器配置
        for category_name, category_config in recognizers.items():
            if not isinstance(category_config, dict):
                logger.error(f"类别配置 {category_name} 必须是字典类型")
                return False

            if "recognizers" not in category_config:
                logger.error(f"类别配置 {category_name} 缺少recognizers字段")
                return False

        logger.info("配置文件验证通过")
        return True
    
    def map_context_config(self, recognizer_config: Dict[str, Any]) -> Optional[ContextConfig]:
        """
        映射上下文配置

        Args:
            recognizer_config: 识别器配置字典

        Returns:
            ContextConfig对象或None
        """
        # 只有上下文识别器才需要上下文配置
        if recognizer_config.get("category") != "contextual":
            return None

        # 获取关键词列表
        positive_keywords = recognizer_config.get("positiveKeywords", [])
        negative_keywords = recognizer_config.get("negativeKeywords", [])

        # 保持None值不变，只处理其他情况
        if positive_keywords is None:
            positive_keywords = None
        elif not isinstance(positive_keywords, list):
            positive_keywords = []

        if negative_keywords is None:
            negative_keywords = []
        elif not isinstance(negative_keywords, list):
            negative_keywords = []

        # 使用默认配置参数
        return ContextConfig(
            validation_level=ValidationLevel.STRICT,
            positive_keywords=positive_keywords,
            negative_keywords=negative_keywords,
            window_size=25,
            min_confidence=0.85,
            require_exact_match=True
        )
    
    def map_recognizer_config(self, recognizer_config: Dict[str, Any]) -> Dict[str, RecognizerConfig]:
        """
        映射单个识别器配置到多个源识别器

        Args:
            recognizer_config: 识别器配置字典

        Returns:
            源识别器名称到RecognizerConfig对象的映射字典
        """
        # 基本配置
        description = recognizer_config.get("description", "")
        enabled = recognizer_config.get("enabled", True)
        category = recognizer_config.get("category", "nlp_recognizer")
        sources = recognizer_config.get("sources", [])

        # 判断是否为NLP识别器
        is_nlp_based = category == "nlp_recognizer"

        # 映射上下文配置
        context_config = self.map_context_config(recognizer_config)

        # 为每个源识别器创建配置
        result = {}
        for source in sources:
            # 从现有配置中获取默认值
            existing_config = UNIFIED_RECOGNIZER_CONFIGS.get(source)
            if existing_config:
                # 使用现有配置的regex和score
                regex = existing_config.regex
                score = existing_config.score
            else:
                # 使用默认值
                regex = None
                score = 0.85

            result[source] = RecognizerConfig(
                regex=regex,
                score=score,
                description=description,
                enabled=enabled,
                context_config=context_config,
                is_nlp_based=is_nlp_based
            )

        return result
    
    def map_all_recognizers(self) -> Dict[str, RecognizerConfig]:
        """
        映射所有识别器配置

        Returns:
            映射后的识别器配置字典
        """
        if self.json_config is None:
            raise ValueError("请先加载配置文件")

        mapped_configs = {}
        recognizers = self.json_config.get("recognizers", {})

        # 遍历所有类别
        for category_config in recognizers.values():
            category_recognizers = category_config.get("recognizers", {})

            # 遍历类别中的所有识别器
            for recognizer_name, recognizer_config in category_recognizers.items():
                try:
                    # 映射到多个源识别器
                    source_configs = self.map_recognizer_config(recognizer_config)

                    # 将源识别器配置添加到结果中
                    for source_name, config in source_configs.items():
                        mapped_configs[source_name] = config
                        logger.debug(f"成功映射源识别器: {source_name}")

                except Exception as e:
                    logger.error(f"映射识别器 {recognizer_name} 失败: {e}")
                    continue

        self.mapped_configs = mapped_configs
        logger.info(f"成功映射 {len(mapped_configs)} 个识别器配置")

        return mapped_configs
    
    def update_unified_configs(self, mapped_configs: Optional[Dict[str, RecognizerConfig]] = None):
        """
        更新统一配置字典
        
        Args:
            mapped_configs: 映射后的配置字典，如果为None则使用内部映射结果
        """
        if mapped_configs is None:
            mapped_configs = self.mapped_configs
        
        if not mapped_configs:
            logger.warning("没有可用的映射配置")
            return
        
        # 更新全局配置字典
        UNIFIED_RECOGNIZER_CONFIGS.clear()
        UNIFIED_RECOGNIZER_CONFIGS.update(mapped_configs)
        
        logger.info(f"已更新统一配置字典，包含 {len(mapped_configs)} 个识别器")
    
    def get_filter_config(self) -> Dict[str, Any]:
        """
        获取过滤器配置
        
        Returns:
            过滤器配置字典
        """
        if self.json_config is None:
            raise ValueError("请先加载配置文件")
        
        return self.json_config.get("filters", {})
    
    def reload_config(self):
        """重新加载配置文件并更新系统配置"""
        try:
            # 重新加载配置文件
            config = self.load_config()
            
            # 验证配置
            if not self.validate_config(config):
                logger.error("配置验证失败，保持原有配置")
                return False
            
            # 映射配置
            mapped_configs = self.map_all_recognizers()
            
            # 更新系统配置
            self.update_unified_configs(mapped_configs)
            
            logger.info("配置重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False


def create_config_mapper(config_file_path: Optional[str] = None) -> ConfigMapper:
    """
    创建配置映射器实例
    
    Args:
        config_file_path: 配置文件路径
        
    Returns:
        ConfigMapper实例
    """
    return ConfigMapper(config_file_path)


def load_and_apply_config(config_file_path: Optional[str] = None) -> bool:
    """
    加载并应用配置文件
    
    Args:
        config_file_path: 配置文件路径
        
    Returns:
        是否成功应用配置
    """
    try:
        mapper = create_config_mapper(config_file_path)
        
        # 加载配置
        config = mapper.load_config()
        
        # 验证配置
        if not mapper.validate_config(config):
            return False
        
        # 映射并应用配置
        mapped_configs = mapper.map_all_recognizers()
        mapper.update_unified_configs(mapped_configs)
        
        return True
        
    except Exception as e:
        logger.error(f"加载和应用配置失败: {e}")
        return False
