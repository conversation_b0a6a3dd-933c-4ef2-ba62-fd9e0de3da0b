## 医疗脱敏系统配置抽取方案设计

### **📋 配置结构总览**

整个配置为一个JSON对象，包含两个主要部分：
1. **recognizers**: 识别器配置
2. **filters**: 过滤器配置

```json
{
  "recognizers":{
    "大类名称": {
     "name": "英文标识",
     "displayName": "中文名称",
      "recognizers": {
        "识别器英文名称": {
          "name": "识别器英文名称",
          "displayName": "识别器中文名称", 
          "description": "识别器介绍说明",
          "category": "nlp_recognizer|contextual",
          "enabled": true,
          "positiveKeywords": ["正面关键词数组"],
          "negativeKeywords": ["负面关键词数组"],
          "sources": ["所属具体识别器数组"]
        }
        ...
   }
    ...
  }
  }
    ,
  "filters": {
    "过滤字典类型英文": {
      "name": "过滤字典类型英文",
      "displayName": "过滤字典类型中文", 
      "description": "过滤字典介绍",
      "values": ["词典数组"]
    }
    ...
  }
}
```

---

### **🔧 识别器配置详细设计**

#### **配置字段说明**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| **name** | string | ✅ | 识别器英文名称，作为唯一标识 |
| **displayName** | string | ✅ | 识别器中文显示名称 |
| **description** | string | ✅ | 识别器功能介绍说明 |
| **category** | enum | ✅ | 识别器类别：`nlp_recognizer` 或 `contextual` |
| **enabled** | boolean | ✅ | 识别器开关状态 |
| **positiveKeywords** | array | ⚪ | 上下文正面关键词数组 |
| **negativeKeywords** | array | ⚪ | 上下文负面关键词数组 |
| **sources** | array | ✅ | 所属的具体识别器类名数组 |

#### **识别器类别说明**
- **nlp_recognizer**: NLP层识别器、规则识别器层，仅使用enabled参数
- **contextual**: 上下文识别器，使用完整配置参数

---

### **📚 完整识别器配置清单**

#### **🎯 A. 身份证件类识别器**

```json
{
  "chineseId": {
    "name": "chineseId",
    "displayName": "中国身份证",
    "description": "识别18位中国身份证号码，支持校验位验证",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["ChineseIDRecognizer"]
  },
  "certificate": {
    "name": "certificate", 
    "displayName": "各类证件",
    "description": "识别护照、驾照、军官证等各类证件号码",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["护照", "驾照", "军官证", "工作证", "居住证", "证件"],
    "negativeKeywords": ["编号", "序列号", "病案号", "检验号"],
    "sources": ["CertificateRecognizer"]
  },
  "bankCard": {
    "name": "bankCard",
    "displayName": "银行卡号",
    "description": "识别16-19位银行卡号码",
    "category": "contextual", 
    "enabled": true,
    "positiveKeywords": ["银行卡", "卡号", "账户", "银行账户", "储蓄卡"],
    "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号"],
    "sources": ["BankCardRecognizer","CREDIT_CARD"]
  },
  "medicalInsurance": {
    "name": "medicalInsurance",
    "displayName": "医保卡号", 
    "description": "识别医保社保卡号（15-18位数字）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["医保卡", "社保卡", "医保号", "社保号", "医疗保险"],
    "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号"],
    "sources": ["MedicalInsuranceRecognizer"]
  },
  
}
```

#### **📞 B. 通信联系类识别器**

```json
{
  "phoneNumber": {
    "name": "phoneNumber",
    "displayName": "电话号码",
    "description": "综合电话号码识别（手机号+固话+通用识别）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["手机", "电话", "联系电话", "移动电话", "固话", "座机"],
    "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号"],
    "sources": ["MobilePhoneRecognizer", "Presidio内置PHONE_NUMBER具体名称待确认"]
  },
  # 固定电话
    "": {
    "name": "",
    "sources": ["LandlinePhoneRecognizer"]
  },
  "wechatId": {
    "name": "wechatId",
    "displayName": "微信号",
    "description": "识别微信号（6-20位字母数字组合）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["微信", "WeChat", "微信号", "微信账号", "wechat", "wx"],
    "negativeKeywords": ["用户名", "编号", "代码", "系统", "数据库", "序列"],
    "sources": ["WeChatRecognizer"]
  },
  "qqNumber": {
    "name": "qqNumber",
    "displayName": "QQ号码",
    "description": "识别QQ号码（5-11位数字）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["QQ", "QQ号", "qq", "QQ号码", "QQ账号"],
    "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号"],
    "sources": ["QQNumberRecognizer"]
  },
  "emailAddress": {
    "name": "emailAddress",
    "displayName": "邮箱地址",
    "description": "识别电子邮箱地址",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["EMAIL_ADDRESS"]
  }
}
```

#### **🏥 C. 医疗信息类识别器**

```json
{
  "medicalNumber": {
    "name": "medicalNumber",
    "displayName": "医疗编号",
    "description": "识别病案号、检验号等各类医疗编号",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["病案号", "病历号", "医疗记录", "检验编号", "标本编号", "编号"],
    "negativeKeywords": ["设备编号", "序列号", "年", "月", "日", "岁"],
    "sources": ["MedicalNumberRecognizer"]
  },
  "structuredField": {
    "name": "structuredField",
    "displayName": "结构化字段",
    "description": "识别'字段:值'格式的结构化敏感信息",
    "category": "nlp_recognizer",
    "enabled": false,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["StructuredFieldRecognizer"]
  },
  "medicalOrganization": {
    "name": "medicalOrganization",
    "displayName": "医疗机构",
    "description": "识别医疗机构名称（医院、CRO、研究院等）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["申办方", "CRO", "研究中心", "医疗机构", "医院", "公司"],
    "negativeKeywords": ["患者", "病人", "就诊", "住院", "门诊"],
    "sources": ["OrganizationRecognizer", "ORG"]
  },
  "sponsor": {
    "name": "sponsor",
    "displayName": "申办方",
    "description": "识别临床试验申办方名称",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["SponsorRecognizer"]
  }
}
```

#### **📍 D. 位置时间类识别器**

```json
{
  "completeAddress": {
    "name": "completeAddress",
    "displayName": "完整地址",
    "description": "识别完整地址（省市区+详细地址）",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["CompleteAddressRecognizer"]
  },
  "gpsCoordinate": {
    "name": "gpsCoordinate", 
    "displayName": "GPS坐标",
    "description": "识别GPS坐标（经纬度格式）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["坐标", "经纬度", "GPS", "位置", "地理位置", "定位"],
    "negativeKeywords": ["数字", "编号", "代码", "序列", "价格", "金额"],
    "sources": ["GPSCoordinateRecognizer"]
  },
  "licensePlate": {
    "name": "licensePlate",
    "displayName": "车牌号码",
    "description": "识别中国车牌号码（支持各省份格式）",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["LicensePlateRecognizer"]
  },
  "location": {
    "name": "location",
    "displayName": "地理位置",
    "description": "识别地理位置信息（地名、位置、IP地址）",
    "category": "nlp_recognizer", 
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["GPE", "LOCATION", "IP_ADDRESS"]
  },
  "dateTime": {
    "name": "dateTime",
    "displayName": "时间日期",
    "description": "基于spaCy NLP模型识别各种时间日期格式（DATE和TIME统一映射为DATE_TIME）",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["spaCy DATE (映射为DATE_TIME)", "spaCy TIME (映射为DATE_TIME)"],
    "note": "DateTimeRecognizer已废弃，现使用spaCy内置识别器通过Presidio统一映射"
  }
}
```

#### **🔒 E. 隐私个人类识别器**

```json
{
  "person": {
    "name": "person",
    "displayName": "人名",
    "description": "识别人名（支持2-4字中文姓名）",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["PERSON"]
  },
  "gender": {
    "name": "gender",
    "displayName": "性别信息",
    "description": "识别医疗文本中的性别信息（男、女、男性、女性）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": ["性别", "患者", "病员", "病人"],
    "negativeKeywords": [],
    "sources": ["GenderRecognizer"]
  },
  "age": {
    "name": "age",
    "displayName": "年龄信息",
    "description": "识别医疗文本中的年龄信息（35岁、45周岁、3个月等）",
    "category": "contextual",
    "enabled": true,
    "positiveKeywords": null,
    "negativeKeywords": ["工龄", "教龄", "司龄", "党龄", "军龄", "学龄", "使用年限", "保质期", "有效期", "存储期", "保修期", "历史", "年代", "时期", "时代", "世纪", "年份", "建立", "成立", "创建", "开业", "营业", "运营", "价格", "金额", "费用", "收费", "成本", "预算", "数量", "重量", "长度", "高度", "面积", "体积"],
    "sources": ["AgeRecognizer"]
  },
  "ethnicity": {
    "name": "ethnicity",
    "displayName": "民族信息",
    "description": "识别56个民族信息",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["EthnicityRecognizer", "NRP"]
  },
  "privacyInfo": {
    "name": "privacyInfo",
    "displayName": "隐私信息",
    "description": "识别婚姻状况、宗教信仰等隐私敏感信息",
    "category": "nlp_recognizer",
    "enabled": true, 
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["PrivacyInfoRecognizer"]
  },
  "educationLevel": {
    "name": "educationLevel",
    "displayName": "学历学位",
    "description": "识别学历学位和医疗职位信息",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["EducationLevelRecognizer"]
  },
  "url": {
    "name": "url",
    "displayName": "网址链接",
    "description": "识别各种URL格式（HTTP/FTP/邮件协议等）",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["URLRecognizer"]
  },
  "ibanCode": {
    "name": "ibanCode",
    "displayName": "国际银行账号",
    "description": "识别IBAN国际银行账号",
    "category": "nlp_recognizer",
    "enabled": true,
    "positiveKeywords": [],
    "negativeKeywords": [],
    "sources": ["IBAN_CODE"]
  }
}
```



---

### **🔧 过滤器配置设计**

过滤器配置包含第一层精确匹配过滤字典，每个字段对应一个词典数组：

```json
{
  "filters": {
    "medicalTestItems": [
      "葡萄糖", "蛋白", "酮体", "胆红素", "白细胞", "红细胞",
      "血糖", "胆固醇", "甘油三酯", "肌酐", "尿素氮", "尿酸"
    ],
    "medicalTerms": [
      "心脏", "肝脏", "肾脏", "肺", "脾脏", "胰腺", "胃", "肠",
      "炎症", "感染", "肿瘤", "癌症", "良性", "恶性", "转移"
    ],
    "drugNames": [
      "奥布替尼", "利妥昔单抗", "来那度胺", "伊布替尼", "泽布替尼",
      "阿糖胞苷", "地西他滨", "维奈克拉", "高三尖杉酯碱"
    ],
    "diseaseNames": [
      "边缘区淋巴瘤", "弥漫大B细胞淋巴瘤", "滤泡性淋巴瘤",
      "急性髓系白血病", "慢性淋巴细胞白血病", "多发性骨髓瘤"
    ],
    "neurologicalSigns": [
      "Hoffmann征", "Babinski征", "Brudzinski征", "Kernig征",
      "Hoffmann", "Babinski", "Brudzinski", "Kernig"
    ],
    "englishMedicalTerms": [
      "Blood", "Urine", "Serum", "Plasma", "Cell", "Tissue",
      "PCR", "ELISA", "DNA", "RNA", "Analyzer", "Microscope"
    ],
    "medicalAbbreviations": [
      "SG", "GLU", "PRO", "PH", "KET", "UBG", "BLD", "LEU",
      "ALT", "AST", "ALP", "LDH", "CK", "PCT", "CRP", "ESR"
    ]
  }
}
```

### **📊 配置优势总结**

#### **✅ 技术优势**
1. **三层架构对应**: 配置分类准确对应实际的三层识别架构
2. **统一数据结构**: 简化配置复杂度，降低维护成本
3. **灵活适配**: 不同类型识别器共用结构，按需取用
4. **准确映射**: sources数组准确反映实际识别器类名和实体类型

#### **✅ 业务优势**
1. **分类清晰**: 按业务功能和技术架构分组，便于理解和管理
2. **配置简单**: 关键参数突出，降低配置门槛
3. **功能完整**: 覆盖所有现有识别器类型和过滤需求
4. **维护友好**: 配置与代码分离，支持热更新

#### **🔧 最新更新（v6.0）**
1. **新增识别器**: 添加了GenderRecognizer（性别识别器）和AgeRecognizer（年龄识别器）
2. **上下文增强**: 两个新识别器都采用严格的医疗上下文验证机制
3. **个人信息完善**: 进一步完善了医疗文本中个人敏感信息的识别覆盖
4. **系统兼容**: 新识别器完全兼容现有框架，无破坏性变更

#### **📈 系统统计（v6.0）**
- **总识别器数量**: 34个（新增2个）
- **规则识别器**: 9个
- **上下文识别器**: 14个（新增2个）
- **NLP识别器**: 8个
- **其他类型**: 3个

这种持续优化的配置设计确保了系统的完整性和可扩展性，为医疗数据脱敏提供了更全面的保护。
