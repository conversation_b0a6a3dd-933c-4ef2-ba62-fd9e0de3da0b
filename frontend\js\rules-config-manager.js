/**
 * 规则配置管理器模块
 * 负责规则配置的动态生成、事件处理和状态管理
 */

class RulesConfigManager {
    constructor() {
        this.rulesData = null;
        this.configContainer = null;
        this.initialized = false;
    }

    /**
     * 初始化规则配置管理器
     */
    async initialize() {
        try {
            console.log('初始化规则配置管理器...');

            // 获取配置容器
            this.configContainer = document.getElementById('rules-config');
            if (!this.configContainer) {
                console.error('未找到规则配置容器 #rules-config');
                return false;
            }

            // 生成规则配置面板
            await this.generateConfigPanel();

            // 绑定事件监听器
            this.bindEventListeners();

            this.initialized = true;
            console.log('✓ 规则配置管理器初始化完成');
            return true;

        } catch (error) {
            console.error('规则配置管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 生成规则配置面板
     */
    async generateConfigPanel() {
        try {
            // 显示加载状态
            this.configContainer.innerHTML = '<div class="loading-rules">正在加载规则配置...</div>';

            // 生成配置面板HTML
            const configHTML = await window.templateLoader.generateRulesConfigPanel();
            
            if (!configHTML) {
                throw new Error('生成规则配置面板失败');
            }

            // 更新容器内容
            this.configContainer.innerHTML = configHTML;

            // 加载规则数据用于状态管理
            this.rulesData = await window.templateLoader.loadData('rules-config.json');

            console.log('✓ 规则配置面板生成完成');

        } catch (error) {
            console.error('生成规则配置面板失败:', error);
            this.configContainer.innerHTML = '<div class="error-rules">规则配置加载失败，请刷新页面重试</div>';
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        if (!this.configContainer) return;

        // 分类展开/折叠事件
        this.configContainer.addEventListener('click', (e) => {
            if (e.target.closest('.expand-btn')) {
                this.handleCategoryToggle(e);
            } else if (e.target.closest('.category-switch input')) {
                this.handleCategorySwitch(e);
            } else if (e.target.closest('.rule-item input[type="checkbox"]')) {
                this.handleRuleToggle(e);
            } else if (e.target.closest('.add-tag-btn')) {
                this.handleAddKeyword(e);
            }
        });

        console.log('✓ 规则配置事件监听器已绑定');
    }

    /**
     * 处理分类展开/折叠
     */
    handleCategoryToggle(e) {
        e.preventDefault();
        e.stopPropagation();

        const expandBtn = e.target.closest('.expand-btn');
        const categoryId = expandBtn.dataset.category || 
                          expandBtn.closest('.category-header').dataset.category;
        
        if (!categoryId) return;

        const categoryContent = document.querySelector(`[data-category="${categoryId}"].category-content`);
        const icon = expandBtn.querySelector('i');

        if (categoryContent && icon) {
            const isCollapsed = categoryContent.classList.contains('collapsed');
            
            if (isCollapsed) {
                categoryContent.classList.remove('collapsed');
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            } else {
                categoryContent.classList.add('collapsed');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            }

            // 更新数据状态
            if (this.rulesData && this.rulesData.categories[categoryId]) {
                this.rulesData.categories[categoryId].collapsed = !isCollapsed;
            }

            console.log(`分类 ${categoryId} ${isCollapsed ? '展开' : '折叠'}`);
        }
    }

    /**
     * 处理分类开关切换
     */
    handleCategorySwitch(e) {
        const checkbox = e.target;
        const categoryId = checkbox.dataset.category;
        const isEnabled = checkbox.checked;

        if (!categoryId) return;

        // 更新该分类下所有规则的状态
        const categoryRules = document.querySelectorAll(`[data-category="${categoryId}"] .rule-item input[type="checkbox"]`);
        categoryRules.forEach(ruleCheckbox => {
            ruleCheckbox.checked = isEnabled;
            // 触发规则切换事件
            this.updateRuleState(ruleCheckbox.dataset.rule, isEnabled);
        });

        // 更新数据状态
        if (this.rulesData && this.rulesData.categories[categoryId]) {
            this.rulesData.categories[categoryId].enabled = isEnabled;
            this.rulesData.categories[categoryId].rules.forEach(rule => {
                rule.enabled = isEnabled;
            });
        }

        console.log(`分类 ${categoryId} ${isEnabled ? '启用' : '禁用'}`);
    }

    /**
     * 处理单个规则切换
     */
    handleRuleToggle(e) {
        const checkbox = e.target;
        const ruleId = checkbox.dataset.rule;
        const isEnabled = checkbox.checked;

        if (!ruleId) return;

        this.updateRuleState(ruleId, isEnabled);

        // 检查并更新分类开关状态
        this.updateCategorySwitchState(ruleId);

        console.log(`规则 ${ruleId} ${isEnabled ? '启用' : '禁用'}`);
    }

    /**
     * 更新规则状态
     */
    updateRuleState(ruleId, isEnabled) {
        if (!this.rulesData) return;

        // 在数据中查找并更新规则状态
        for (const category of Object.values(this.rulesData.categories)) {
            const rule = category.rules.find(r => r.id === ruleId);
            if (rule) {
                rule.enabled = isEnabled;
                break;
            }
        }
    }

    /**
     * 更新分类开关状态
     */
    updateCategorySwitchState(ruleId) {
        if (!this.rulesData) return;

        // 找到规则所属的分类
        let targetCategory = null;
        for (const category of Object.values(this.rulesData.categories)) {
            if (category.rules.some(r => r.id === ruleId)) {
                targetCategory = category;
                break;
            }
        }

        if (!targetCategory) return;

        // 检查该分类下所有规则的状态
        const allEnabled = targetCategory.rules.every(rule => rule.enabled);
        const categorySwitch = document.querySelector(`[data-category="${targetCategory.id}"].category-switch input`);
        
        if (categorySwitch) {
            categorySwitch.checked = allEnabled;
        }
    }

    /**
     * 处理添加关键词
     */
    handleAddKeyword(e) {
        e.preventDefault();
        e.stopPropagation();

        const button = e.target.closest('.add-tag-btn');
        const categoryId = button.dataset.category;

        // 简单的关键词添加功能
        const keyword = prompt('请输入要添加的关键词:');
        if (keyword && keyword.trim()) {
            this.addKeywordTag(button, keyword.trim());
            console.log(`为分类 ${categoryId} 添加关键词: ${keyword}`);
        }
    }

    /**
     * 添加关键词标签
     */
    addKeywordTag(addButton, keyword) {
        const keywordTag = document.createElement('span');
        keywordTag.className = 'keyword-tag';
        keywordTag.textContent = keyword;
        
        // 在添加按钮前插入新标签
        addButton.parentNode.insertBefore(keywordTag, addButton);
    }

    /**
     * 获取当前规则配置
     */
    getCurrentConfig() {
        return this.rulesData;
    }

    /**
     * 获取启用的规则列表
     */
    getEnabledRules() {
        if (!this.rulesData) return [];

        const enabledRules = [];
        for (const category of Object.values(this.rulesData.categories)) {
            const categoryEnabledRules = category.rules.filter(rule => rule.enabled);
            enabledRules.push(...categoryEnabledRules);
        }
        return enabledRules;
    }

    /**
     * 重新加载配置
     */
    async reload() {
        console.log('重新加载规则配置...');
        
        // 清除缓存
        window.templateLoader.clearCache();
        
        // 重新生成配置面板
        await this.generateConfigPanel();
        
        console.log('✓ 规则配置重新加载完成');
    }
}

// 创建全局实例
const rulesConfigManager = new RulesConfigManager();

// 导出到全局
window.RulesConfigManager = RulesConfigManager;
window.rulesConfigManager = rulesConfigManager;

console.log('✓ 规则配置管理器模块已加载');
