"""
医疗数据脱敏系统

基于Microsoft Presidio核心技术的中文医疗文本敏感信息检测统一入口系统。

本包提供简单、标准化的医疗数据脱敏接口，支持12大类敏感信息的识别。
"""

from .core import MedicalAnonymizer
from medical_anonymizer.recognizers.patterns import MEDICAL_PATTERNS
from .recognizers.pattern_matchers import (
    ChineseIDRecognizer,
    EthnicityRecognizer,
    EducationLevelRecognizer,
    PrivacyInfoRecognizer,
    # CompleteAddressRecognizer,
    URLRecognizer,
    StructuredFieldRecognizer,
    SponsorRecognizer,
)
from .recognizers.contextual_analyzers import (
    # 升级的上下文识别器
    MobilePhoneRecognizer,
    BankCardRecognizer,
    # 原有的上下文识别器
    WeChatRecognizer,
    CertificateRecognizer,
    # 从advanced_detectors迁移的识别器
    GPSCoordinateRecognizer,
    # CommunicationContentRecognizer,
    MedicalNumberRecognizer,
)

__version__ = "1.0.0"
__author__ = "张崇文"
__description__ = "基于Presidio的医疗数据脱敏统一入口系统"

# 支持的实体类型
# SUPPORTED_ENTITIES = [
#     "CHINESE_ID",        # 身份证
#     "MOBILE_PHONE",      # 手机号
#     "MEDICAL_RECORD_ID", # 病案号
#     "WECHAT_ID",         # 微信号
#     "BANK_CARD",         # 银行卡
#     "ETHNICITY",         # 民族
#     "MEDICAL_POSITION",  # 医疗职位
#     "MEDICAL_INSURANCE", # 医保卡
#     "BIOMETRIC_FEATURE", # 生物特征
#     "PRIVACY_INFO",      # 隐私信息
#     "MEDICAL_ORGANIZATION", # 医疗机构
#     "EDUCATION_LEVEL",   # 学历学位
#     "GPS_COORDINATE",    # GPS坐标
#     "VEHICLE_INFO",      # 车辆信息
#     "DEVICE_SERIAL",     # 设备序列号
#     "COMMUNICATION_CONTENT", # 通信内容
#     "COMPLETE_ADDRESS",  # 完整地址
#     "PERSON",            # 人名（spaCy识别）
#     "ORG",               # 机构名（spaCy识别）
#     "GPE",               # 地名（spaCy识别）
#     "LOCATION",          # 位置（spaCy识别）
#     "EMAIL_ADDRESS",     # 邮箱（Presidio内置）
#     "URL",               # 网址（Presidio内置）
#     "IP_ADDRESS",        # IP地址（Presidio内置）
#     # "DATE_TIME"          # 日期时间（Presidio内置）- 注释掉，时间不是敏感词
# ]

__all__ = [
    "MedicalAnonymizer",
    "MEDICAL_PATTERNS",
    # "SUPPORTED_ENTITIES",
    "ChineseIDRecognizer",
    "MobilePhoneRecognizer",
    "WeChatRecognizer",
    "BankCardRecognizer",
    "EthnicityRecognizer"
]
