/**
 * 结果显示模块
 * 处理脱敏结果的显示、标签页切换和UI更新
 */

// 结果显示模块 - 避免变量重复声明，直接使用模块方法

// 当前结果数据
let currentResults = null;
let currentOriginalText = '';
let currentImageId = null;

// 显示结果主函数
function showResults(results, originalText) {
    console.log('showResults 被调用');
    console.log('结果数据:', results);
    console.log('原文长度:', originalText ? originalText.length : 0);

    currentResults = results;
    currentOriginalText = originalText;
    currentImageId = results ? results.imageId : null;

    console.log(`📷 当前显示图片ID: ${currentImageId}`);

    // 获取DOM元素
    const elements = window.DOMUtils.elements;

    // 检查必要的DOM元素
    if (!elements.resultsSection) {
        console.error('结果区域元素不存在');
        return;
    }

    // 更新图片导航系统
    if (window.ImageNavigation) {
        window.ImageNavigation.updateNavigableImages();
    }

    // 显示脱敏后的文本
    if (elements.deidentifiedText) {
        elements.deidentifiedText.textContent = results.deidentified_text || '无脱敏文本';
        console.log('✓ 脱敏文本已显示');
    } else {
        console.warn('脱敏文本元素不存在');
    }

    // 显示高亮的原文
    if (originalText && results.sensitive_words) {
        displayHighlightedText(originalText, results.sensitive_words);
        console.log('✓ 高亮文本已显示');
    } else {
        console.warn('无法显示高亮文本：原文或敏感词为空');
    }

    // 显示敏感信息列表
    const sensitiveWords = results.sensitive_words || [];
    displaySensitiveInfoList(sensitiveWords);
    console.log(`✓ 敏感信息列表已显示，共 ${sensitiveWords.length} 项`);
    
    // 显示过滤信息详情
    const filteredEntities = results.filtered_entities || [];
    displayFilteredInfo(filteredEntities);
    console.log(`✓ 过滤信息已显示，共 ${filteredEntities.length} 项`);

    // 更新统计信息
    if (elements.processingTime) {
        elements.processingTime.textContent = Math.round((results.processing_time || 0) * 1000);
    }
    if (elements.entityCount) {
        elements.entityCount.textContent = results.total_entities || sensitiveWords.length;
    }

    // 更新标签页计数
    if (elements.detectedCount) {
        elements.detectedCount.textContent = sensitiveWords.length;
        console.log(`✓ 检测到的敏感信息计数已更新: ${sensitiveWords.length}`);
    }
    if (elements.filteredCount) {
        elements.filteredCount.textContent = filteredEntities.length;
        console.log(`✓ 过滤词汇计数已更新: ${filteredEntities.length}`);
    }

    // 确保脱敏结果卡片默认为折叠状态
    try {
        const deidentifiedContent = document.getElementById('deidentified-content');
        const deidentifiedToggle = document.querySelector('[data-target="deidentified-content"]');
        if (deidentifiedContent && deidentifiedToggle) {
            const toggleIcon = deidentifiedToggle.nextElementSibling?.querySelector('i');
            if (toggleIcon) {
                deidentifiedContent.classList.add('collapsed');
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-right');
            }
        }
    } catch (error) {
        console.warn('设置脱敏结果卡片折叠状态时出错:', error);
    }

    // 显示结果区域
    elements.resultsSection.style.display = 'block';
    console.log('✓ 结果区域已显示');

    // 显示悬浮导航栏
    const floatingNav = document.getElementById('floating-nav');
    if (floatingNav) {
        floatingNav.style.display = 'block';
        console.log('✓ 悬浮导航栏已显示');
    }

    // 移除自动滚动行为 - 保持用户当前的滚动位置
    // setTimeout(() => {
    //     elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
    //     console.log('✓ 已滚动到结果区域');
    // }, 100);
    
    console.log('🎉 showResults 完成，结果已全部显示');
}

// 显示高亮文本
function displayHighlightedText(text, entities) {
    const elements = window.DOMUtils.elements;
    if (!elements.highlightedText || !text || !entities) return;

    const highlightedHTML = window.DataFormatters.highlightSensitiveText(text, entities);
    elements.highlightedText.innerHTML = highlightedHTML;
}

// 显示敏感信息列表
function displaySensitiveInfoList(sensitiveWords) {
    const elements = window.DOMUtils.elements;
    if (!elements.sensitiveInfoList) return;

    if (!sensitiveWords || sensitiveWords.length === 0) {
        elements.sensitiveInfoList.innerHTML = '<div class="empty-state">未检测到敏感信息</div>';
        return;
    }

    // 生成表格HTML
    let html = `
        <div class="sensitive-table-container">
            <table class="sensitive-table">
                <thead>
                    <tr>
                        <th>敏感文本</th>
                        <th>检测器类型</th>
                        <th>信息类型</th>
                        <th>位置</th>
                    </tr>
                </thead>
                <tbody>
    `;

    sensitiveWords.forEach(entity => {
        const detectorInfo = window.DetectorMapping.getDetectorInfo(entity.entity_type);
        const entityType = window.DataFormatters.ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type;

        html += `
            <tr>
                <td>
                    <div class="sensitive-text-cell">${entity.text}</div>
                </td>
                <td>
                    <div class="detector-badge detector-${detectorInfo.type.toLowerCase()}">
                        <i class="${detectorInfo.icon}"></i>
                        <span class="detector-name">${detectorInfo.name}</span>
                    </div>
                </td>
                <td>
                    <div class="entity-type-badge">${entityType}</div>
                </td>
                <td>
                    <div class="position-info">${entity.start}-${entity.end}</div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    elements.sensitiveInfoList.innerHTML = html;
}

// 显示过滤信息详情
function displayFilteredInfo(filteredEntities) {
    const elements = window.DOMUtils.elements;
    if (!elements.filteredInfoList) return;
    
    if (!filteredEntities || filteredEntities.length === 0) {
        elements.filteredInfoList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-filter"></i>
                <p>暂无被过滤的词汇</p>
                <small>过滤器会自动识别并过滤掉误报的医学术语</small>
            </div>
        `;
        return;
    }
    
    // 按过滤器类型分组
    const groupedFiltered = window.DataFormatters.groupFilteredEntitiesByType(filteredEntities);
    
    let html = '';
    for (const [filterType, entities] of Object.entries(groupedFiltered)) {
        html += `
            <div class="filtered-group">
                <div class="filtered-group-header">
                    <h4>
                        <i class="fas fa-filter"></i>
                        ${window.DataFormatters.getFilterTypeDisplayName(filterType)}
                        <span class="count">(${entities.length})</span>
                    </h4>
                </div>
                <div class="filtered-group-content">
        `;
        
        entities.forEach(entity => {
            html += `
                <div class="filtered-item">
                    <div class="filtered-item-header">
                        <span class="filtered-entity">${entity.text}</span>
                        <span class="filter-type">${window.DataFormatters.getFilterTypeDisplayName(entity.filter_type)}</span>
                    </div>
                    <div class="filtered-details">
                        <span class="entity-type-badge">${window.DataFormatters.ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type}</span>
                        ${entity.context ? `<div class="filtered-context">上下文: "${entity.context}"</div>` : ''}
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    elements.filteredInfoList.innerHTML = html;
}

// 切换结果标签页
function switchResultTab(tabName) {
    console.log(`切换到结果标签页: ${tabName}`);

    const elements = window.DOMUtils.elements;

    // 更新标签按钮状态
    if (elements.resultTabBtns) {
        elements.resultTabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
    }

    // 更新标签内容显示
    if (elements.resultTabContents) {
        elements.resultTabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });
    }
}

// 查看图片详情
function viewImageDetail(imageId) {
    console.log(`查看图片详情: ${imageId}`);

    const imageItem = window.BatchImageProcessing.findImageById(imageId);
    if (!imageItem) {
        console.error(`未找到图片项: ${imageId}`);
        return;
    }

    if (!imageItem.result) {
        console.error(`图片 ${imageItem.file.name} 没有处理结果`);
        window.UIUtils.showError('该图片还没有处理结果，请先进行处理');
        return;
    }

    console.log(`显示图片 ${imageItem.file.name} 的处理结果:`, imageItem.result);

    // 设置当前图片到导航系统
    if (window.ImageNavigation) {
        window.ImageNavigation.setCurrentImage(imageId);
    }

    // 显示该图片的处理结果
    displayResults(imageItem.result);

    console.log('✓ 图片详情显示完成');
}

// 为了兼容性，添加displayResults别名
function displayResults(results) {
    console.log('displayResults 被调用，结果数据:', results);
    
    if (!results) {
        console.error('displayResults: 结果数据为空');
        return;
    }
    
    const originalText = results.originalText || '';
    console.log(`调用 showResults，原文长度: ${originalText.length}`);
    
    if (results.originalText) {
        showResults(results, results.originalText);
    } else {
        showResults(results, '');
    }
    
    console.log('✓ displayResults 完成');
}

// 隐藏结果
function hideResults() {
    const elements = window.DOMUtils.elements;
    if (elements.resultsSection) {
        elements.resultsSection.style.display = 'none';
    }

    const floatingNav = document.getElementById('floating-nav');
    if (floatingNav) {
        floatingNav.style.display = 'none';
    }

    console.log('结果区域已隐藏');
}

// 切换高亮显示
function toggleHighlight() {
    const elements = window.DOMUtils.elements;
    if (!elements.highlightedText) return;

    const highlights = elements.highlightedText.querySelectorAll('.highlight');
    highlights.forEach(highlight => {
        highlight.classList.toggle('hidden');
    });
}

// 导出结果
function exportResults() {
    if (!currentResults) {
        window.UIUtils.showError('没有可导出的结果');
        return;
    }
    
    const exportData = {
        timestamp: new Date().toISOString(),
        originalText: currentOriginalText,
        results: currentResults
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `deidentification-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('结果已导出');
}

// 获取当前显示的图片ID
function getCurrentImageId() {
    return currentImageId;
}

// 导出结果显示功能
window.ResultDisplay = {
    showResults,
    displayResults,
    hideResults,
    displayHighlightedText,
    displaySensitiveInfoList,
    displayFilteredInfo,
    switchResultTab,
    viewImageDetail,
    toggleHighlight,
    exportResults,
    getCurrentImageId
};

console.log('✓ 结果显示模块已加载');
