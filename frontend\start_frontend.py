#!/usr/bin/env python3
"""
HIPAA脱敏系统前端启动脚本

这个脚本用于启动前端演示页面的HTTP服务器。
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import argparse
from pathlib import Path


def check_backend_service(host='127.0.0.1', port=50505):
    """检查后端脱敏服务是否运行"""
    import urllib.request
    import urllib.error
    
    try:
        url = f"http://{host}:{port}/health"
        response = urllib.request.urlopen(url, timeout=5)
        if response.status == 200:
            print(f"✓ 后端脱敏服务运行正常: {url}")
            return True
    except (urllib.error.URLError, urllib.error.HTTPError, OSError):
        pass
    
    print(f"⚠️  警告: 无法连接到后端脱敏服务 ({host}:{port})")
    print("   请确保HIPAA脱敏API服务正在运行")
    print("   启动命令: cd ../api && python start_service.py")
    return False


def start_server(port=8080, host='0.0.0.0', open_browser=True):
    """启动HTTP服务器"""

    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查必要文件是否存在
    required_files = ['index.html', 'styles.css', 'script.js']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ 错误: 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("🚀 启动HIPAA脱敏系统前端演示页面")
    print("=" * 50)
    
    # 检查后端服务
    check_backend_service()
    
    print(f"\n📂 服务目录: {script_dir}")
    print(f"🌐 服务地址: {host}:{port}")
    print(f"🔗 本地访问: http://localhost:{port}")
    if host != '127.0.0.1' and host != 'localhost':
        print(f"🌍 外部访问: http://{host}:{port}")
    
    try:
        # 创建HTTP服务器
        handler = http.server.SimpleHTTPRequestHandler
        
        # 添加CORS头部支持
        class CORSRequestHandler(handler):
            def end_headers(self):
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                super().end_headers()
        
        with socketserver.TCPServer((host, port), CORSRequestHandler) as httpd:
            print(f"\n✅ 服务器启动成功!")
            print(f"   绑定地址: {host}:{port}")
            print(f"   本地访问: http://localhost:{port}")
            if host != '127.0.0.1' and host != 'localhost':
                print(f"   外部访问: http://{host}:{port}")
            print(f"   按 Ctrl+C 停止服务")
            
            # 自动打开浏览器
            if open_browser:
                print(f"\n🌐 正在打开浏览器...")
                webbrowser.open(f'http://localhost:{port}')
            
            print(f"\n📋 使用说明:")
            print(f"   1. 确保后端脱敏服务正在运行 (端口50505)")
            print(f"   2. 在页面中输入文本或上传图片进行脱敏演示")
            print(f"   3. 可以点击'加载示例'按钮快速体验功能")
            
            print(f"\n🔧 故障排除:")
            print(f"   - 如果无法访问，请检查端口{port}是否被占用")
            print(f"   - 如果脱敏功能异常，请检查后端API服务状态")
            print(f"   - 如果OCR功能异常，请检查OCR服务配置")
            
            print(f"\n" + "=" * 50)
            print(f"服务器正在运行中...")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n\n👋 服务器已停止")
        return True
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 错误: 端口 {port} 已被占用")
            print(f"   请尝试使用其他端口: python start_frontend.py --port 8081")
        else:
            print(f"❌ 错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="HIPAA脱敏系统前端演示页面启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_frontend.py                    # 使用默认配置启动(0.0.0.0:8080)
  python start_frontend.py --port 8081       # 使用指定端口启动
  python start_frontend.py --host 127.0.0.1  # 仅本地访问
  python start_frontend.py --no-browser      # 启动但不自动打开浏览器
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8080,
        help='HTTP服务器端口 (默认: 8080)'
    )

    parser.add_argument(
        '--host',
        type=str,
        default='0.0.0.0',
        help='HTTP服务器绑定地址 (默认: 0.0.0.0，允许外部访问)'
    )

    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    args = parser.parse_args()
    
    # 启动服务器
    success = start_server(
        port=args.port,
        host=args.host,
        open_browser=not args.no_browser
    )
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
