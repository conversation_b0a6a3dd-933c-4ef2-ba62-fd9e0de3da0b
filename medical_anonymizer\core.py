"""
医疗数据脱敏系统核心模块

本模块包含主要的MedicalAnonymizer类，为检测中文医疗文本中的敏感信息提供统一入口。
"""

from presidio_analyzer import AnalyzerEngine, PatternRecognizer
from presidio_analyzer.nlp_engine import NlpEngineProvider
from presidio_anonymizer import AnonymizerEngine
from presidio_anonymizer.entities import OperatorConfig
from typing import List, Dict, Any, Union
import logging

# 使用统一注册器模块，保持兼容性
from .recognizers.unified_registry import  get_all_recognizers
from .utils import (
    format_detection_results,
    validate_text_input,
    remove_overlapping_results,
    create_summary_statistics
)
from .filters import create_two_layer_filter, TwoLayerFilterConfig


class MedicalAnonymizer:
    """
    医疗数据脱敏系统 - 统一入口设计

    使用Microsoft Presidio作为核心技术，为检测中文医疗文本中的敏感信息提供简化的统一接口。

    本类提供单一入口方法detect_sensitive_info()，处理所有类型的医疗敏感信息检测。
    """

    def __init__(self,  enable_logging: bool = False, anonymization_strategy: str = "mask"):
        """
        初始化医疗脱敏器及所有必要组件

        参数:
            use_all_recognizers (bool): 是否使用所有可用识别器，
                                      或仅使用核心识别器（默认：False，仅核心）
            enable_logging (bool): 是否启用详细日志记录
            anonymization_strategy (str): 脱敏策略，可选值：
                                        - "replace": 替换为 [REDACTED]
                                        - "mask": 替换为 ****
                                        - "redact": 替换为 <REDACTED>
        """
        if enable_logging:
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(__name__)
        else:
            self.logger = None

        self.anonymization_strategy = anonymization_strategy

        # 初始化动态配置系统
        self._init_dynamic_config()

        # 初始化两层过滤器架构（传递配置管理器以支持动态配置）
        filter_config = TwoLayerFilterConfig(config_manager=self.config_manager)
        filter_config.enable_stats = enable_logging  # 根据日志设置启用统计
        filter_config.context_window = 20
        self.medical_filter = create_two_layer_filter(filter_config, config_manager=self.config_manager)

        self.analyzer = self._init_analyzer()
        self.anonymizer = self._init_anonymizer()

        if self.logger:
            recognizer_count = len(self.analyzer.registry.recognizers)
            self.logger.info(f"医疗脱敏器已初始化，包含 {recognizer_count} 个识别器，两层过滤器架构：启用，脱敏策略：{anonymization_strategy}")

    def _init_dynamic_config(self):
        """初始化动态配置系统"""
        try:
            from medical_anonymizer.config import initialize_config_system, get_config_manager

            # 初始化配置系统
            if not initialize_config_system():
                if self.logger:
                    self.logger.warning("动态配置系统初始化失败，将使用默认配置")
                self.config_manager = None
                self.dynamic_configs = None
                return

            # 获取配置管理器和动态配置
            self.config_manager = get_config_manager()
            self.dynamic_configs = self.config_manager.mapper.map_all_recognizers()

            if self.logger:
                self.logger.info(f"动态配置系统初始化成功，加载了 {len(self.dynamic_configs)} 个识别器配置")

        except Exception as e:
            if self.logger:
                self.logger.error(f"动态配置系统初始化失败: {e}")
            self.config_manager = None
            self.dynamic_configs = None

    def detect_sensitive_info(self, text: str) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
        """
        统一入口：检测医疗文本中的敏感信息

        这是系统唯一的公共接口，用于检测敏感信息。
        它处理所有类型的医疗敏感信息并返回标准化结果。

        参数:
            text (str): 要分析敏感信息的医疗文本

        返回:
                Dict[str, Any]: 包含敏感信息和脱敏文本的字典
                {
                    "entities": [
                    {
                        "text": "张三",           # 敏感文本内容
                        "entity_type": "PERSON", # 实体类型
                        "start": 2,              # 起始位置（字符索引）
                        "end": 4,                # 结束位置（字符索引）
                        "confidence": 0.95       # 置信度分数（0-1）
                    },
                    ...
                ],
                    "anonymized_text": "..."     # 脱敏后的文本
                }

        异常:
            ValueError: 如果输入文本无效
            RuntimeError: 如果分析失败
        """
        # 验证输入
        is_valid, error_message = validate_text_input(text)
        if not is_valid:
            raise ValueError(f"输入文本无效: {error_message}")

        try:
            if self.logger:
                self.logger.info(f"正在分析长度为 {len(text)} 的文本")

            # 使用Presidio分析文本
            try:
                results = self.analyzer.analyze(text=text, language="zh")
            except Exception as analyze_error:
                if self.logger:
                    self.logger.error(f"Presidio分析器错误: {type(analyze_error).__name__}: {str(analyze_error)}")
                    import traceback
                    self.logger.error(f"详细堆栈: {traceback.format_exc()}")

                    # 尝试找出有问题的识别器
                    self.logger.info("开始逐个测试识别器以找出问题源...")
                    self._test_individual_recognizers(text)

                raise analyze_error

            if self.logger:
                self.logger.info(f"发现 {len(results)} 个潜在实体")

            # 转换为标准化格式
            formatted_results = format_detection_results(results, text)

            # 应用实体类型映射（统一时间日期实体类型）
            # mapped_results = self._apply_entity_type_mapping(formatted_results)

            # 移除重叠结果（保留置信度更高的）
            deduplicated_results = remove_overlapping_results(formatted_results)

            # 应用两层过滤器架构
            filtered_entities = []
            if self.medical_filter:
                # 获取过滤前的实体列表
                before_filter = deduplicated_results.copy()
                final_results = self.medical_filter.filter_results(deduplicated_results, text)

                # 计算被过滤的实体
                final_texts = {entity['text'] for entity in final_results}
                filtered_entities = [
                    {
                        **entity,
                        'filter_type': 'specialized_rules',  # 默认过滤器类型
                        'context': text[max(0, entity['start']-20):entity['end']+20]  # 添加上下文
                    }
                    for entity in before_filter
                    if entity['text'] not in final_texts
                ]

                if self.logger:
                    filtered_count = len(deduplicated_results) - len(final_results)
                    self.logger.info(f"两层过滤器过滤了 {filtered_count} 个误识别实体")

                    # 输出详细的过滤统计信息
                    filter_stats = self.medical_filter.get_stats()
                    if filter_stats:
                        summary = filter_stats['summary']
                        self.logger.info(f"过滤统计 - 第一层: {summary['layer1_filtered']}, 第二层: {summary['layer2_filtered']}, 总过滤率: {summary['overall_filter_rate']:.2%}")
            else:
                final_results = deduplicated_results

            if self.logger:
                self.logger.info(f"最终结果: {len(final_results)} 个敏感实体")

            # 输出最终的检测结果
            print("检测结果:")
            for result in final_results:
                print(f"{result['text']} - {result['entity_type']} - {result['confidence']}")


            # 生成脱敏文本
            anonymized_text = self._anonymize_text(text, final_results)

            # if self.logger:
                # self.logger.info(f"已生成脱敏文本，长度: {len(anonymized_text)}")

            # 返回包含敏感信息、脱敏文本和过滤信息的字典
            return {
                "entities": final_results,
                "anonymized_text": anonymized_text,
                "filtered_entities": filtered_entities
            }

        except Exception as e:
            error_msg = f"文本分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _get_raw_results(self, text: str) -> List[Dict[str, Any]]:
        """
        获取原始检测结果（未经过滤）- 用于调试

        Args:
            text: 待检测的文本

        Returns:
            原始检测结果列表
        """
        if not validate_text_input(text):
            return []

        try:
            # 使用Presidio进行检测
            raw_results = self.analyzer.analyze(
                text=text,
                language='zh',
                entities=self.get_supported_entities()
            )

            # 转换为标准格式
            formatted_results = format_detection_results(raw_results, text)

            return formatted_results

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取原始结果失败: {str(e)}")
            return []

    def get_supported_entities(self) -> List[str]:
        """
        获取支持的实体类型列表

        返回:
            List[str]: 支持的实体类型名称列表
        """
        return [
            # 规则识别器实体类型（使用识别器类名）
            "ChineseIDRecognizer",        # 身份证
            "LicensePlateRecognizer",     # 车牌号
            "EthnicityRecognizer",        # 民族
            "EducationLevelRecognizer",   # 学历学位
            "PrivacyInfoRecognizer",      # 隐私信息
            "CompleteAddressRecognizer",  # 完整地址
            "URLRecognizer",              # 网址链接
            "StructuredFieldRecognizer",  # 结构化字段
            "SponsorRecognizer",          # 申办方名称

            # 上下文识别器实体类型（使用识别器类名）
            "MobilePhoneRecognizer",      # 手机号
            "LandlinePhoneRecognizer",    # 固话
            "MedicalInsuranceRecognizer", # 医保卡
            "BankCardRecognizer",         # 银行卡
            "WeChatRecognizer",           # 微信号
            "CertificateRecognizer",      # 各类证件
            "QQNumberRecognizer",         # QQ号
            "OrganizationRecognizer",     # 医疗机构
            "GPSCoordinateRecognizer",    # GPS坐标
            "MedicalNumberRecognizer",    # 医疗编号
            "GenderRecognizer",           # 性别识别器
            "AgeRecognizer",              # 年龄识别器

            # spaCy识别器实体类型
            "PERSON",            # 人名
            "LOCATION",          # 位置
            "NRP",               # 民族/国籍（spaCy）
            "DATE_TIME",         # 日期时间

            # Presidio内置实体类型
            "EMAIL_ADDRESS",     # 邮箱
            "IP_ADDRESS",        # IP地址
            "CREDIT_CARD",       # 信用卡
            "IBAN_CODE",         # 国际银行账号

            # 其他可能的实体类型
            "PII",               # 个人身份信息
            "FINANCIAL_DATA",    # 金融数据
            "HEALTH_DATA"        # 健康数据
        ]

    def get_detection_summary(self, text: str) -> Dict[str, Any]:
        """
        获取带统计信息的检测摘要（可选的便利方法）

        参数:
            text (str): 要分析的医疗文本

        返回:
            Dict[str, Any]: 包含摘要统计的检测结果
        """
        results = self.detect_sensitive_info(text)
        summary = create_summary_statistics(results)

        return {
            "entities": results,
            "summary": summary
        }

    def _init_analyzer(self) -> AnalyzerEngine:
        """
        Initialize Presidio analyzer with Chinese NLP support (internal method)

        Returns:
            AnalyzerEngine: Configured Presidio analyzer
        """
        try:
            # Configure Chinese NLP engine
            configuration = {
                "nlp_engine_name": "spacy",
                "models": [{"lang_code": "zh", "model_name": "zh_core_web_trf"}],
            }

            provider = NlpEngineProvider(nlp_configuration=configuration)
            nlp_engine = provider.create_engine()

            # Create analyzer with Chinese language support
            analyzer = AnalyzerEngine(
                nlp_engine=nlp_engine,
                supported_languages=["zh"]
            )

            # 移除冲突和有问题的内置识别器
            self._remove_conflicting_recognizers(analyzer)

            # Register medical-specific recognizers
            self._register_medical_recognizers(analyzer)

            # 过滤禁用的NLP识别器
            self._filter_disabled_nlp_recognizers(analyzer)

            # 配置时间日期实体类型映射
            # self._configure_datetime_entity_mapping(analyzer)

            # 调整识别器优先级
            self._adjust_recognizer_priority(analyzer)

            return analyzer

        except Exception as e:
            error_msg = f"Failed to initialize analyzer: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _init_anonymizer(self) -> AnonymizerEngine:
        """
        初始化Presidio匿名化引擎（内部方法）

        返回:
            AnonymizerEngine: 配置好的Presidio匿名化引擎
        """
        try:
            # 创建匿名化引擎
            anonymizer = AnonymizerEngine()

            if self.logger:
                self.logger.info("匿名化引擎初始化成功")

            return anonymizer

        except Exception as e:
            error_msg = f"Failed to initialize anonymizer: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _anonymize_text(self, text: str, entities: List[Dict[str, Any]]) -> str:
        """
        使用检测到的实体对文本进行脱敏处理（内部方法）

        参数:
            text (str): 原始文本
            entities (List[Dict[str, Any]]): 检测到的敏感实体列表

        返回:
            str: 脱敏后的文本
        """
        try:
            if not entities:
                return text

            # 将我们的实体格式转换为Presidio的RecognizerResult格式
            from presidio_analyzer import RecognizerResult

            presidio_results = []
            for entity in entities:
                result = RecognizerResult(
                    entity_type=entity['entity_type'],
                    start=entity['start'],
                    end=entity['end'],
                    score=entity['confidence']
                )
                presidio_results.append(result)

            # 根据脱敏策略配置操作符
            operators = self._get_anonymization_operators()

            # 执行脱敏
            anonymized_result = self.anonymizer.anonymize(
                text=text,
                analyzer_results=presidio_results,
                operators=operators
            )

            return anonymized_result.text

        except Exception as e:
            error_msg = f"文本脱敏失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            # 如果脱敏失败，返回原文本但记录错误
            return text

    def _get_anonymization_operators(self) -> Dict[str, OperatorConfig]:
        """
        根据脱敏策略获取操作符配置（内部方法）

        返回:
            Dict[str, OperatorConfig]: 操作符配置字典
        """
        if self.anonymization_strategy == "mask":
            # 使用星号掩码
            default_operator = OperatorConfig("mask", {"chars_to_mask": 10, "masking_char": "*", "from_end": False})
        elif self.anonymization_strategy == "redact":
            # 使用尖括号标记
            default_operator = OperatorConfig("replace", {"new_value": "<REDACTED>"})
        else:  # default: "replace"
            # 使用方括号标记
            default_operator = OperatorConfig("replace", {"new_value": "[REDACTED]"})

        # 为所有实体类型使用相同的操作符
        operators = {}
        supported_entities = self.get_supported_entities()
        for entity_type in supported_entities:
            operators[entity_type] = default_operator

        return operators

    def _remove_conflicting_recognizers(self, analyzer: AnalyzerEngine) -> None:
        """
        移除与自定义识别器冲突或有问题的内置识别器（内部方法）

        参数:
            analyzer (AnalyzerEngine): Presidio分析器
        """
        try:
            # 需要移除的识别器名称及原因
            recognizers_to_remove_config = [
                # 冲突识别器（与自定义识别器功能重叠）
                ("DateRecognizer", "与身份证号冲突"),
                ("PhoneRecognizer", "与手机号冲突"),
                ("MedicalLicenseRecognizer", "与病案号冲突"),
                ("UrlRecognizer", "内置URL识别器存在误识别问题"),
                ("DeviceSerialRecognizer", "与增强医疗编号识别器冲突"),
                # 有问题的识别器（导致运行时错误）
                ("CryptoRecognizer", "在处理医疗文本中的细胞抗原标记时导致整数溢出错误"),
            ]

            # 保留时间日期识别器，但需要统一实体类型
            # DateTimeRecognizer 和 TimeRecognizer 将被保留并映射到 DATE_TIME 实体类型

            removed_count = 0
            # 移除识别器
            for recognizer_name, reason in recognizers_to_remove_config:
                recognizers_found = [
                    r for r in analyzer.registry.recognizers
                    if r.__class__.__name__ == recognizer_name
                ]

                for recognizer in recognizers_found:
                    analyzer.registry.recognizers.remove(recognizer)
                    removed_count += 1
                    if self.logger:
                        self.logger.info(f"已移除识别器: {recognizer_name} (原因: {reason})")

            if self.logger:
                remaining_count = len(analyzer.registry.recognizers)
                self.logger.info(f"识别器清理完成，移除了 {removed_count} 个识别器，剩余 {remaining_count} 个识别器")

        except Exception as e:
            if self.logger:
                self.logger.warning(f"移除识别器时出错: {str(e)}")

    def _adjust_recognizer_priority(self, analyzer: AnalyzerEngine) -> None:
        """
        调整识别器优先级，确保专用识别器优先于通用识别器（内部方法）

        参数:
            analyzer (AnalyzerEngine): Presidio分析器
        """
        try:
            # 高优先级识别器（精确匹配）
            high_priority = ["ChineseIDRecognizer", "MobilePhoneRecognizer", "BankCardRecognizer"]

            # 中优先级识别器（上下文增强）
            medium_priority = ["MedicalRecordRecognizer", "CertificateRecognizer", "MedicalInsuranceRecognizer"]

            # 低优先级识别器（可能冲突）
            low_priority = ["WeChatRecognizer"]

            # 重新排序识别器列表
            recognizers = analyzer.registry.recognizers.copy()
            analyzer.registry.recognizers.clear()

            # 按优先级添加识别器
            for priority_list in [high_priority, medium_priority, low_priority]:
                for recognizer in recognizers:
                    if recognizer.__class__.__name__ in priority_list:
                        analyzer.registry.recognizers.append(recognizer)

            # 添加剩余识别器
            for recognizer in recognizers:
                if recognizer not in analyzer.registry.recognizers:
                    analyzer.registry.recognizers.append(recognizer)

            if self.logger:
                self.logger.info(f"识别器优先级调整完成，共{len(analyzer.registry.recognizers)}个识别器")

        except Exception as e:
            if self.logger:
                self.logger.warning(f"调整识别器优先级时出错: {str(e)}")

    def _register_medical_recognizers(self, analyzer: AnalyzerEngine) -> None:
        """
        Register all medical recognizers with the analyzer (internal method)

        Args:
            analyzer (AnalyzerEngine): Presidio analyzer to register recognizers with
        """
        try:
            # 如果有动态配置，使用动态配置过滤的识别器
            if self.dynamic_configs:
                recognizers = self._get_enabled_recognizers()
            else:
                # 回退到静态配置
                recognizers = get_all_recognizers()

            # Register each recognizer
            for recognizer in recognizers:
                analyzer.registry.add_recognizer(recognizer)
                # if self.logger:
                    # self.logger.info(f"Registered recognizer: {recognizer.name}")

        except Exception as e:
            error_msg = f"Failed to register recognizers: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _get_enabled_recognizers(self):
        """根据动态配置获取启用的识别器"""
        try:
            from medical_anonymizer.recognizers.pattern_matchers import RULE_BASED_RECOGNIZERS
            from medical_anonymizer.recognizers.contextual_analyzers import CONTEXT_BASED_RECOGNIZERS

            enabled_recognizers = []

            # 获取启用的规则识别器
            for recognizer_class in RULE_BASED_RECOGNIZERS:
                class_name = recognizer_class.__name__
                if class_name in self.dynamic_configs:
                    config = self.dynamic_configs[class_name]
                    if config.enabled:
                        enabled_recognizers.append(recognizer_class())
                        if self.logger:
                            self.logger.debug(f"启用规则识别器: {class_name}")

            # 获取启用的上下文识别器
            for recognizer_class in CONTEXT_BASED_RECOGNIZERS:
                class_name = recognizer_class.__name__
                if class_name in self.dynamic_configs:
                    config = self.dynamic_configs[class_name]
                    if config.enabled:
                        enabled_recognizers.append(recognizer_class())
                        if self.logger:
                            self.logger.debug(f"启用上下文识别器: {class_name}")

            if self.logger:
                self.logger.info(f"根据动态配置启用了 {len(enabled_recognizers)} 个识别器")

            return enabled_recognizers

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取启用识别器失败: {e}")
            # 回退到静态配置
            return get_all_recognizers()

    def _filter_disabled_nlp_recognizers(self, analyzer: AnalyzerEngine) -> None:
        """
        过滤禁用的NLP识别器（内部方法）

        根据动态配置中的enabled配置，
        过滤禁用的spaCy实体类型和Presidio识别器。

        Args:
            analyzer (AnalyzerEngine): Presidio分析器
        """
        try:
            # 使用动态配置或回退到静态配置
            configs = self.dynamic_configs if self.dynamic_configs else {}
            if not configs:
                from .recognizers.patterns import UNIFIED_RECOGNIZER_CONFIGS
                configs = UNIFIED_RECOGNIZER_CONFIGS

            # 获取所有禁用的NLP实体类型
            disabled_nlp_entities = set()
            for entity_name, config in configs.items():
                if config.is_nlp_based and not config.enabled:
                    disabled_nlp_entities.add(entity_name)

            if not disabled_nlp_entities:
                if self.logger:
                    self.logger.info("所有NLP识别器都已启用，无需过滤")
                return

            # 过滤spaCy识别器的支持实体类型
            disabled_spacy_entities = disabled_nlp_entities & {'PERSON', 'NRP', 'LOCATION', 'DATE_TIME'}
            if disabled_spacy_entities:
                spacy_recognizers = [
                    r for r in analyzer.registry.recognizers
                    if r.__class__.__name__ == "SpacyRecognizer"
                ]

                for recognizer in spacy_recognizers:
                    if hasattr(recognizer, 'supported_entities'):
                        original_entities = recognizer.supported_entities.copy()
                        # 移除禁用的实体类型
                        recognizer.supported_entities = [
                            entity for entity in original_entities
                            if entity not in disabled_spacy_entities
                        ]

                        removed_entities = set(original_entities) - set(recognizer.supported_entities)
                        if removed_entities and self.logger:
                            self.logger.info(f"从spaCy识别器中移除了禁用的实体类型: {removed_entities}")

            # 过滤Presidio内置识别器
            presidio_entity_to_class_map = {
                'EMAIL_ADDRESS': 'EmailRecognizer',
                'IP_ADDRESS': 'IpRecognizer',
                'CREDIT_CARD': 'CreditCardRecognizer',
                'IBAN_CODE': 'IbanRecognizer'
            }

            recognizers_to_remove = []
            for entity_type in disabled_nlp_entities:
                if entity_type in presidio_entity_to_class_map:
                    class_name = presidio_entity_to_class_map[entity_type]
                    matching_recognizers = [
                        r for r in analyzer.registry.recognizers
                        if r.__class__.__name__ == class_name
                    ]
                    recognizers_to_remove.extend(matching_recognizers)

            # 移除禁用的Presidio识别器
            for recognizer in recognizers_to_remove:
                analyzer.registry.recognizers.remove(recognizer)
                if self.logger:
                    self.logger.info(f"移除了禁用的Presidio识别器: {recognizer.__class__.__name__}")

            if self.logger:
                total_disabled = len(disabled_nlp_entities)
                self.logger.info(f"NLP识别器过滤完成，禁用了 {total_disabled} 个NLP实体类型")

        except Exception as e:
            if self.logger:
                self.logger.warning(f"过滤NLP识别器时出错: {str(e)}")

    def add_custom_recognizer(self, recognizer: PatternRecognizer) -> None:
        """
        Add a custom recognizer to the analyzer (advanced usage)

        Args:
            recognizer (PatternRecognizer): Custom recognizer to add
        """
        try:
            self.analyzer.registry.add_recognizer(recognizer)
            if self.logger:
                self.logger.info(f"Added custom recognizer: {recognizer.name}")
        except Exception as e:
            error_msg = f"Failed to add custom recognizer: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def get_analyzer_info(self) -> Dict[str, Any]:
        """
        Get information about the analyzer configuration (debugging/info)

        Returns:
            Dict[str, Any]: Analyzer configuration information
        """
        recognizer_names = [r.name for r in self.analyzer.registry.recognizers]

        return {
            "supported_languages": self.analyzer.supported_languages,
            "recognizer_count": len(recognizer_names),
            "recognizer_names": recognizer_names,
            "nlp_engine": type(self.analyzer.nlp_engine).__name__,
        }



    def _test_individual_recognizers(self, text: str) -> None:
        """
        逐个测试识别器以找出有问题的识别器（内部方法）

        参数:
            text (str): 测试文本
        """
        if not self.logger:
            return

        self.logger.info(f"开始测试 {len(self.analyzer.registry.recognizers)} 个识别器...")

        problematic_recognizers = []
        working_recognizers = []

        for recognizer in self.analyzer.registry.recognizers:
            try:
                # 直接调用识别器的analyze方法
                temp_results = recognizer.analyze(text=text, entities=None)
                working_recognizers.append(recognizer.name)
                self.logger.debug(f"✅ {recognizer.name}: 正常 ({len(temp_results)} 个结果)")

            except Exception as e:
                problematic_recognizers.append((recognizer.name, type(e).__name__, str(e)))
                self.logger.error(f"❌ {recognizer.name}: {type(e).__name__}: {str(e)}")

        self.logger.info(f"测试完成: {len(working_recognizers)} 个正常, {len(problematic_recognizers)} 个有问题")

        if problematic_recognizers:
            self.logger.error("有问题的识别器详情:")
            for name, error_type, error_msg in problematic_recognizers:
                self.logger.error(f"  - {name}: {error_type} - {error_msg}")

        if working_recognizers:
            self.logger.info(f"正常工作的识别器: {', '.join(working_recognizers[:5])}{'...' if len(working_recognizers) > 5 else ''}")
