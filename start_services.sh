#!/bin/bash
#
# HIPAA医疗数据脱敏服务 - 容器内部启动脚本
#
# 这个脚本设计用于在已创建的Docker容器内部启动后端和前端服务。
# 不负责容器的创建和管理，仅负责服务的启动和管理。
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BACKEND_PORT=50505
FRONTEND_PORT=50506
WORKERS=5
BACKEND_ONLY=false
BACKEND_PID_FILE="/tmp/hipaa-backend.pid"
FRONTEND_PID_FILE="/tmp/hipaa-frontend.pid"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}HIPAA脱敏服务容器内启动脚本${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "用法: $0 [命令] [选项]"
    echo -e ""
    echo -e "${GREEN}主要命令:${NC}"
    echo -e "  ${YELLOW}start${NC}        - 启动完整服务（后端+前端）"
    echo -e "  ${YELLOW}start-backend${NC} - 仅启动后端服务"
    echo -e "  ${YELLOW}start-frontend${NC} - 仅启动前端服务"
    echo -e "  ${YELLOW}stop${NC}         - 停止所有服务"
    echo -e "  ${YELLOW}stop-backend${NC} - 停止后端服务"
    echo -e "  ${YELLOW}stop-frontend${NC} - 停止前端服务"
    echo -e "  ${YELLOW}restart${NC}      - 重启所有服务"
    echo -e "  ${YELLOW}status${NC}       - 查看服务状态"
    echo -e "  ${YELLOW}verify${NC}       - 验证服务是否正常运行"
    echo -e "  ${YELLOW}logs${NC}         - 查看服务日志"
    echo -e ""
    echo -e "${GREEN}配置选项:${NC}"
    echo -e "  --backend-port N    设置后端服务端口 (默认: ${BACKEND_PORT})"
    echo -e "  --frontend-port N   设置前端服务端口 (默认: ${FRONTEND_PORT})"
    echo -e "  --workers N         设置后端工作进程数 (默认: ${WORKERS})"
    echo -e "  --backend-only      仅启动后端服务"
    echo -e ""
    echo -e "${GREEN}示例:${NC}"
    echo -e "  $0                           # 启动完整服务"
    echo -e "  $0 start --backend-only      # 仅启动后端"
    echo -e "  $0 start --workers 8         # 使用8个工作进程"
    echo -e "  $0 status                    # 查看服务状态"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-port)
                BACKEND_PORT="$2"
                echo -e "${BLUE}设置后端端口: ${BACKEND_PORT}${NC}"
                shift 2
                ;;
            --frontend-port)
                FRONTEND_PORT="$2"
                echo -e "${BLUE}设置前端端口: ${FRONTEND_PORT}${NC}"
                shift 2
                ;;
            --workers)
                WORKERS="$2"
                echo -e "${BLUE}设置工作进程数: ${WORKERS}${NC}"
                shift 2
                ;;
            --backend-only)
                BACKEND_ONLY=true
                echo -e "${BLUE}模式: 仅启动后端${NC}"
                shift
                ;;
            *)
                # 忽略未知参数
                shift
                ;;
        esac
    done
}

# 验证后端服务
verify_backend_service() {
    echo -e "${YELLOW}🔍 验证后端服务...${NC}"

    local max_attempts=10
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo -e "${BLUE}尝试 $attempt/$max_attempts: 检查后端服务响应...${NC}"

        # 检查健康接口
        if command -v curl >/dev/null 2>&1; then
            if curl -s --connect-timeout 3 --max-time 5 "http://localhost:${BACKEND_PORT}/health" > /dev/null 2>&1; then
                echo -e "${GREEN}✓ 健康检查接口响应正常${NC}"

                # 进一步测试脱敏接口
                local test_result=$(curl -s --connect-timeout 3 --max-time 10 \
                    -X POST "http://localhost:${BACKEND_PORT}/deidentify" \
                    -H "Content-Type: application/json" \
                    -d '{"text": "测试文本"}' 2>/dev/null)

                if echo "$test_result" | grep -q "deidentified_text"; then
                    echo -e "${GREEN}✓ 脱敏接口测试通过${NC}"
                    return 0
                else
                    echo -e "${YELLOW}⚠️ 脱敏接口测试失败，但健康检查正常${NC}"
                    return 0
                fi
            fi
        else
            # 如果没有curl，只检查端口是否监听
            if command -v netstat >/dev/null 2>&1; then
                if netstat -tlnp 2>/dev/null | grep -q ":${BACKEND_PORT}.*LISTEN"; then
                    echo -e "${GREEN}✓ 后端端口 ${BACKEND_PORT} 正在监听${NC}"
                    return 0
                fi
            else
                echo -e "${YELLOW}⚠️ 无法验证服务（缺少curl和netstat）${NC}"
                return 0
            fi
        fi

        echo -e "${YELLOW}等待 2 秒后重试...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}❌ 后端服务验证失败（已尝试 $max_attempts 次）${NC}"
    return 1
}

# 验证前端服务
verify_frontend_service() {
    echo -e "${YELLOW}🔍 验证前端服务...${NC}"

    local max_attempts=6
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo -e "${BLUE}尝试 $attempt/$max_attempts: 检查前端服务响应...${NC}"

        # 检查前端HTTP服务
        if command -v curl >/dev/null 2>&1; then
            if curl -s --connect-timeout 3 --max-time 5 "http://localhost:${FRONTEND_PORT}/" > /dev/null 2>&1; then
                echo -e "${GREEN}✓ 前端HTTP服务响应正常${NC}"
                return 0
            fi
        else
            # 如果没有curl，只检查端口是否监听
            if command -v netstat >/dev/null 2>&1; then
                if netstat -tlnp 2>/dev/null | grep -q ":${FRONTEND_PORT}.*LISTEN"; then
                    echo -e "${GREEN}✓ 前端端口 ${FRONTEND_PORT} 正在监听${NC}"
                    return 0
                fi
            else
                echo -e "${YELLOW}⚠️ 无法验证前端服务（缺少curl和netstat）${NC}"
                return 0
            fi
        fi

        echo -e "${YELLOW}等待 2 秒后重试...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}❌ 前端服务验证失败（已尝试 $max_attempts 次）${NC}"
    return 1
}

# 检查Python环境
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python未安装${NC}"
        return 1
    fi
    echo -e "${BLUE}✓ 使用Python: $PYTHON_CMD${NC}"
    return 0
}

# 检查必要文件
check_files() {
    echo -e "${BLUE}🔍 检查必要文件...${NC}"
    
    # 检查后端文件
    if [ ! -f "api/main.py" ]; then
        echo -e "${RED}❌ 未找到后端主文件: api/main.py${NC}"
        return 1
    fi
    
    if [ ! -f "api/start_service.py" ]; then
        echo -e "${RED}❌ 未找到后端启动脚本: api/start_service.py${NC}"
        return 1
    fi
    
    # 检查前端文件
    if [[ "$BACKEND_ONLY" != "true" ]]; then
        if [ ! -d "frontend" ]; then
            echo -e "${RED}❌ 未找到前端目录: frontend${NC}"
            return 1
        fi
        
        if [ ! -f "frontend/start_frontend.py" ]; then
            echo -e "${RED}❌ 未找到前端启动脚本: frontend/start_frontend.py${NC}"
            return 1
        fi
    fi
    
    echo -e "${GREEN}✓ 文件检查通过${NC}"
    return 0
}

# 启动后端服务
start_backend() {
    echo -e "${BLUE}🚀 启动后端脱敏服务...${NC}"
    echo -e "${YELLOW}端口: ${BACKEND_PORT}${NC}"
    echo -e "${YELLOW}工作进程: ${WORKERS}${NC}"
    
    # 停止现有后端服务
    stop_backend_service
    
    # 切换到api目录
    cd api
    
    # 启动后端服务（后台运行）
    echo -e "${YELLOW}⏳ 正在启动后端服务...${NC}"
    nohup $PYTHON_CMD start_service.py \
        --host 0.0.0.0 \
        --port $BACKEND_PORT \
        --workers $WORKERS \
        --log-level INFO \
        > ../backend.log 2>&1 &
    
    BACKEND_PID=$!
    echo $BACKEND_PID > "$BACKEND_PID_FILE"
    
    cd ..
    
    # 等待后端服务启动
    echo -e "${YELLOW}⏳ 等待后端服务启动...${NC}"
    sleep 8
    
    # 检查后端服务是否启动成功
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${GREEN}✓ 后端进程启动成功 (PID: ${BACKEND_PID})${NC}"

        # 验证后端服务
        if verify_backend_service; then
            echo -e "${GREEN}✓ 后端服务验证通过${NC}"
            echo -e "${GREEN}服务地址: http://localhost:${BACKEND_PORT}${NC}"
            echo -e "${GREEN}API文档: http://localhost:${BACKEND_PORT}/docs${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️ 后端服务验证失败，但进程正在运行${NC}"
            echo -e "${YELLOW}可能需要更多时间启动，请稍后手动验证${NC}"
            return 0
        fi
    else
        echo -e "${RED}❌ 后端服务启动失败${NC}"
        echo -e "${YELLOW}查看日志: tail -f backend.log${NC}"
        return 1
    fi
}

# 启动前端服务
start_frontend() {
    echo -e "${BLUE}🌐 启动前端演示页面...${NC}"
    echo -e "${YELLOW}端口: ${FRONTEND_PORT}${NC}"
    
    # 停止现有前端服务
    stop_frontend_service
    
    # 切换到frontend目录
    cd frontend
    
    # 启动前端服务（后台运行）
    echo -e "${YELLOW}⏳ 正在启动前端服务...${NC}"
    nohup $PYTHON_CMD start_frontend.py \
        --port $FRONTEND_PORT \
        --host 0.0.0.0 \
        --no-browser \
        > ../frontend.log 2>&1 &
    
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$FRONTEND_PID_FILE"
    
    cd ..
    
    # 等待前端服务启动
    sleep 3
    
    # 检查前端服务是否启动成功
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${GREEN}✓ 前端进程启动成功 (PID: ${FRONTEND_PID})${NC}"

        # 验证前端服务
        if verify_frontend_service; then
            echo -e "${GREEN}✓ 前端服务验证通过${NC}"
            echo -e "${GREEN}前端地址: http://localhost:${FRONTEND_PORT}${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️ 前端服务验证失败，但进程正在运行${NC}"
            echo -e "${YELLOW}可能需要更多时间启动，请稍后手动验证${NC}"
            return 0
        fi
    else
        echo -e "${RED}❌ 前端服务启动失败${NC}"
        echo -e "${YELLOW}查看日志: tail -f frontend.log${NC}"
        return 1
    fi
}

# 停止后端服务
stop_backend_service() {
    if [ -f "$BACKEND_PID_FILE" ]; then
        BACKEND_PID=$(cat "$BACKEND_PID_FILE")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo -e "${YELLOW}⚠️ 停止现有后端服务 (PID: $BACKEND_PID)...${NC}"
            kill $BACKEND_PID 2>/dev/null || true
            sleep 3
            # 强制杀死如果还在运行
            if kill -0 $BACKEND_PID 2>/dev/null; then
                kill -9 $BACKEND_PID 2>/dev/null || true
            fi
        fi
        rm -f "$BACKEND_PID_FILE"
    fi
    
    # 额外检查：杀死所有可能的后端进程
    pkill -f "start_service.py" 2>/dev/null || true
    pkill -f "api.main:app" 2>/dev/null || true
    
    echo -e "${GREEN}✓ 后端服务已停止${NC}"
}

# 停止前端服务
stop_frontend_service() {
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo -e "${YELLOW}⚠️ 停止现有前端服务 (PID: $FRONTEND_PID)...${NC}"
            kill $FRONTEND_PID 2>/dev/null || true
            sleep 2
            # 强制杀死如果还在运行
            if kill -0 $FRONTEND_PID 2>/dev/null; then
                kill -9 $FRONTEND_PID 2>/dev/null || true
            fi
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi
    
    # 额外检查：杀死所有可能的前端进程
    pkill -f "start_frontend.py" 2>/dev/null || true
    
    echo -e "${GREEN}✓ 前端服务已停止${NC}"
}

# 启动完整服务
start_services() {
    echo -e "${BLUE}🚀 启动HIPAA脱敏服务${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    # 检查环境
    if ! check_python; then
        exit 1
    fi
    
    if ! check_files; then
        exit 1
    fi
    
    # 启动后端服务
    if ! start_backend; then
        echo -e "${RED}❌ 后端服务启动失败${NC}"
        exit 1
    fi
    
    # 根据配置决定是否启动前端
    if [[ "$BACKEND_ONLY" == "true" ]]; then
        echo -e "${BLUE}========================================${NC}"
        echo -e "${GREEN}🎉 后端服务启动成功！${NC}"
        echo -e "${GREEN}后端API: http://localhost:${BACKEND_PORT}${NC}"
        echo -e "${GREEN}API文档: http://localhost:${BACKEND_PORT}/docs${NC}"
        echo -e "${BLUE}========================================${NC}"
    else
        # 启动前端服务
        if start_frontend; then
            echo -e "${BLUE}========================================${NC}"
            echo -e "${GREEN}🎉 完整服务启动成功！${NC}"
            echo -e "${GREEN}后端API: http://localhost:${BACKEND_PORT}${NC}"
            echo -e "${GREEN}前端页面: http://localhost:${FRONTEND_PORT}${NC}"
            echo -e "${GREEN}API文档: http://localhost:${BACKEND_PORT}/docs${NC}"
            echo -e "${BLUE}========================================${NC}"
        else
            echo -e "${YELLOW}⚠️ 前端服务启动失败，但后端服务正常运行${NC}"
            echo -e "${GREEN}后端服务地址: http://localhost:${BACKEND_PORT}${NC}"
        fi
    fi
}

# 停止所有服务
stop_services() {
    echo -e "${BLUE}🛑 停止HIPAA脱敏服务${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    stop_frontend_service
    stop_backend_service
    
    echo -e "${GREEN}✓ 所有服务已停止${NC}"
}

# 重启服务
restart_services() {
    echo -e "${BLUE}🔄 重启HIPAA脱敏服务${NC}"
    stop_services
    sleep 2
    start_services
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}📊 服务状态信息${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    # 检查后端服务状态
    if [ -f "$BACKEND_PID_FILE" ]; then
        BACKEND_PID=$(cat "$BACKEND_PID_FILE")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo -e "${GREEN}✓ 后端服务运行中 (PID: $BACKEND_PID)${NC}"
            echo -e "  地址: http://localhost:${BACKEND_PORT}"
            echo -e "  API文档: http://localhost:${BACKEND_PORT}/docs"

            # 详细验证后端服务
            echo -e "${YELLOW}  正在验证服务状态...${NC}"
            if command -v curl >/dev/null 2>&1; then
                # 健康检查
                if curl -s --connect-timeout 2 --max-time 3 "http://localhost:${BACKEND_PORT}/health" > /dev/null 2>&1; then
                    echo -e "  健康检查: ${GREEN}✓ 正常${NC}"

                    # 脱敏接口测试
                    local test_result=$(curl -s --connect-timeout 2 --max-time 5 \
                        -X POST "http://localhost:${BACKEND_PORT}/deidentify" \
                        -H "Content-Type: application/json" \
                        -d '{"text": "测试"}' 2>/dev/null)

                    if echo "$test_result" | grep -q "deidentified_text"; then
                        echo -e "  脱敏接口: ${GREEN}✓ 正常${NC}"
                    else
                        echo -e "  脱敏接口: ${YELLOW}⚠️ 异常${NC}"
                    fi
                else
                    echo -e "  健康检查: ${RED}❌ 异常${NC}"
                fi
            else
                echo -e "  验证状态: ${YELLOW}⚠️ 无法验证（缺少curl）${NC}"
            fi
        else
            echo -e "${RED}❌ 后端服务未运行${NC}"
        fi
    else
        echo -e "${RED}❌ 后端服务未运行${NC}"
    fi
    
    echo ""
    
    # 检查前端服务状态
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo -e "${GREEN}✓ 前端服务运行中 (PID: $FRONTEND_PID)${NC}"
            echo -e "  地址: http://localhost:${FRONTEND_PORT}"

            # 验证前端服务
            echo -e "${YELLOW}  正在验证前端状态...${NC}"
            if command -v curl >/dev/null 2>&1; then
                if curl -s --connect-timeout 2 --max-time 3 "http://localhost:${FRONTEND_PORT}/" > /dev/null 2>&1; then
                    echo -e "  HTTP服务: ${GREEN}✓ 正常${NC}"
                else
                    echo -e "  HTTP服务: ${RED}❌ 异常${NC}"
                fi
            else
                echo -e "  验证状态: ${YELLOW}⚠️ 无法验证（缺少curl）${NC}"
            fi
        else
            echo -e "${RED}❌ 前端服务未运行${NC}"
        fi
    else
        echo -e "${RED}❌ 前端服务未运行${NC}"
    fi
}

# 验证所有服务
verify_services() {
    echo -e "${BLUE}🔍 验证HIPAA脱敏服务${NC}"
    echo -e "${BLUE}========================================${NC}"

    local backend_ok=false
    local frontend_ok=false

    # 验证后端服务
    echo -e "${YELLOW}📡 验证后端服务...${NC}"
    if [ -f "$BACKEND_PID_FILE" ]; then
        BACKEND_PID=$(cat "$BACKEND_PID_FILE")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo -e "${GREEN}✓ 后端进程运行中 (PID: $BACKEND_PID)${NC}"
            if verify_backend_service; then
                backend_ok=true
            fi
        else
            echo -e "${RED}❌ 后端进程未运行${NC}"
        fi
    else
        echo -e "${RED}❌ 后端服务未启动${NC}"
    fi

    echo ""

    # 验证前端服务
    echo -e "${YELLOW}🌐 验证前端服务...${NC}"
    if [ -f "$FRONTEND_PID_FILE" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo -e "${GREEN}✓ 前端进程运行中 (PID: $FRONTEND_PID)${NC}"
            if verify_frontend_service; then
                frontend_ok=true
            fi
        else
            echo -e "${RED}❌ 前端进程未运行${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 前端服务未启动（可能是仅后端模式）${NC}"
        frontend_ok=true  # 如果是仅后端模式，前端不验证也算正常
    fi

    echo ""
    echo -e "${BLUE}========================================${NC}"

    # 总结验证结果
    if [ "$backend_ok" = true ]; then
        echo -e "${GREEN}🎉 后端服务验证通过！${NC}"
        echo -e "${GREEN}  - API地址: http://localhost:${BACKEND_PORT}${NC}"
        echo -e "${GREEN}  - API文档: http://localhost:${BACKEND_PORT}/docs${NC}"
    else
        echo -e "${RED}❌ 后端服务验证失败${NC}"
    fi

    if [ -f "$FRONTEND_PID_FILE" ]; then
        if [ "$frontend_ok" = true ]; then
            echo -e "${GREEN}🎉 前端服务验证通过！${NC}"
            echo -e "${GREEN}  - 前端地址: http://localhost:${FRONTEND_PORT}${NC}"
        else
            echo -e "${RED}❌ 前端服务验证失败${NC}"
        fi
    fi

    echo -e "${BLUE}========================================${NC}"

    # 返回验证结果
    if [ "$backend_ok" = true ] && [ "$frontend_ok" = true ]; then
        echo -e "${GREEN}✅ 所有服务验证通过${NC}"
        return 0
    else
        echo -e "${RED}⚠️ 部分服务验证失败${NC}"
        return 1
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}📋 查看服务日志${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if [ -f "backend.log" ]; then
        echo -e "${YELLOW}后端日志 (最后20行):${NC}"
        tail -20 backend.log
        echo ""
    fi
    
    if [ -f "frontend.log" ]; then
        echo -e "${YELLOW}前端日志 (最后20行):${NC}"
        tail -20 frontend.log
    fi
    
    echo -e "${BLUE}========================================${NC}"
    echo -e "${YELLOW}实时查看日志:${NC}"
    echo -e "  tail -f backend.log   # 后端日志"
    echo -e "  tail -f frontend.log  # 前端日志"
}

# 主程序
main() {
    # 先获取命令，再解析其余参数
    command="$1"
    shift || true

    # 解析剩余参数
    parse_args "$@"
    
    case "$command" in
        start)
            start_services
            ;;
        start-backend)
            BACKEND_ONLY=true
            start_services
            ;;
        start-frontend)
            start_frontend
            ;;
        stop)
            stop_services
            ;;
        stop-backend)
            stop_backend_service
            ;;
        stop-frontend)
            stop_frontend_service
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        verify)
            verify_services
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            echo -e "${BLUE}🚀 默认启动完整服务（后端+前端）${NC}"
            start_services
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $command${NC}"
            echo -e "${YELLOW}使用 '$0 help' 查看帮助${NC}"
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
