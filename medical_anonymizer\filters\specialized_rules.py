"""
实体类型特殊实体规则过滤处理器
第二层过滤：针对特定实体类型的复杂规则处理

其实主要是人名的过滤，其他的过滤器作用一般并且没什么必要
"""

import re
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
# from .medical_dictionaries import DRUG_NAMES, DISEASE_NAMES, NEUROLOGICAL_SIGNS, BIOMOLECULAR_TERMS


class BaseEntityRules(ABC):
    """实体规则处理器基类"""
    
    @abstractmethod
    def should_exclude(self, entity_text: str, context: str) -> bool:
        """判断是否应该排除该实体"""
        pass
    
    @abstractmethod
    def get_entity_type(self) -> str:
        """获取处理的实体类型"""
        pass


class PersonFilterRules(BaseEntityRules):
    """PERSON类型特殊实体规则过滤处理器"""
    
    def __init__(self):
        # 预编译正则表达式提高性能
        self._medical_patterns = [
            re.compile(r'无\w+'),           # 无xxx的医学描述
            re.compile(r'\w+无\w*'),        # xxx无xxx的医学描述
            re.compile(r'\w+分型'),         # xxx分型
            re.compile(r'\w+检查'),         # xxx检查
            re.compile(r'\w+分析'),         # xxx分析
            re.compile(r'\w+测定'),         # xxx测定
            re.compile(r'\w+检验'),         # xxx检验
            re.compile(r'\w+检测'),         # xxx检测
            re.compile(r'\w+诊断'),         # xxx诊断
            re.compile(r'\w+治疗'),         # xxx治疗
            re.compile(r'\w+淋巴瘤'),       # 各种淋巴瘤
            re.compile(r'\w+白血病'),       # 各种白血病
            re.compile(r'\w+骨髓瘤')        # 各种骨髓瘤
        ]
        

        
        # 上下文排除关键词
        # self._context_exclusions = frozenset([
        #     ")", "(", "*", "+", "-", "↑", "↓",
        # ])
    
    def should_exclude(self, entity_text: str, context: str) -> bool:
        """PERSON类型专门化过滤规则"""
        
        # 规则1：长度过滤（基于统计规律）
        if len(entity_text) >= 4:
            return True
        
        # # 规则2：药物名称检查
        # if entity_text in DRUG_NAMES:
        #     return True
        
        # # 规则3：疾病名称检查
        # if entity_text in DISEASE_NAMES:
        #     return True
        
        # 规则4：特殊符号检查
        if any(char in entity_text for char in [')', '(', '*', '+', '-', '↑', '↓']):
            return True
        
        # 规则5：医学缩写检查
        if entity_text.isupper() and len(entity_text) >= 2:
            return True
        
        # 规则6：医学术语模式匹配
        for pattern in self._medical_patterns:
            if pattern.search(entity_text):
                return True
        
        # 规则7：上下文关键词检查
        # if any(keyword in context for keyword in self._context_exclusions):
        #     return True
        
        # 规则8：中文人名宽松处理
        # len(entity_text) <= 3 and
        if all('\u4e00' <= char <= '\u9fff' for char in entity_text):
            # 对于中文PERSON实体，采用宽松策略：
            # 只要是纯中文且长度合理，就认为可能是真实人名，保留
            if 1 <= len(entity_text) <= 4:
                return False  # 保留可能的中文人名

        # 其他情况：非中文的PERSON实体，也保留（可能是外国人名）
        return False
    

    
    def get_entity_type(self) -> str:
        return "PERSON"


# class LocationFilterRules(BaseEntityRules):
#     """LOCATION类型特殊实体规则过滤处理器"""
#
#     def __init__(self):
#         self._context_exclusions = frozenset([
#             "干化学", "尿流式", "镜检", "检验", "分析", "测定", "检查", "检测"
#         ])
#
#     def should_exclude(self, entity_text: str, context: str) -> bool:
#         """LOCATION类型专门化过滤规则"""
#
#         # 规则1：疾病相关术语检查
#         # if entity_text in DISEASE_NAMES:
#         #     return True
#
#         # 规则2：长度过滤
#         if len(entity_text) == 1:
#             return True
#
#         # 规则3：上下文关键词检查
#         if any(keyword in context for keyword in self._context_exclusions):
#             return True
#
#         return False
#
#     def get_entity_type(self) -> str:
#         return "LOCATION"



class MedicalNumberFilterRules(BaseEntityRules):
    """MEDICAL_NUMBER类型特殊实体规则过滤处理器"""
    
    def __init__(self):
        self._context_exclusions = frozenset([
            "反射", "征阴性", "征阳性"
        ])
    
    def should_exclude(self, entity_text: str, context: str) -> bool:
        """MEDICAL_NUMBER类型专门化过滤规则"""
        
        # 规则1：神经反射检查术语检查
        # if entity_text in NEUROLOGICAL_SIGNS:
        #     return True
        
        # 规则2：医学缩写模式检查
        if any(char.isdigit() for char in entity_text):
            if entity_text.startswith(('ICD', 'CPT', 'DRG', 'MDC')):
                return True
        
        # 规则3：上下文关键词检查
        if any(keyword in context for keyword in self._context_exclusions):
            return True
        
        return False
    
    def get_entity_type(self) -> str:
        return "MEDICAL_NUMBER"


class OrgFilterRules(BaseEntityRules):
    """ORG类型特殊实体规则过滤处理器"""
    
    def __init__(self):
        self._context_exclusions = frozenset([
            "检验", "检查", "分析", "测定", "检测"
        ])
    
    def should_exclude(self, entity_text: str, context: str) -> bool:
        """ORG类型专门化过滤规则"""
        
        # 规则1：长度过滤
        if len(entity_text) == 1:
            return True
        
        # 规则2：上下文关键词检查
        if any(keyword in context for keyword in self._context_exclusions):
            return True
        
        return False
    
    def get_entity_type(self) -> str:
        return "ORG"





class UrlFilterRules(BaseEntityRules):
    """URL类型特殊实体规则过滤处理器"""

    def __init__(self):
        self._context_exclusions = frozenset([
            "检验", "检查", "分析", "测定", "检测", "评分"
        ])

    def should_exclude(self, entity_text: str, context: str) -> bool:
        """URL类型专门化过滤规则"""

        # 规则1：医学评分术语检查
        if "ECOG" in entity_text or "评分" in context:
            return True

        # 规则2：上下文关键词检查
        if any(keyword in context for keyword in self._context_exclusions):
            return True

        return False

    def get_entity_type(self) -> str:
        return "URL"


class DateTimeFilterRules(BaseEntityRules):
    """
    DATE_TIME类型特殊实体规则过滤处理器 - 采用严格白名单方法过滤非时间实体

    设计理念：
    - 采用白名单策略，只保留明确的有效日期时间格式
    - 优先精确性而非召回率，宁可错过边缘情况也不误保留医疗数值
    - 有效过滤医疗数值误识别，如 "107.00"、"40--200 U/L"、"." 等

    保留的格式包括：
    - 完整日期：2024年1月15日、2024-01-15、1月15日
    - 完整时间：上午9:30、14:30、晚上8点
    - 相对日期：今天、昨天、明天、星期一
    - 完整日期时间组合

    排除的格式包括：
    - 医疗数值片段：.、00 40、107.00
    - 医疗范围：40--200、5.5-12.5
    - 单纯数字：107、40、200
    - 不完整的时间片段
    """

    def __init__(self):
        # 严格的有效日期时间白名单模式（只有匹配这些模式的实体才会被保留）
        self._valid_datetime_whitelist = [
            # === 完整日期格式 ===
            # 中文日期格式
            re.compile(r'^\d{4}年\d{1,2}月\d{1,2}[日号]$'),           # 2024年1月15日、2024年01月15号
            re.compile(r'^\d{4}年\d{1,2}月$'),                       # 2024年1月、2024年01月
            re.compile(r'^\d{1,2}月\d{1,2}[日号]$'),                 # 1月15日、01月15号

            # 数字日期格式（严格完整格式）
            re.compile(r'^\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}$'),        # 2024-01-15、2024/01/15、2024.01.15
            re.compile(r'^\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4}$'),        # 15-01-2024、15/01/2024

            # === 完整时间格式 ===
            # 带时段的时间（中文）
            re.compile(r'^(?:上午|下午|早上|晚上|中午|凌晨)\s*\d{1,2}[：:]\d{1,2}(?:[：:]\d{1,2})?$'),  # 上午9:30、下午2:30:45
            re.compile(r'^(?:上午|下午|早上|晚上|中午|凌晨)\s*\d{1,2}点(?:\d{1,2}分)?(?:\d{1,2}秒)?$'),  # 上午9点、晚上8点30分

            # 24小时制时间（严格格式）
            re.compile(r'^(?:[01]?\d|2[0-3])[：:][0-5]\d(?:[：:][0-5]\d)?$'),  # 14:30、09:30:45、23:59

            # === 相对日期时间 ===
            # 相对日期
            # re.compile(r'^(?:今天|昨天|明天|前天|后天|今日|昨日|明日)$'),
            re.compile(r'^(?:星期|周)[一二三四五六七天日1-7]$'),              # 星期一、周二

            # 时间段表达
            # re.compile(r'^(?:早上|上午|中午|下午|晚上|夜里|凌晨|深夜)$'),

            # === 完整的日期时间组合 ===
            # 日期+时间组合
            re.compile(r'^\d{4}年\d{1,2}月\d{1,2}[日号]\s*(?:上午|下午|早上|晚上|中午|凌晨)\s*\d{1,2}[：:]\d{1,2}$'),
            re.compile(r'^\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}\s+(?:[01]?\d|2[0-3])[：:][0-5]\d$'),
            re.compile(r'^\d{1,2}月\d{1,2}[日号]\s*(?:上午|下午|早上|晚上|中午|凌晨)\s*\d{1,2}点$'),

            # === 特殊的完整时间表达 ===
            # 农历和传统时间
            re.compile(r'^农历\d{1,2}月\d{1,2}[日号]$'),                # 农历正月十五
            re.compile(r'^(?:正月|二月|三月|四月|五月|六月|七月|八月|九月|十月|十一月|十二月)\d{1,2}[日号]$'),

            # 季节和月份
            re.compile(r'^(?:春季|夏季|秋季|冬季|春天|夏天|秋天|冬天)$'),
            re.compile(r'^(?:一月|二月|三月|四月|五月|六月|七月|八月|九月|十月|十一月|十二月)$'),

            # 年份（完整4位数）
            re.compile(r'^\d{4}年$'),                                  # 2024年

            # === 时间间隔和持续时间（完整表达） ===
            # 明确的时间间隔表达
            # re.compile(r'^(?:半小时|一小时|两小时|三小时|四小时|五小时|六小时|七小时|八小时|九小时|十小时)$'),
            # re.compile(r'^(?:一天|两天|三天|四天|五天|六天|七天|一周|两周|三周|四周|一个月|两个月|三个月|半年|一年)$'),
        ]

    def should_exclude(self, entity_text: str, context: str) -> bool:
        """
        DATE_TIME类型专门化过滤规则 - 采用严格白名单策略

        白名单策略：只有明确匹配有效日期时间格式的实体才会被保留（返回False），
        所有其他实体都会被排除（返回True），确保精确性优于召回率。

        Args:
            entity_text: 实体文本
            context: 上下文文本（在白名单策略中主要用于保持接口一致性）

        Returns:
            True: 排除该实体（不是有效的日期时间）
            False: 保留该实体（是有效的日期时间）
        """

        # 白名单策略：检查实体文本是否匹配任何有效的日期时间格式
        # 注意：我们主要依赖实体文本本身，context 参数保留用于接口一致性
        for pattern in self._valid_datetime_whitelist:
            if pattern.match(entity_text.strip()):
                # 匹配白名单模式，保留该实体
                return False

        # 不匹配任何白名单模式，排除该实体
        # 这包括所有可能的医疗数值、片段、范围等，如：
        # - "." (小数点片段)
        # - "00 40" (数字片段)
        # - "107.00" (医疗数值)
        # - "40--200" (医疗范围)
        return True

    def _is_valid_datetime_format(self, entity_text: str) -> bool:
        """
        检查实体文本是否为有效的日期时间格式

        Args:
            entity_text: 实体文本

        Returns:
            True: 是有效的日期时间格式
            False: 不是有效的日期时间格式
        """
        # 使用白名单模式进行严格匹配
        for pattern in self._valid_datetime_whitelist:
            if pattern.match(entity_text.strip()):
                return True
        return False

    def get_entity_type(self) -> str:
        return "DATE_TIME"


# 规则处理器注册表
ENTITY_RULES_REGISTRY = {
    "PERSON": PersonFilterRules,
    # "LOCATION": LocationFilterRules,
    "MEDICAL_NUMBER": MedicalNumberFilterRules,
    "ORG": OrgFilterRules,
    "URL": UrlFilterRules,
    "DATE_TIME": DateTimeFilterRules
}


def get_entity_rules(entity_type: str) -> Optional[BaseEntityRules]:
    """
    获取指定实体类型的规则处理器
    
    Args:
        entity_type: 实体类型
        
    Returns:
        规则处理器实例，如果不存在则返回None
    """
    rules_class = ENTITY_RULES_REGISTRY.get(entity_type)
    if rules_class:
        return rules_class()
    return None
