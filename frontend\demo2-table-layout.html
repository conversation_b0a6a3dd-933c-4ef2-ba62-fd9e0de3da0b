<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 2: 表格式布局 - 敏感信息详情显示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .demo-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .demo-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .demo-header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .demo-content {
            padding: 30px;
        }

        .controls-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stats-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-badge {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .sensitive-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
        }

        .sensitive-table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .sensitive-table th:first-child {
            border-top-left-radius: 12px;
        }

        .sensitive-table th:last-child {
            border-top-right-radius: 12px;
        }

        .sensitive-table td {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .sensitive-table tr:hover {
            background: #f8f9fa;
        }

        .sensitive-table tr:last-child td {
            border-bottom: none;
        }

        .sensitive-text {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
            word-break: break-word;
        }

        .detector-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            color: white;
        }

        .detector-regex { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .detector-dictionary { background: linear-gradient(135deg, #3498db, #2980b9); }
        .detector-context { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .detector-nlp_model { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .detector-hybrid { background: linear-gradient(135deg, #2ecc71, #27ae60); }

        .entity-type-badge {
            background: #6c757d;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .position-info {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .sort-header {
            cursor: pointer;
            user-select: none;
            transition: color 0.3s ease;
        }

        .sort-header:hover {
            color: #667eea;
        }

        .sort-header i {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-header.active i {
            opacity: 1;
            color: #667eea;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 768px) {
            .controls-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats-info {
                justify-content: center;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .sensitive-table {
                min-width: 600px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.history.back()">
        <i class="fas fa-arrow-left"></i> 返回主页
    </button>

    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-table"></i> 方案二：表格式布局</h1>
            <p>以表格形式展示敏感信息，支持排序和筛选，信息密度高，便于对比分析</p>
        </div>

        <div class="demo-content">
            <div class="controls-bar">
                <div class="stats-info">
                    <div class="stat-badge">
                        <i class="fas fa-shield-alt"></i>
                        总计: <strong id="total-count">0</strong> 项
                    </div>
                    <div class="stat-badge">
                        <i class="fas fa-cogs"></i>
                        检测器: <strong id="detector-count">0</strong> 种
                    </div>
                </div>
                
                <div class="filter-controls">
                    <label for="detector-filter">筛选检测器:</label>
                    <select id="detector-filter" class="filter-select">
                        <option value="">全部检测器</option>
                    </select>
                    <label for="type-filter">筛选类型:</label>
                    <select id="type-filter" class="filter-select">
                        <option value="">全部类型</option>
                    </select>
                </div>
            </div>

            <div class="table-container">
                <table class="sensitive-table">
                    <thead>
                        <tr>
                            <th class="sort-header" data-sort="text">
                                敏感文本 <i class="fas fa-sort"></i>
                            </th>
                            <th class="sort-header" data-sort="detector">
                                检测器 <i class="fas fa-sort"></i>
                            </th>
                            <th class="sort-header" data-sort="type">
                                信息类型 <i class="fas fa-sort"></i>
                            </th>
                            <th class="sort-header" data-sort="position">
                                位置 <i class="fas fa-sort"></i>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <!-- 表格行将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="js/data-formatters.js"></script>
    <script src="js/detector-mapping.js"></script>
    <script>
        // 示例数据
        const sampleData = [
            { text: "张三", entity_type: "PERSON", start: 2, end: 4 },
            { text: "110101199001011234", entity_type: "ChineseIDRecognizer", start: 15, end: 33 },
            { text: "13812345678", entity_type: "MobilePhoneRecognizer", start: 40, end: 51 },
            { text: "汉族", entity_type: "EthnicityRecognizer", start: 60, end: 62 },
            { text: "MR20230001", entity_type: "MedicalNumberRecognizer", start: 70, end: 80 },
            { text: "北京市朝阳区", entity_type: "LOCATION", start: 90, end: 96 },
            { text: "主任医师", entity_type: "EducationLevelRecognizer", start: 100, end: 104 },
            { text: "zhang_san_123", entity_type: "WeChatRecognizer", start: 110, end: 123 }
        ];

        let currentData = [...sampleData];
        let sortColumn = '';
        let sortDirection = 'asc';

        function renderTable() {
            const tbody = document.getElementById('table-body');
            const detectorTypes = new Set();
            const entityTypes = new Set();
            
            let html = '';
            currentData.forEach(entity => {
                const detectorInfo = window.DetectorMapping.getDetectorInfo(entity.entity_type);
                const entityType = window.DataFormatters.ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type;
                
                detectorTypes.add(detectorInfo.type);
                entityTypes.add(entityType);

                html += `
                    <tr>
                        <td>
                            <div class="sensitive-text">${entity.text}</div>
                        </td>
                        <td>
                            <div class="detector-badge detector-${detectorInfo.type.toLowerCase()}">
                                <i class="${detectorInfo.icon}"></i>
                                ${detectorInfo.name}
                            </div>
                        </td>
                        <td>
                            <div class="entity-type-badge">${entityType}</div>
                        </td>
                        <td>
                            <div class="position-info">${entity.start}-${entity.end}</div>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            document.getElementById('total-count').textContent = currentData.length;
            document.getElementById('detector-count').textContent = detectorTypes.size;
            
            updateFilters(detectorTypes, entityTypes);
        }

        function updateFilters(detectorTypes, entityTypes) {
            const detectorFilter = document.getElementById('detector-filter');
            const typeFilter = document.getElementById('type-filter');
            
            // 更新检测器筛选器
            detectorFilter.innerHTML = '<option value="">全部检测器</option>';
            detectorTypes.forEach(type => {
                const detectorInfo = window.DetectorMapping.DETECTOR_TYPES[type];
                detectorFilter.innerHTML += `<option value="${type}">${detectorInfo.name}</option>`;
            });
            
            // 更新类型筛选器
            typeFilter.innerHTML = '<option value="">全部类型</option>';
            entityTypes.forEach(type => {
                typeFilter.innerHTML += `<option value="${type}">${type}</option>`;
            });
        }

        function filterData() {
            const detectorFilter = document.getElementById('detector-filter').value;
            const typeFilter = document.getElementById('type-filter').value;
            
            currentData = sampleData.filter(entity => {
                const detectorInfo = window.DetectorMapping.getDetectorInfo(entity.entity_type);
                const entityType = window.DataFormatters.ENTITY_TYPE_MAPPING[entity.entity_type] || entity.entity_type;
                
                const matchesDetector = !detectorFilter || detectorInfo.type === detectorFilter;
                const matchesType = !typeFilter || entityType === typeFilter;
                
                return matchesDetector && matchesType;
            });
            
            renderTable();
        }

        function sortData(column) {
            if (sortColumn === column) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortDirection = 'asc';
            }
            
            currentData.sort((a, b) => {
                let valueA, valueB;
                
                switch (column) {
                    case 'text':
                        valueA = a.text;
                        valueB = b.text;
                        break;
                    case 'detector':
                        valueA = window.DetectorMapping.getDetectorInfo(a.entity_type).name;
                        valueB = window.DetectorMapping.getDetectorInfo(b.entity_type).name;
                        break;
                    case 'type':
                        valueA = window.DataFormatters.ENTITY_TYPE_MAPPING[a.entity_type] || a.entity_type;
                        valueB = window.DataFormatters.ENTITY_TYPE_MAPPING[b.entity_type] || b.entity_type;
                        break;
                    case 'position':
                        valueA = a.start;
                        valueB = b.start;
                        break;
                }
                
                if (sortDirection === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });
            
            updateSortHeaders();
            renderTable();
        }

        function updateSortHeaders() {
            document.querySelectorAll('.sort-header').forEach(header => {
                header.classList.remove('active');
                const icon = header.querySelector('i');
                icon.className = 'fas fa-sort';
            });
            
            if (sortColumn) {
                const activeHeader = document.querySelector(`[data-sort="${sortColumn}"]`);
                activeHeader.classList.add('active');
                const icon = activeHeader.querySelector('i');
                icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            }
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', () => {
            renderTable();
            
            // 排序事件
            document.querySelectorAll('.sort-header').forEach(header => {
                header.addEventListener('click', () => {
                    sortData(header.dataset.sort);
                });
            });
            
            // 筛选事件
            document.getElementById('detector-filter').addEventListener('change', filterData);
            document.getElementById('type-filter').addEventListener('change', filterData);
        });
    </script>
</body>
</html>
