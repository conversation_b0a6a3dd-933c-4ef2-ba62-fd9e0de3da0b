# HIPAA脱敏服务容器内启动脚本指南

## 概述

基于现有的 `run_docker.sh` Docker管理脚本，我创建了三个专门用于Docker容器内部的服务启动和管理脚本。这些脚本设计用于在已创建的Docker容器内部运行，不负责容器的创建和管理。

## 脚本说明

### 1. `start_services.sh` - 完整功能启动脚本

**功能特性:**
- ✅ 完整的服务管理功能（启动、停止、重启、状态查看）
- ✅ 支持自定义配置（端口、工作进程数等）
- ✅ 智能的错误处理和健康检查
- ✅ 详细的日志管理
- ✅ 服务依赖检查

**适用场景:**
- 需要完整服务管理功能的生产环境
- 需要灵活配置的部署场景
- 需要详细监控和日志的环境



## 使用方法

### 方式一：使用完整功能脚本

```bash
# 进入容器
docker exec -it hipaa-deidentify-service bash

# 启动完整服务
./start_services.sh

# 仅启动后端
./start_services.sh start --backend-only

# 查看服务状态
./start_services.sh status

# 停止服务
./start_services.sh stop
```


## 推荐工作流程

### 生产环境

```bash
# 1. 部署容器（宿主机）
./run_docker.sh deploy --backend-only

# 2. 进入容器
docker exec -it hipaa-deidentify-service bash

# 3. 启动服务（容器内）
./start_services.sh start --workers 8 --backend-port 50505

# 4. 监控服务状态
./start_services.sh status

# 5. 查看日志
./start_services.sh logs
```

## 配置说明

### 默认配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 后端端口 | 50505 | API服务端口 |
| 前端端口 | 50506 | Web界面端口 |
| 工作进程 | 5 | 后端工作进程数 |

### 自定义配置示例

```bash
# 自定义端口和进程数
./start_services.sh start --backend-port 50509 --frontend-port 50501 --workers 5

# 仅启动后端，使用更多工作进程
bash start_services.sh start --backend-only --backend-port 50508 --workers 5
```

## 日志管理

### 日志文件位置

- `backend.log` - 后端服务日志
- `frontend.log` - 前端服务日志

### 查看日志

```bash
# 查看日志摘要
./start_services.sh logs

# 实时查看后端日志
tail -f backend.log

# 实时查看前端日志
tail -f frontend.log

# 同时查看两个日志
tail -f backend.log frontend.log
```

## 故障排除

### 1. 脚本权限问题

在Linux容器中，确保脚本有执行权限：

```bash
chmod +x start_services.sh quick_start.sh stop_services.sh
```

### 2. 端口冲突

如果默认端口被占用：

```bash
# 使用自定义端口
./start_services.sh start --backend-port 50507 --frontend-port 50508
```

### 3. 服务启动失败

```bash
# 查看详细状态
./start_services.sh status

# 查看错误日志
tail -50 backend.log
tail -50 frontend.log
```
