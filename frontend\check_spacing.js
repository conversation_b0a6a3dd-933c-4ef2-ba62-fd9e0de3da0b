// 检查卡片间距的脚本
function checkCardSpacing() {
    const resultsSection = document.getElementById('results-section');
    const cards = resultsSection.querySelectorAll('.result-card');
    
    console.log('=== 卡片间距检查 ===');
    console.log('结果区域:', resultsSection);
    console.log('卡片数量:', cards.length);
    
    cards.forEach((card, index) => {
        const computedStyle = window.getComputedStyle(card);
        const marginBottom = computedStyle.marginBottom;
        const marginTop = computedStyle.marginTop;
        
        console.log(`卡片 ${index + 1}:`);
        console.log(`  - margin-bottom: ${marginBottom}`);
        console.log(`  - margin-top: ${marginTop}`);
        
        if (index < cards.length - 1) {
            const nextCard = cards[index + 1];
            const cardRect = card.getBoundingClientRect();
            const nextCardRect = nextCard.getBoundingClientRect();
            const actualGap = nextCardRect.top - cardRect.bottom;
            
            console.log(`  - 与下一个卡片的实际间距: ${actualGap}px`);
        }
    });
    
    // 检查结果区域的样式
    const sectionStyle = window.getComputedStyle(resultsSection);
    console.log('结果区域样式:');
    console.log(`  - display: ${sectionStyle.display}`);
    console.log(`  - flex-direction: ${sectionStyle.flexDirection}`);
    console.log(`  - gap: ${sectionStyle.gap}`);
    console.log(`  - margin-top: ${sectionStyle.marginTop}`);
    console.log(`  - margin-bottom: ${sectionStyle.marginBottom}`);
}

// 在页面加载完成后运行检查
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(checkCardSpacing, 1000);
    });
} else {
    setTimeout(checkCardSpacing, 1000);
}

// 也可以手动调用
window.checkCardSpacing = checkCardSpacing;
