#!/bin/bash
"""
重启服务脚本

停止由run_docker.sh启动的服务，然后使用新的启动脚本重新启动。
"""

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔄 重启HIPAA脱敏服务${NC}"
echo "========================================"

# 第一步：停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"

echo "停止uvicorn进程..."
pkill -f "uvicorn" 2>/dev/null || true

echo "停止API服务进程..."
pkill -f "api.main:app" 2>/dev/null || true

echo "停止Python启动进程..."
pkill -f "python -c" 2>/dev/null || true

echo "停止其他相关进程..."
pkill -f "start_service.py" 2>/dev/null || true
pkill -f "start_frontend.py" 2>/dev/null || true

# 等待进程停止
echo "等待进程停止..."
sleep 5

# 强制停止残留进程
echo "清理残留进程..."
pkill -9 -f "uvicorn" 2>/dev/null || true
pkill -9 -f "api.main:app" 2>/dev/null || true
pkill -9 -f "python -c" 2>/dev/null || true
pkill -9 -f "start_service.py" 2>/dev/null || true
pkill -9 -f "start_frontend.py" 2>/dev/null || true

echo -e "${GREEN}✓ 现有服务已停止${NC}"

# 第二步：验证服务已停止
echo -e "${YELLOW}🔍 验证服务状态...${NC}"

remaining_processes=$(ps aux | grep -E "(uvicorn|api.main|start_service|start_frontend)" | grep -v grep | wc -l)

if [ "$remaining_processes" -eq 0 ]; then
    echo -e "${GREEN}✓ 所有相关进程已停止${NC}"
else
    echo -e "${RED}⚠️ 发现 $remaining_processes 个残留进程:${NC}"
    ps aux | grep -E "(uvicorn|api.main|start_service|start_frontend)" | grep -v grep || true
    echo ""
fi

# 检查端口状态
echo "检查端口状态..."
if netstat -tlnp 2>/dev/null | grep -q ":50505"; then
    echo -e "${YELLOW}⚠️ 端口50505仍被占用${NC}"
    netstat -tlnp | grep ":50505" || true
else
    echo -e "${GREEN}✓ 端口50505已释放${NC}"
fi

echo ""

# 第三步：选择启动方式
echo -e "${BLUE}🚀 选择启动方式:${NC}"
echo "1) 快速启动 (后端+前端)"
echo "2) 快速启动 (仅后端)"
echo "3) 完整功能启动 (后端+前端)"
echo "4) 完整功能启动 (仅后端)"
echo "5) 手动选择"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo -e "${GREEN}启动快速模式 (后端+前端)...${NC}"
        ./quick_start.sh
        ;;
    2)
        echo -e "${GREEN}启动快速模式 (仅后端)...${NC}"
        ./quick_start.sh --backend-only
        ;;
    3)
        echo -e "${GREEN}启动完整功能模式 (后端+前端)...${NC}"
        ./start_services.sh start
        ;;
    4)
        echo -e "${GREEN}启动完整功能模式 (仅后端)...${NC}"
        ./start_services.sh start --backend-only
        ;;
    5)
        echo -e "${YELLOW}可用的启动命令:${NC}"
        echo "  ./quick_start.sh                    # 快速启动 (后端+前端)"
        echo "  ./quick_start.sh --backend-only     # 快速启动 (仅后端)"
        echo "  ./start_services.sh start           # 完整功能 (后端+前端)"
        echo "  ./start_services.sh start --backend-only  # 完整功能 (仅后端)"
        echo ""
        echo "请手动执行您需要的命令。"
        ;;
    *)
        echo -e "${RED}无效选择，请手动启动服务${NC}"
        echo "可用命令:"
        echo "  ./quick_start.sh"
        echo "  ./start_services.sh start"
        ;;
esac

echo ""
echo "========================================"
echo -e "${GREEN}🎉 重启流程完成！${NC}"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "- 查看服务状态: ./start_services.sh status"
echo "- 查看日志: tail -f backend.log frontend.log"
echo "- 停止服务: ./stop_services.sh"
