# HIPAA脱敏服务容器内启动脚本使用说明

## 概述

`start_services.sh` 是一个专门设计用于在已创建的Docker容器内部启动HIPAA医疗数据脱敏服务的shell脚本。该脚本不负责容器的创建和管理，仅负责容器内部服务的启动、停止和管理。

## 功能特性

- ✅ 自动启动后端脱敏API服务
- ✅ 自动启动前端演示页面
- ✅ 支持仅启动后端服务模式
- ✅ 智能的服务依赖检查
- ✅ 完善的错误处理和日志记录
- ✅ 服务状态监控和管理
- ✅ 优雅的服务停止和重启

## 使用方法

### 1. 基本使用

```bash
# 启动完整服务（后端+前端）
./start_services.sh

# 或者显式指定start命令
./start_services.sh start
```

### 2. 仅启动后端服务

```bash
# 仅启动后端API服务
./start_services.sh start-backend

# 或者使用参数
./start_services.sh start --backend-only
```

### 3. 分别启动服务

```bash
# 仅启动后端
./start_services.sh start-backend

# 仅启动前端
./start_services.sh start-frontend
```

### 4. 服务管理

```bash
# 查看服务状态
./start_services.sh status

# 停止所有服务
./start_services.sh stop

# 停止特定服务
./start_services.sh stop-backend
./start_services.sh stop-frontend

# 重启所有服务
./start_services.sh restart

# 查看服务日志
./start_services.sh logs
```

### 5. 自定义配置

```bash
# 自定义端口和工作进程数
./start_services.sh start --backend-port 50505 --frontend-port 50506 --workers 8

# 仅启动后端，使用自定义配置
./start_services.sh start --backend-only --backend-port 50505 --workers 10
```

## 配置选项

| 选项 | 默认值 | 说明 |
|------|--------|------|
| `--backend-port` | 50505 | 后端服务端口 |
| `--frontend-port` | 50506 | 前端服务端口 |
| `--workers` | 5 | 后端工作进程数量 |
| `--backend-only` | false | 仅启动后端服务 |

## 服务信息

### 后端服务
- **端口**: 50505 (默认)
- **API文档**: http://localhost:50505/docs
- **健康检查**: http://localhost:50505/health
- **日志文件**: backend.log

### 前端服务
- **端口**: 50506 (默认)
- **访问地址**: http://localhost:50506
- **日志文件**: frontend.log

## 在Docker容器中使用

### 1. 进入运行中的容器

```bash
# 进入容器
docker exec -it hipaa-deidentify-service bash

# 运行启动脚本
./start_services.sh
```

### 2. 容器启动时自动运行

在Dockerfile中添加：

```dockerfile
# 复制启动脚本
COPY start_services.sh /app/
RUN chmod +x /app/start_services.sh

# 设置启动命令
CMD ["/app/start_services.sh"]
```

或者在docker run时指定：

```bash
docker run -d \
    --name hipaa-deidentify-service \
    -p 50505:50505 \
    -p 50506:50506 \
    hipaa-deidentify \
    /app/start_services.sh
```

## 故障排除

### 1. 权限问题

如果遇到权限错误，确保脚本有执行权限：

```bash
chmod +x start_services.sh
```

### 2. 端口占用

如果端口被占用，可以使用自定义端口：

```bash
./start_services.sh start --backend-port 50507 --frontend-port 50508
```

### 3. 服务启动失败

查看详细日志：

```bash
# 查看脚本输出的日志摘要
./start_services.sh logs

# 查看完整的实时日志
tail -f backend.log
tail -f frontend.log
```

### 4. 健康检查失败

检查服务状态：

```bash
./start_services.sh status
```

如果后端服务进程运行但健康检查失败，可能需要等待更长时间让服务完全启动。

## 日志管理

脚本会自动创建以下日志文件：

- `backend.log`: 后端服务日志
- `frontend.log`: 前端服务日志

查看实时日志：

```bash
# 后端日志
tail -f backend.log

# 前端日志  
tail -f frontend.log

# 同时查看两个日志
tail -f backend.log frontend.log
```

## 与原始Docker脚本的关系

这个脚本是对原始 `run_docker.sh` 脚本的补充：

- `run_docker.sh`: 负责Docker容器的创建、构建、管理
- `start_services.sh`: 负责容器内部服务的启动和管理

两个脚本可以配合使用：

1. 使用 `run_docker.sh` 创建和启动容器
2. 在容器内使用 `start_services.sh` 管理服务

## 示例工作流程

```bash
# 1. 在宿主机上构建和启动容器
./run_docker.sh deploy --backend-only

# 2. 进入容器
docker exec -it hipaa-deidentify-service bash

# 3. 在容器内启动服务
./start_services.sh start

# 4. 检查服务状态
./start_services.sh status

# 5. 查看日志
./start_services.sh logs
```

这样的设计提供了更大的灵活性，允许在容器运行时动态管理服务。
