"""
HIPAA脱敏服务请求工具

提供简洁易用的脱敏接口调用功能。
"""

import requests
import asyncio
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class DeidentifyResult:
    """脱敏结果数据类"""
    sensitive_words: List[Dict[str, Any]]
    deidentified_text: str
    processing_time: float
    total_entities: int

    @property
    def sensitive_words_only(self) -> List[str]:
        """获取纯敏感词列表"""
        return [entity['text'] for entity in self.sensitive_words]


class DeidentifyClient:
    """HIPAA脱敏服务客户端"""

    def __init__(self,
                 base_url: str = "http://127.0.0.1:50505",
                 timeout: int = 30):
        """
        初始化脱敏客户端

        Args:
            base_url: 脱敏服务的基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.deidentify_url = f"{self.base_url}/deidentify"

    def deidentify(self, text: str) -> DeidentifyResult:
        """
        调用脱敏接口

        Args:
            text: 待脱敏的文本

        Returns:
            DeidentifyResult: 脱敏结果对象
        """
        if not text or not text.strip():
            raise ValueError("文本内容不能为空")

        try:
            response = requests.post(
                self.deidentify_url,
                json={"text": text},
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                return DeidentifyResult(
                    sensitive_words=data['sensitive_words'],
                    deidentified_text=data['deidentified_text'],
                    processing_time=data['processing_time'],
                    total_entities=data['total_entities']
                )

            elif response.status_code == 400:
                error_data = response.json()
                raise ValueError(f"请求参数错误: {error_data.get('detail', {}).get('message', '未知错误')}")

            else:
                raise RuntimeError(f"服务返回错误，状态码: {response.status_code}")

        except requests.exceptions.Timeout:
            raise TimeoutError(f"请求超时（{self.timeout}秒）")

        except requests.exceptions.ConnectionError:
            raise ConnectionError(f"无法连接到脱敏服务: {self.base_url}")

        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"请求失败: {str(e)}")

    async def deidentify_async(self, text: str) -> DeidentifyResult:
        """
        异步调用脱敏接口

        Args:
            text: 待脱敏的文本

        Returns:
            DeidentifyResult: 脱敏结果对象
        """
        if not text or not text.strip():
            raise ValueError("文本内容不能为空")

        try:
            import aiohttp

            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    self.deidentify_url,
                    json={"text": text},
                    headers={"Content-Type": "application/json"}
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        return DeidentifyResult(
                            sensitive_words=data['sensitive_words'],
                            deidentified_text=data['deidentified_text'],
                            processing_time=data['processing_time'],
                            total_entities=data['total_entities']
                        )

                    elif response.status == 400:
                        error_data = await response.json()
                        raise ValueError(f"请求参数错误: {error_data.get('detail', {}).get('message', '未知错误')}")

                    else:
                        raise RuntimeError(f"服务返回错误，状态码: {response.status}")

        except asyncio.TimeoutError:
            raise TimeoutError(f"异步请求超时（{self.timeout}秒）")

        except ImportError:
            raise RuntimeError("异步功能需要安装aiohttp: pip install aiohttp")

        except Exception as e:
            if "aiohttp" in str(type(e)):
                raise ConnectionError(f"无法连接到脱敏服务: {self.base_url}")
            raise RuntimeError(f"异步请求失败: {str(e)}")


# 便捷函数
def deidentify_text(text: str,
                   base_url: str = "http://127.0.0.1:50505",
                   timeout: int = 30) -> DeidentifyResult:
    """
    便捷的脱敏函数

    Args:
        text: 待脱敏的文本
        base_url: 服务地址
        timeout: 超时时间

    Returns:
        DeidentifyResult: 脱敏结果
    """
    client = DeidentifyClient(base_url=base_url, timeout=timeout)
    return client.deidentify(text)


async def deidentify_text_async(text: str,
                               base_url: str = "http://127.0.0.1:50505",
                               timeout: int = 30) -> DeidentifyResult:
    """
    便捷的异步脱敏函数

    Args:
        text: 待脱敏的文本
        base_url: 服务地址
        timeout: 超时时间

    Returns:
        DeidentifyResult: 脱敏结果
    """
    client = DeidentifyClient(base_url=base_url, timeout=timeout)
    return await client.deidentify_async(text)
