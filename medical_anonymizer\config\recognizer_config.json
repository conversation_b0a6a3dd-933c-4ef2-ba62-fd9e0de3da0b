{"recognizers": {"identityDocuments": {"name": "identityDocuments", "displayName": "身份证件类", "recognizers": {"chineseId": {"name": "chineseId", "displayName": "中国身份证", "description": "18位中国身份证号码识别", "category": "nlp_recognizer", "enabled": true, "sources": ["ChineseIDRecognizer"]}, "certificates": {"name": "certificates", "displayName": "各类证件", "description": "护照、驾照、军官证等各类证件识别", "category": "contextual", "enabled": true, "positiveKeywords": ["护照", "护照号", "护照号码", "passport", "Passport", "PASSPORT", "护照信息", "护照证件", "护照编号", "出入境证件", "国际证件", "军官证", "军官证号", "军官证号码", "军人证", "军人证件", "部队证件", "军籍号", "军人身份证", "现役军人证", "军官身份证", "驾驶证", "驾驶证号", "驾驶证号码", "驾照", "驾照号", "驾照号码", "机动车驾驶证", "驾驶执照", "驾驶证件", "驾驶证编号", "工作证", "工作证号", "工作证号码", "员工证", "员工证号", "员工卡", "工牌", "工号", "职工证", "工作证件", "员工证件", "工作卡", "居住证", "居住证号", "居住证号码", "暂住证", "暂住证号", "居住证件", "居住登记", "居住证编号", "临时居住证", "居住许可证", "证件", "证件号", "证件号码", "证书", "证书号", "执照", "执照号", "许可证", "许可证号", "资格证", "资格证号", "从业证", "从业证号"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "床位号", "手机", "电话", "身份证", "银行卡", "医保卡", "设备", "仪器", "机器", "系统", "软件", "程序", "医学", "医疗", "临床", "检测", "化验", "诊断"], "sources": ["CertificateRecognizer"]}, "bankCards": {"name": "bankCards", "displayName": "银行卡信息", "description": "银行卡号、信用卡号识别", "category": "contextual", "enabled": true, "positiveKeywords": ["银行卡", "卡号", "账户", "银行账户", "储蓄卡", "信用卡", "银行卡号", "账户号", "银行账号", "卡片号码", "银行卡账户"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "手机", "电话", "身份证", "医保卡", "工号", "学号"], "sources": ["BankCardRecognizer"]}, "creditCard": {"name": "creditCard", "displayName": "信用卡号", "description": "信用卡号识别（国际标准）", "category": "nlp_recognizer", "enabled": true, "sources": ["CREDIT_CARD"]}, "medicalInsurance": {"name": "medicalInsurance", "displayName": "医保社保卡", "description": "医保社保卡号识别", "category": "contextual", "enabled": true, "positiveKeywords": ["医保卡", "社保卡", "医保号", "社保号", "医疗保险", "社会保险", "医保账户", "社保账户", "保险卡号", "医疗卡"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "手机", "电话", "身份证", "银行卡", "工号"], "sources": ["MedicalInsuranceRecognizer"]}, "internationalBankAccount": {"name": "internationalBankAccount", "displayName": "国际银行账号", "description": "国际银行账号识别", "category": "nlp_recognizer", "enabled": true, "sources": ["IBAN_CODE"]}}}, "communication": {"name": "communication", "displayName": "通信联系类", "recognizers": {"mobilePhone": {"name": "mobilePhone", "displayName": "手机号码", "description": "手机号码识别", "category": "contextual", "enabled": true, "positiveKeywords": ["手机", "手机号", "电话", "联系电话", "移动电话", "手机号码", "联系方式", "电话号码", "手机联系", "移动号码"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "床位号", "身份证", "银行卡", "医保卡", "工号", "学号"], "sources": ["MobilePhoneRecognizer"]}, "landlinePhone": {"name": "landlinePhone", "displayName": "固定电话", "description": "固定电话号码识别", "category": "contextual", "enabled": true, "positiveKeywords": ["电话", "座机", "固话", "联系电话", "办公电话", "家庭电话", "固定电话", "电话号码", "联系方式", "办公室电话"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "手机", "移动电话", "身份证", "银行卡"], "sources": ["LandlinePhoneRecognizer"]}, "wechat": {"name": "wechat", "displayName": "微信号", "description": "微信号识别", "category": "contextual", "enabled": true, "positiveKeywords": ["微信", "WeChat", "微信号", "微信账号", "wechat", "wx", "WX", "微信联系", "加微信", "微信好友", "微信群", "微信名", "WeChat ID", "wechat id", "WECHAT ID", "联系微信"], "negativeKeywords": ["编号", "代码", "系统", "数据库", "序列", "批次", "检测", "检验", "分析", "试剂", "抗体", "抗原", "蛋白", "基因", "分子", "细胞", "标记", "染色", "流式", "细胞术", "免疫", "组化", "病理", "仪器", "设备", "软件", "系统", "平台", "程序", "Expert", "Cy<PERSON>", "HLA", "CD", "DR"], "sources": ["WeChatRecognizer"]}, "qq": {"name": "qq", "displayName": "QQ号", "description": "QQ号码识别", "category": "contextual", "enabled": true, "positiveKeywords": ["QQ", "QQ号", "qq", "QQ号码", "QQ账号", "qq号", "qq号码", "联系QQ", "加QQ", "QQ联系", "我的QQ", "QQ号是", "qq号是"], "negativeKeywords": ["编号", "序列号", "病案号", "检验号", "患者号", "床位号", "电话", "手机", "身份证", "银行卡", "医保卡"], "sources": ["QQNumberRecognizer"]}, "email": {"name": "email", "displayName": "邮箱地址", "description": "邮箱地址识别", "category": "nlp_recognizer", "enabled": true, "sources": ["EMAIL_ADDRESS"]}}}, "locationTime": {"name": "locationTime", "displayName": "位置时间类", "recognizers": {"licensePlate": {"name": "licensePlate", "displayName": "车牌号", "description": "中国车牌号码识别", "category": "nlp_recognizer", "enabled": true, "sources": ["LicensePlateRecognizer"]}, "completeAddress": {"name": "completeAddress", "displayName": "完整地址", "description": "完整地址（省市区+详细）识别", "category": "nlp_recognizer", "enabled": true, "sources": ["CompleteAddressRecognizer"]}, "gpsCoordinates": {"name": "gpsCoordinates", "displayName": "GPS坐标", "description": "GPS坐标（经纬度）识别", "category": "contextual", "enabled": true, "positiveKeywords": ["坐标", "经纬度", "GPS", "位置", "地理位置", "定位", "经度", "纬度", "地标", "导航", "地图", "位置信息", "GPS坐标", "地理坐标", "坐标点", "位置坐标"], "negativeKeywords": ["数字", "编号", "代码", "序列", "价格", "金额", "电话", "手机", "身份证", "银行卡", "时间", "日期", "版本", "型号", "规格", "参数", "配置"], "sources": ["GPSCoordinateRecognizer"]}, "locations": {"name": "locations", "displayName": "地理位置", "description": "地理位置信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["LOCATION"]}, "ipAddresses": {"name": "ipAddresses", "displayName": "IP地址", "description": "IP地址识别", "category": "nlp_recognizer", "enabled": true, "sources": ["IP_ADDRESS"]}, "dateTime": {"name": "dateTime", "displayName": "日期时间", "description": "日期时间识别", "category": "nlp_recognizer", "enabled": true, "sources": ["DATE_TIME"]}}}, "medicalInformation": {"name": "medicalInformation", "displayName": "医疗信息类", "recognizers": {"medicalNumbers": {"name": "medicalNumbers", "displayName": "医疗编号", "description": "病案号、检验号等医疗编号识别", "category": "contextual", "enabled": true, "positiveKeywords": ["病案号", "病历号", "医疗记录", "病案", "病历", "门诊号", "住院号", "就诊号", "挂号", "预约号", "诊疗号", "标本编号", "检验编号", "化验编号", "样本编号", "标本号", "检验号", "化验号", "样本号", "送检编号", "报告编号", "实验编号", "测试编号", "卡号", "医保卡", "就诊卡", "健康卡", "医疗卡", "诊疗卡", "处方号", "药品编号", "床位号", "科室编号", "医师编号", "申请编号", "条码号", "追踪号", "流水号", "登记号", "受理号", "编号", "号码", "代码", "ID", "识别码", "标识符", "设备编号", "序列号", "设备号", "机器编号", "仪器编号", "设备序列号", "医疗设备", "检测设备", "监护设备", "治疗设备", "诊断设备"], "negativeKeywords": ["电话", "手机", "身份证", "银行卡", "QQ", "微信", "车牌", "驾照", "护照", "工号", "学号", "会员号", "年", "月", "日", "时", "分", "秒", "点", "元", "角", "分", "价格", "金额", "费用", "收费", "省", "市", "区", "县", "街道", "路", "号", "邮编", "版本", "型号", "规格", "参数", "配置", "数量", "重量"], "sources": ["MedicalNumberRecognizer"]}, "organizations": {"name": "organizations", "displayName": "医疗机构", "description": "医疗机构名称识别", "category": "contextual", "enabled": true, "positiveKeywords": null, "negativeKeywords": ["的医生", "的护士", "的专家", "的主任", "的科室", "药前"], "sources": ["OrganizationRecognizer"]}, "sponsor": {"name": "sponsor", "displayName": "申办方", "description": "申办方名称信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["SponsorRecognizer"]}, "structuredField": {"name": "structuredField", "displayName": "结构化字段", "description": "\"字段:值\"格式的结构化信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["StructuredFieldRecognizer"]}}}, "privacyPersonal": {"name": "privacyPersonal", "displayName": "隐私个人类", "recognizers": {"persons": {"name": "persons", "displayName": "人名", "description": "人名识别", "category": "nlp_recognizer", "enabled": true, "sources": ["PERSON"]}, "gender": {"name": "gender", "displayName": "性别", "description": "性别信息识别", "category": "contextual", "enabled": true, "positiveKeywords": ["性别", "患者", "病员", "病人"], "negativeKeywords": [], "sources": ["GenderRecognizer"]}, "age": {"name": "age", "displayName": "年龄", "description": "年龄信息识别", "category": "contextual", "enabled": true, "positiveKeywords": null, "negativeKeywords": ["工龄", "教龄", "司龄", "党龄", "军龄", "学龄", "使用年限", "保质期", "有效期", "存储期", "保修期", "历史", "年代", "时期", "时代", "世纪", "年份", "建立", "成立", "创建", "开业", "营业", "运营", "价格", "金额", "费用", "收费", "成本", "预算", "数量", "重量", "长度", "高度", "面积", "体积"], "sources": ["AgeRecognizer"]}, "ethnicity": {"name": "ethnicity", "displayName": "民族", "description": "56个民族信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["EthnicityRecognizer"]}, "nationality": {"name": "nationality", "displayName": "民族国籍", "description": "民族/国籍信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["NRP"]}, "educationLevel": {"name": "educationLevel", "displayName": "学历学位", "description": "学历学位信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["EducationLevelRecognizer"]}, "privacyInfo": {"name": "privacyInfo", "displayName": "隐私信息", "description": "婚姻状况、宗教信仰等隐私信息识别", "category": "nlp_recognizer", "enabled": true, "sources": ["PrivacyInfoRecognizer"]}, "url": {"name": "url", "displayName": "URL地址", "description": "严格URL识别（HTTP/FTP/邮件）", "category": "nlp_recognizer", "enabled": true, "sources": ["URLRecognizer"]}}}}, "filters": {"MEDICAL_TEST_ITEMS": {"name": "MEDICAL_TEST_ITEMS", "displayName": "医学检验项目", "description": "排除误识别的检验项目", "values": ["葡萄糖", "蛋白", "酮体", "胆红素", "白细胞", "红细胞", "细菌", "酵母", "比重", "酸度", "潜血", "精子", "上皮细胞", "粘液丝", "管型", "结晶", "尿胆原", "亚硝酸盐", "白细胞酯酶", "酵母样菌", "完整红细胞", "血红蛋白", "血小板", "中性粒细胞", "淋巴细胞", "单核细胞", "嗜酸性粒细胞", "血糖", "胆固醇", "甘油三酯", "肌酐", "尿素氮", "尿酸", "总蛋白", "白蛋白", "谷丙转氨酶", "谷草转氨酶", "碱性磷酸酶", "乳酸脱氢酶", "钠", "钾", "氯", "钙", "磷", "镁", "铁", "锌", "铜", "硒", "维生素", "叶酸", "维生素B12", "维生素D", "甲状腺激素", "胰岛素", "免疫球蛋白", "补体", "抗体", "抗原", "细胞因子", "白介素", "大肠杆菌", "金黄色葡萄球菌", "链球菌", "肺炎球菌", "真菌", "病毒", "活化部分凝血活酶时间", "凝血酶原时间", "纤维蛋白原", "谷氨酰胺转移酶", "血镁浓度", "血钠浓度"]}, "MEDICAL_TERMS": {"name": "MEDICAL_TERMS", "displayName": "医学术语", "description": "排除医学专业术语", "values": ["心脏", "肝脏", "肾脏", "肺", "脾脏", "胰腺", "胃", "肠", "膀胱", "前列腺", "子宫", "卵巢", "乳腺", "甲状腺", "肾上腺", "垂体", "大脑", "小脑", "脊髓", "上皮细胞", "内皮细胞", "间质细胞", "纤维细胞", "脂肪细胞", "肌细胞", "神经细胞", "胶质细胞", "干细胞", "免疫细胞", "巨噬细胞", "树突细胞", "炎症", "感染", "肿瘤", "癌症", "良性", "恶性", "转移", "浸润", "坏死", "纤维化", "硬化", "增生", "萎缩", "变性", "水肿", "出血", "血栓", "镜检", "培养", "染色", "免疫组化", "分子诊断", "基因检测", "流式细胞术", "电泳", "色谱", "质谱", "酶联免疫", "放射免疫", "化学发光", "荧光", "阴性", "阳性", "正常", "异常", "升高", "降低", "未见", "少量", "中量", "大量"]}, "DRUG_NAMES": {"name": "DRUG_NAMES", "displayName": "药物名称", "description": "排除药物名称误识别", "values": ["奥布替尼", "伊布替尼", "泽布替尼", "阿卡替尼", "利妥昔单抗", "奥妥珠单抗", "奥法妥木单抗", "贝伐珠单抗", "曲妥珠单抗", "帕妥珠单抗", "阿德妥珠单抗", "来那度胺", "泊马度胺", "沙利度胺", "阿糖胞苷", "地西他滨", "阿扎胞苷", "氟达拉滨", "苯达莫司汀", "环磷酰胺", "异环磷酰胺", "表柔比星", "阿霉素", "柔红霉素", "伊达比星", "长春新碱", "长春花碱", "长春瑞滨", "紫杉醇", "多西他赛", "白蛋白紫杉醇", "维奈克拉", "伊马替尼", "达沙替尼", "尼洛替尼", "博舒替尼", "普纳替尼", "舒尼替尼", "索拉非尼", "泼尼松", "甲泼尼龙", "地塞米松", "氢化可的松"]}, "DISEASE_NAMES": {"name": "DISEASE_NAMES", "displayName": "疾病名称", "description": "排除疾病名称误识别", "values": ["边缘区淋巴瘤", "弥漫大B细胞淋巴瘤", "滤泡性淋巴瘤", "套细胞淋巴瘤", "霍奇金淋巴瘤", "非霍奇金淋巴瘤", "MALT淋巴瘤", "DLBCL", "FL", "MCL", "HL", "NHL", "急性髓系白血病", "急性淋巴细胞白血病", "慢性髓系白血病", "慢性淋巴细胞白血病", "急性早幼粒细胞白血病", "AML", "ALL", "CML", "CLL", "APL", "多发性骨髓瘤", "华氏巨球蛋白血症", "骨髓增生异常综合征", "骨髓纤维化", "真性红细胞增多症", "原发性血小板增多症", "MM", "WM", "MDS", "PMF", "PV", "ET", "边缘区", "华氏", "Castleman病", "噬血细胞综合征"]}, "NEUROLOGICAL_SIGNS": {"name": "NEUROLOGICAL_SIGNS", "displayName": "神经学体征", "description": "排除神经学体征误识别", "values": ["<PERSON>征", "<PERSON><PERSON><PERSON>征", "Brudzinski征", "<PERSON><PERSON>征", "Chaddock征", "Oppenheim征", "<PERSON>征", "<PERSON><PERSON><PERSON><PERSON>征", "<PERSON>oli<PERSON>征", "Mendel征", "<PERSON><PERSON>征"]}, "ENGLISH_MEDICAL_TERMS": {"name": "ENGLISH_MEDICAL_TERMS", "displayName": "英文医学术语", "description": "排除英文医学术语", "values": ["<PERSON><PERSON><PERSON>", "Microscope", "Centrifuge", "Incubator", "Spectrophotometer", "Chromatography", "Electrophoresis", "PCR", "ELISA", "Flow", "Cytometry", "Result", "Analysis", "Examination", "Treatment", "Therapy", "Medicine"]}, "MEDICAL_ABBREVIATIONS": {"name": "MEDICAL_ABBREVIATIONS", "displayName": "医学缩写", "description": "排除医学缩写误识别", "values": ["CBC", "CRP", "ESR", "ALT", "AST", "BUN", "Cr", "UA", "TC", "TG", "HDL", "LDL", "FBG", "HbA1c", "TSH", "T3", "T4", "PSA", "AFP", "CEA"]}, "OTHER_NAMES": {"name": "OTHER_NAMES", "displayName": "其他名称", "description": "其他专业术语过滤", "values": ["参考区间", "参考区"]}}}