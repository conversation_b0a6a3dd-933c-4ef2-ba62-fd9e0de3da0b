"""
HIPAA医疗数据脱敏服务 - 简化版FastAPI应用

提供医疗文本敏感信息检测和脱敏功能的RESTful API服务。
"""

import time
import threading
import logging
import uuid
import os
import sys
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_anonymizer import MedicalAnonymizer
from api.config import config

# 配置日志
log_level_map = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL
}
log_level = log_level_map.get(config.LOG_LEVEL.upper(), logging.INFO)

logging.basicConfig(
    level=log_level,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=config.APP_NAME,
    version=config.APP_VERSION,
    description=config.APP_DESCRIPTION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.ALLOW_ORIGINS,
    allow_credentials=config.ALLOW_CREDENTIALS,
    allow_methods=config.ALLOW_METHODS,
    allow_headers=config.ALLOW_HEADERS,
)

# 全局变量
_anonymizer: Optional[MedicalAnonymizer] = None
_request_count = 0
_request_lock = threading.Lock()
_start_time = time.time()

# 固定配置
MAX_REQUEST_COUNT = 10000000  # 请求计数器最大值


def get_anonymizer() -> MedicalAnonymizer:
    """获取脱敏器实例（单例模式）"""
    global _anonymizer
    if _anonymizer is None:
        logger.info("正在初始化医疗脱敏器...")
        _anonymizer = MedicalAnonymizer(
            enable_logging=True,           # 启用详细日志（自动启用两层过滤器统计）
            anonymization_strategy="mask"  # 固定使用mask策略
        )
        logger.info("医疗脱敏器初始化完成")

        # 测试脱敏器是否正确配置
        test_result = _anonymizer.detect_sensitive_info("测试张三")
        logger.info(f"脱敏器测试结果: {test_result['anonymized_text']}")
    return _anonymizer


def increment_request_count() -> int:
    """增加请求计数（线程安全）"""
    global _request_count
    with _request_lock:
        _request_count += 1
        if _request_count >= MAX_REQUEST_COUNT:
            logger.info(f"请求计数器达到最大值 {MAX_REQUEST_COUNT}，重置为0")
            _request_count = 0
        return _request_count


def get_request_count() -> int:
    """获取当前请求计数"""
    global _request_count
    with _request_lock:
        return _request_count


def validate_text_input(text: str) -> None:
    """验证文本输入"""
    if not text or not text.strip():
        raise ValueError("文本内容不能为空")

    if len(text) > config.MAX_TEXT_LENGTH:
        raise ValueError(f"文本长度超过限制（最大{config.MAX_TEXT_LENGTH}字符）")


def generate_request_id() -> str:
    """生成唯一的请求ID"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"req_{timestamp}_{unique_id}"


@app.get("/", summary="服务根路径", description="返回服务基本信息")
async def root():
    """服务根路径"""
    return {
        "service": config.APP_NAME,
        "version": config.APP_VERSION,
        "status": "running",
        "docs_url": "/docs",
        "endpoints": {
            "deidentify": "POST /deidentify",
            "stats": "GET /stats",
            "health": "GET /health"
        }
    }


@app.post("/deidentify", summary="医疗文本脱敏", description="对医疗文本进行敏感信息检测和脱敏处理")
async def deidentify_text(request_data: Dict[str, Any]):
    """
    医疗文本脱敏接口

    请求体格式:
    {
        "text": "待脱敏的医疗文本"
    }

    响应格式:
    {
        "sensitive_words": [
            {
                "text": "敏感文本",
                "entity_type": "实体类型",
                "start": 起始位置,
                "end": 结束位置,
                "confidence": 置信度
            }
        ],
        "deidentified_text": "脱敏后的文本",
        "processing_time": 处理耗时,
        "total_entities": 敏感实体总数,
        # "request_id": "请求ID"
    }
    """
    # request_id = generate_request_id()
    start_time = time.time()

    try:
        # 增加请求计数
        increment_request_count()

        # 获取请求参数
        text = request_data.get("text")
        if not text:
            raise ValueError("缺少必需的参数: text")

        # 验证输入
        validate_text_input(text)

        logger.info(f"开始处理脱敏请求，接收文本: {text}")
        logger.info(f"文本长度: {len(text)}字符")

        # 获取脱敏器（固定使用mask策略）
        anonymizer = get_anonymizer()

        # 执行脱敏检测
        result = anonymizer.detect_sensitive_info(
            text=text,
        )

        processing_time = time.time() - start_time

        # 提取纯敏感词列表
        sensitive_words_only = [entity['text'] for entity in result['entities']]
        logger.info(f"识别到的纯敏感词列表: {sensitive_words_only}")
        # logger.info(f"脱敏后文本: {result['anonymized_text']}")
        logger.info(f"处理耗时: {processing_time:.3f}秒")

        # 格式化响应
        response = {
            "sensitive_words": result['entities'],
            "deidentified_text": result['anonymized_text'],
            "filtered_entities": result.get('filtered_entities', []),
            "processing_time": round(processing_time, 3),
            "total_entities": len(result['entities']),
            # "request_id": request_id,
            "timestamp": datetime.now().isoformat()
        }

        # logger.info(
        #     # f"脱敏请求 {request_id} 处理完成，"
        #     # f"检测到 {len(result['entities'])} 个敏感实体，"
        #     # f"耗时 {processing_time:.3f}s"
        # )

        return response

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "ValidationError",
                "message": str(e),
            }
        )

    except Exception as e:
        processing_time = time.time() - start_time
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "ProcessingError",
                "message": "脱敏处理失败",
                "processing_time": round(processing_time, 3)
            }
        )


@app.get("/stats", summary="获取系统统计信息", description="获取系统使用统计信息")
async def get_stats():
    """
    获取系统统计信息

    响应格式:
    {
        "total_requests": 总请求次数,
        "uptime_seconds": 运行时间(秒),
        "version": 服务版本
    }
    """
    try:
        uptime = time.time() - _start_time
        request_count = get_request_count()

        response = {
            "total_requests": request_count,
            "uptime_seconds": round(uptime, 1),
            "version": config.APP_VERSION,
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"返回统计信息: 总请求数={request_count}, 运行时间={uptime:.1f}秒")

        return response

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "InternalServerError",
                "message": "获取统计信息失败"
            }
        )


@app.get("/health", summary="健康检查", description="检查服务健康状态")
async def health_check():
    """
    健康检查接口

    响应格式:
    {
        "status": "healthy|unhealthy",
        "version": 服务版本,
        "uptime_seconds": 运行时间,
        "checks": {
            "anonymizer": "healthy|unhealthy",
            "service": "healthy|unhealthy"
        }
    }
    """
    try:
        uptime = time.time() - _start_time
        checks = {"service": "healthy"}

        # 检查脱敏器状态
        try:
            anonymizer = get_anonymizer()
            # 简单测试
            _ = anonymizer.detect_sensitive_info("测试文本")
            checks["anonymizer"] = "healthy"
        except Exception as e:
            logger.warning(f"脱敏器健康检查失败: {str(e)}")
            checks["anonymizer"] = "unhealthy"

        # 确定整体状态
        overall_status = "healthy" if all(status == "healthy" for status in checks.values()) else "unhealthy"

        response = {
            "status": overall_status,
            "version": config.APP_VERSION,
            "uptime_seconds": round(uptime, 1),
            "checks": checks,
            "timestamp": datetime.now().isoformat()
        }

        return response

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "version": config.APP_VERSION,
            "uptime_seconds": round(time.time() - _start_time, 1),
            "checks": {"service": "unhealthy"},
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info(f"正在启动 {config.APP_NAME} v{config.APP_VERSION}")

    try:
        # 预热脱敏器
        _ = get_anonymizer()
        logger.info("脱敏器预热完成")

        logger.info(f"服务启动成功，监听地址: http://127.0.0.1:{config.PORT}")
        logger.info(f"API文档地址: http://127.0.0.1:{config.PORT}/docs")

    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        raise


# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("正在关闭服务...")
    uptime = time.time() - _start_time
    request_count = get_request_count()
    logger.info(f"服务运行时间: {uptime:.1f}秒，总处理请求: {request_count}次")
    logger.info("服务已关闭")


# 主函数
def main():
    """启动服务的主函数"""
    logger.info(f"启动配置: HOST=127.0.0.1, PORT={config.PORT}, DEBUG={config.DEBUG}")

    uvicorn.run(
        "main:app",
        host="127.0.0.1",  # 固定使用本地地址
        port=config.PORT,
        reload=config.DEBUG,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
