"""
简化版配置文件

包含FastAPI服务的基本配置参数。
"""

import os


class SimpleConfig:
    """简化的配置类"""

    # 服务基本信息
    APP_NAME = "HIPAA医疗数据脱敏服务"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "基于FastAPI的医疗文本敏感信息检测与脱敏服务"

    # 服务器配置
    PORT = int(os.getenv("PORT", 50505))
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"

    # 请求限制配置
    MAX_TEXT_LENGTH = 500000  # 最大文本长度（字符数）



    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    # CORS配置
    ALLOW_ORIGINS = ["*"]
    ALLOW_CREDENTIALS = True
    ALLOW_METHODS = ["*"]
    ALLOW_HEADERS = ["*"]


# 全局配置实例
config = SimpleConfig()
