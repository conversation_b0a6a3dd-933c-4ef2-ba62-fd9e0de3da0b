# # #!/usr/bin/env python3
# # # -*- coding: utf-8 -*-
# #
# # # 你的其他代码从这里开始
# # print("你好，世界！")
# # import json
# #
# # # 假设你的多行文本存储在这个变量中
# # my_text = """您好！首先感谢您有意参加本项临床研究。本研究是为了评估奥布替尼联合来那 度胺和利妥昔单抗（^2}）对比安慰剂联合R^2治疗复发/难治性边缘区淋巴瘤患者的疗效 和安全性。 在您决定是否参加之前，请仔细阅读下面的信息，如果您愿意也可以和您的亲人 或朋友商量。如果您有任何不清楚的地方，请直接询问您的医生。 如果您了解了本研究、并自愿参加，您需要签署本研究的知情同意书。签署知情 同意书不会改变您的合法权益，仅表示您已经理解了这些信息并自愿参加本研究。 【研究背景】 边缘区淋巴瘤（MZL）是一组异质性的惰性非霍奇金淋巴瘤，约占所有非霍奇金 淋巴瘤的7~8%。边缘区淋巴瘤通常对各种免疫化疗敏感，但常规疗法仍无法将其治愈。 大多数MZL患者最终会复发，疾病的持续存在或反复复发导致严重的并发症和死亡。 复发/难治性MZL患者，目前仍缺乏公认的标准治疗方法，尤其是缺乏优效的靶向药物。 近年来，布鲁顿氏酪氨酸激酶（BTK）抑制剂已在B细胞恶性疾病中获得了广泛的使 用，在国内已获批用于治疗慢性淋巴细胞白血病/小淋巴细胞淋巴瘤和华氏巨球蛋白血 症。有研究证明，奥布替尼、伊布替尼和泽布替尼治疗复发/难治性MZL患者获得了一 定的疗效，其中奥布替尼已国内获得国家药品监督管理局（NMPA）的批准，用于既 往至少接受过一种治疗的边缘区淋巴瘤（MZL）患者的治疗；泽布替尼也已获得国外 <中国医科大学附属第一医院>专用版，版本号：V2.0，版本日期：2024年03月15日： <ICP-CL-00123>知情同意书，版本号：V2.0，版本日期：2024年03月08日 第1页，共18页 SOP编号：CL-PM-SOP-003a 版本：4.0 8 扫描全能王创建 INFO:__main__:OCR API调用耗时: 1.02秒 INFO:__main__:正在处理文件: 8797-医大一-13001-访视病历(1).pdf (类型: pdf) INFO:__main__:开始处理PDF文件: D:\data\脱敏用\36张\8797-医大一-13001-访视病历(1).pdf，共13页 📄 OCR识别结果:  ------------------------------ a 日 INNOCARE 知情同意书 ① 0 0 8 知情同意书签字页 研究参与者声明： 我已阅读并理解了本知情同意书的信息，并就本研究和研究医生进行了讨论。 我已经了解研究所涉及的所有程序，包括任何目前研究人员所能够了解到的、已 知或预期的不便、风险、不适或潜在的副作用，以及它们各自所代表的临床含义。 我了解并且同意，如果我参与此项研究，就意味着我允许研究人员将能够查看我 的医疗记录。 我自愿选择参加本研究，我也了解我可以随时退出。该决定不会影响我未来的治 疗或其他受益。 我还明白，本研究是严格保密的。 我已获知，我将收到一份已签名并注明日期的本同意书副本。 我已经经过充分考虑，我所有的问题都得到了满意的回答。在此，我同意参加 <ICP-CL-00123>研究。 我明白，我可以为如下选项做出选择，勾选“是”表示我同意，勾选“否”表示 我不同意。 我愿意参加药代动力学采血和研究。 晋风 □否 通过以下签字，我允许收集我的个人信息作为本临床研究的一部分： 仅使用我的研究参与者编号识别；申办者及其授权代表出于研究方案中描述的目 的进行审查、处理和转移；由适当授权机构进行审查和稽查；发表并发送给中国或其 他国家/地区的监管机构或医疗保险公司；和如果需要，转移至数据保护法可能不太严 格的国家。 工 如本隐私声明所述，可以收集、使用和存档以进行研究；在法规允许的范围内， 转移至中国境外的其他公司并与之共享；转移至第三方和其他公司并与之共享。 彩萍 魏萍 2024年106月121日14时5分 研究参与者正楷/印刷体姓名 签名 日期/时间（24小时制） __年___月_日_时__分 监护人正楷/印刷体姓名 签名 日期/时间（24小时制） （研究参与者为无民事行为能力或不完全民事行为能力） 监护人与研究参与者的关系（请在方框内打钩）：口父母口配偶口子女口其他 <中国医科大学附属第一医院>专用版，版本号：V2.0，版本日期：2024年03月15日； <ICP-CL-00123>知情同意书，版本号：V2.0，版本日期：2024年03月08日 第17页，共18页 SOP编号：CL-PM-SOP-003a 版本：4.0 C 扫描全能王创建 INFO:__main__:PDF转换完成，生成13张图片 INFO:__main__:OCR API调用耗时: 1.52秒"""
# #
# # # 创建一个Python字典
# # data = {
# #     "text": my_text
# # }
# #
# # # 使用 json.dumps() 将字典转换为正确的JSON字符串
# # # 这会自动处理所有的换行符(\n)、引号(\")等
# # json_string = json.dumps(data, ensure_ascii=False, indent=2)
# #
# # # 打印结果
# # print(json_string)
#
#
# def print_cleaned_lines(filename):
#     """
#     读取文本文件，跳过包含特定关键字的行，清理其余行，
#     并直接将干净的文本内容逐行输出到终端。
#
#     Args:
#         filename: 要读取的文本文件的名称。
#     """
#     try:
#         with open(filename, 'r', encoding='utf-8') as f:
#             lines = f.readlines()
#         keywords_to_skip = ['医院',"药业", "生物科技", "制药",'生物科技','医疗中心','医药研究所','大学','公司']
#         # keywords_to_skip = ['医院',"药业", "生物科技", "制药",'生物科技','生物医药','医疗中心','医药研究所','信息技术','医药研究','医药','集团','大学','公司']
#
#         # 遍历文件中的每一行
#         for line in lines:
#             # 1. 检查当前行是否包含任何关键字，如果包含则跳过
#             if any(keyword in line for keyword in keywords_to_skip):
#                 continue
#
#             # 2. 强力清理：直接替换掉所有不需要的字符
#             #    - .replace('"', '')  -> 移除所有双引号
#             #    - .replace('\\', '') -> 移除所有反斜杠
#             #    - .replace(',', '')  -> 移除所有逗号
#             #    - .strip()         -> 移除首尾的空格和换行符
#             cleaned_line = line.replace('"', '').replace('\\', '').replace(',', '').replace('\'', '').strip()
#
#             # 3. 如果清理后还有内容，就直接打印出来
#             if cleaned_line:
#                 print(f"\'{cleaned_line}\',")
#
#     except FileNotFoundError:
#         print(f"错误：找不到文件 '{filename}'")
#     except Exception as e:
#         print(f"发生错误：{e}")
#
# if __name__ == "__main__":
#     # 直接调用函数并传入指定的文件路径
#     file_to_read = r"D:\下载\a.txt"
#     print_cleaned_lines(file_to_read)

print("""检查结果回报：\n2024-10-18GCP空腹血糖：GCP氨基末端脑钠肽前体检测：检查结果正常\n2024-10-18GCP心功能常规：肌酸激酶 17.40U/L，GCP电解质：镁1.10mol/L，GCP大便常规+隐血\n潜血（单克隆法）阳性（+)，GCP凝血功能+D二聚体：纤维蛋白原浓度5.10g/L，均为NCS，余值均\n常。\n2024-10-18 GCP尿沉渣:隐血弱阳性(±)，白细胞 阳性(+)，红细胞19.00Cel1s/U1,白细胞42\n00Cells/UI，（CS，尿路感染AEI级持续）\n5.80mmol/L\n均为CS，高血脂症病史有关\n2024-10-18 GCP肾功能：尿素13.51mmol/L，肌酐121.Oumol1/L，（CS，与肾功能不全病史有\n关）\n2024-10-18GCP心功能常规：乳酸脱氢酶 412.6OU/L，GCP肝功能酶学检查：乳酸脱氢酶 412.\n60U/L，谷氨酰氨基转肽酶 430.00U/L，白蛋白 37.7g/L，（CS，与肿瘤疾病相关）GCP肝功能常\n规：丙氨酸氨基转移酶*88.20U/L，（CS，肝功能损伤AEI级，肝功能损伤AEII级于2024.10.18结束转\n肝功能损伤AEI级开始时间2024.10.18，目前持续，与注射液盐酸托泊替康可能有关，与研究流程\n无关。予以对研究药物采取的措施为继续用药，予以护肝药对症治疗）""")