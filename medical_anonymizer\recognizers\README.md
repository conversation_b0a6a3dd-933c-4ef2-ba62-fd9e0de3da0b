# 医疗数据脱敏系统 - 识别器模块

## 📋 概述

本模块是医疗数据脱敏系统的核心组件，包含29个专业识别器，采用三层架构设计，支持动态配置管理，能够准确识别和脱敏各类医疗敏感信息。

## 🏗️ 架构设计

### 三层识别器架构

```
┌─────────────────────────────────────────────────────────────┐
│                    NLP识别器层 (Presidio)                    │
│  基于机器学习的高级识别 - 人名、地名、医疗执照等              │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   上下文识别器层 (12个)                      │
│  基于关键词和上下文的智能识别 - 证件、联系方式、医疗信息等     │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   纯规则识别器层 (8个)                       │
│  基于正则表达式的固定模式识别 - 身份证、车牌、结构化字段等     │
└─────────────────────────────────────────────────────────────┘
```

### 核心文件结构

```
medical_anonymizer/recognizers/
├── __init__.py                 # 模块初始化和文档
├── pattern_matchers.py         # 纯规则识别器层 (8个识别器)
├── contextual_analyzers.py     # 上下文识别器层 (12个识别器)
├── unified_registry.py         # 统一注册器和管理
└── patterns.py                 # 识别器配置和模式定义
```

## 🔧 动态配置系统

### 配置文件驱动

所有识别器的行为都由 `medical_anonymizer/config/recognizer_config.json` 配置文件控制：

```json
{
  "recognizers": {
    "identityDocuments": {
      "recognizers": {
        "chineseId": {
          "enabled": true,
          "displayName": "中国身份证",
          "category": "nlp_recognizer"
        },
        "certificates": {
          "enabled": true,
          "displayName": "各类证件",
          "category": "contextual",
          "positiveKeywords": ["护照", "驾照", "军官证"]
        }
      }
    }
  }
}
```

### 动态操作支持

```python
from medical_anonymizer.config import get_config_manager

manager = get_config_manager()

# 运行时启用/禁用识别器
manager.enable_recognizer("WeChatRecognizer")
manager.disable_recognizer("StructuredFieldRecognizer")

# 动态修改关键词
manager.update_recognizer_config("certificates", {
    "positiveKeywords": ["护照", "驾照", "军官证", "学生证"]
})

# 配置热重载
manager.reload_config()

# 获取识别器状态
status = manager.get_recognizer_status()
```

## 📊 识别器详细清单

### 🆔 身份证件类 (5个识别器)

| 识别器 | 类型 | 功能描述 | 配置状态 |
|--------|------|----------|----------|
| **ChineseIDRecognizer** | 规则识别器 | 18位中国身份证号码识别 | ✅ 启用 |
| **CertificateRecognizer** | 上下文识别器 | 护照、驾照、军官证等各类证件 | ✅ 启用 |
| **BankCardRecognizer** | 上下文识别器 | 银行卡号识别（16-19位） | ✅ 启用 |
| **MedicalInsuranceRecognizer** | 上下文识别器 | 医保社保卡号识别 | ✅ 启用 |
| **CREDIT_CARD** | Presidio内置 | 信用卡号识别（国际标准） | ✅ 启用 |

### 📞 通信联系类 (4个识别器)

| 识别器 | 类型 | 功能描述 | 配置状态 |
|--------|------|----------|----------|
| **PhoneRecognizer** | 上下文识别器 | 手机号、固定电话识别 | ✅ 启用 |
| **WeChatRecognizer** | 上下文识别器 | 微信号识别 | ✅ 启用 |
| **QQNumberRecognizer** | 上下文识别器 | QQ号码识别 | ✅ 启用 |
| **EMAIL** | Presidio内置 | 邮箱地址识别 | ✅ 启用 |

### 📍 位置时间类 (4个识别器)

| 识别器 | 类型 | 功能描述 | 配置状态 |
|--------|------|----------|----------|
| **GPSRecognizer** | 上下文识别器 | GPS坐标识别 | ✅ 启用 |
| **LocationRecognizer** | 上下文识别器 | 地理位置、地名识别 | ✅ 启用 |
| **IP_ADDRESS** | Presidio内置 | IP地址识别 | ✅ 启用 |
| **DATE_TIME** | Presidio内置 | 日期时间识别 | ✅ 启用 |

### 🏥 医疗信息类 (4个识别器)

| 识别器 | 类型 | 功能描述 | 配置状态 |
|--------|------|----------|----------|
| **MedicalNumberRecognizer** | 上下文识别器 | 病案号、检验号等医疗编号 | ✅ 启用 |
| **StructuredFieldRecognizer** | 规则识别器 | "字段:值"格式的结构化信息 | ⚠️ 可配置 |
| **MEDICAL_LICENSE** | Presidio内置 | 医疗执照号码 | ✅ 启用 |
| **OrganizationRecognizer** | 上下文识别器 | 医疗机构名称（医院、CRO等） | ✅ 启用 |

### 👤 隐私个人类 (12个识别器)

| 识别器 | 类型 | 功能描述 | 配置状态 |
|--------|------|----------|----------|
| **PERSON** | Presidio内置 | 人名识别 | ✅ 启用 |
| **GenderRecognizer** | 上下文识别器 | 性别信息识别 | ✅ 启用 |
| **AgeRecognizer** | 上下文识别器 | 年龄信息识别 | ✅ 启用 |
| **EthnicityRecognizer** | 规则识别器 | 民族信息识别 | ✅ 启用 |
| **EducationLevelRecognizer** | 规则识别器 | 学历学位识别 | ✅ 启用 |
| **PrivacyInfoRecognizer** | 规则识别器 | 隐私敏感信息识别 | ✅ 启用 |
| **CompleteAddressRecognizer** | 规则识别器 | 完整地址识别 | ✅ 启用 |
| **LicensePlateRecognizer** | 规则识别器 | 车牌号识别 | ✅ 启用 |
| **URLRecognizer** | 规则识别器 | URL链接识别 | ✅ 启用 |
| **SponsorRecognizer** | 规则识别器 | 申办方名称识别 | ✅ 启用 |
| **LOCATION** | Presidio内置 | 地名识别 | ✅ 启用 |
| **NationalityRecognizer** | 上下文识别器 | 国籍信息识别 | ✅ 启用 |

## 🔍 识别器工作原理

### 纯规则识别器
- **工作方式**: 基于预定义的正则表达式模式
- **优势**: 高精度、低误报、高性能
- **适用场景**: 格式固定的敏感信息（身份证号、车牌号等）

### 上下文识别器
- **工作方式**: 基于关键词和上下文分析
- **优势**: 灵活性高、支持动态配置
- **适用场景**: 需要上下文判断的敏感信息（证件、联系方式等）

### NLP识别器
- **工作方式**: 基于机器学习的命名实体识别
- **优势**: 语义理解能力强、适应性好
- **适用场景**: 复杂的语义理解场景（人名、地名等）

---

**版本**: 2.0.0  
**维护者**: 张崇文
